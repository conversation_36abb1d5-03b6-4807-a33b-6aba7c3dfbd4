import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { IssuerFilter } from 'shared/table/filters/issuer-filter';
import { SiteAreaTableFilter } from 'shared/table/filters/site-area-table-filter';
import { DataResultAuthorizations } from 'types/Authorization';
import { CentralServerService } from '../../../services/central-server.service';
import { ComponentService } from '../../../services/component.service';
import { DialogService } from '../../../services/dialog.service';
import { MessageService } from '../../../services/message.service';
import { SpinnerService } from '../../../services/spinner.service';
import { AppDatePipe } from '../../../shared/formatters/app-date.pipe';
import { TableAutoRefreshAction } from '../../../shared/table/actions/table-auto-refresh-action';
import { TableRefreshAction } from '../../../shared/table/actions/table-refresh-action';
import { TableDataSource } from '../../../shared/table/table-data-source';
import { DataResult } from '../../../types/DataResult';
import { TableActionDef, TableColumnDef, TableDef, TableFilterDef } from '../../../types/Table';
import { Utils } from '../../../utils/Utils';
import {
  ChargingStationSchedules,
  ChargingStationSchedulesButtonAction,
} from 'types/ChargingStationSchedules';
import {
  TableCreateChargingStationSchedulesAction,
  TableCreateChargingStationSchedulesActionDef,
} from 'shared/table/actions/charging-stations/table-create-charging-station-schedules-action';
import { ChargingStationsChargingStationSchedulesDialogComponent } from './addEdit/charging-stations-schedules.dialog.component';
import { TableEditChargingStationSchedulesAction } from 'shared/table/actions/charging-stations/table-edit-charging-station-schedules-action';
import {
  TableDeleteChargingStationSchedulesAction,
  TableDeleteChargingStationSchedulesActionDef,
} from 'shared/table/actions/charging-stations/table-delete-charging-station-schedules-action';
import { DateRangeTableFilter } from 'shared/table/filters/date-range-table-filter';
import { ChargingStationFilter } from 'pages/payment-settlement/filters/charging-stations-filter';
import { UserStatusFormatterComponent } from 'pages/users/formatters/user-status-formatter.component';

@Injectable()
export class EvseManagementTableDataSource extends TableDataSource<ChargingStationSchedules> {
  private dateRangeFilter: TableFilterDef;
  private createAction = new TableCreateChargingStationSchedulesAction().getActionDef();
  private deleteAction = new TableDeleteChargingStationSchedulesAction().getActionDef();
  private editAction = new TableEditChargingStationSchedulesAction().getActionDef();
  private ChargingStationFilterIDFilter: TableFilterDef;
  private ChargingStationSchedulesAuthorizations: DataResultAuthorizations;
  private currentDate = moment().format('YYYY-MM-DD');

  public constructor(
    public spinnerService: SpinnerService,
    public translateService: TranslateService,
    private messageService: MessageService,
    private dialogService: DialogService,
    private router: Router,
    private dialog: MatDialog,
    private componentService: ComponentService,
    private centralServerService: CentralServerService,
    private datePipe: AppDatePipe
  ) {
    super(spinnerService, translateService);
    // Init
    this.initDataSource();
  }

  public loadDataImpl(): Observable<DataResult<ChargingStationSchedules>> {
    return new Observable((observer) => {
      // Get the Tenants
      this.centralServerService
        .getChargingStationSchedules(this.buildFilterValues(), this.getPaging(), this.getSorting())
        .subscribe({
          next: (response) => {
            // this.createAction.visible = ChargingStationSchedules.canCreate;
            this.createAction.visible = true;
            // Initialise authorizations
            this.ChargingStationSchedulesAuthorizations = {
              // Metadata
              metadata: response.metadata,
            };
            observer.next(response);
            observer.complete();
          },
          error: (error) => {
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'general.error_backend'
            );
            observer.error(error);
          },
        });
    });
  }

  public buildTableDef(): TableDef {
    return {
      search: {
        enabled: true,
      },
      hasDynamicRowAction: true,
    };
  }

  public buildTableColumnDefs(): TableColumnDef[] {
    const columns: TableColumnDef[] = [
      {
        id: 'reservationId',
        name: 'evse.table.reservationId',
        headerClass: 'col-15p',
        class: 'text-left col-15p',
        direction: 'desc',
        sortable: true,
      },
      {
        id: 'chargingStationID',
        name: 'general.charging_station_id',
        headerClass: 'col-15p',
        class: 'text-left col-15p',
        direction: 'desc',
        sortable: true,
        formatter: (value: string) => (value ? value : '-'),
      },
      {
        id: 'connectorId',
        name: 'evse.table.connectorId',
        headerClass: 'col-15p',
        class: 'text-left col-15p',
        direction: 'desc',
        sortable: true,
        formatter: (value: string) => (value ? `Gun ${value}` : '-'),
      },
      // {
      //   id: 'parentIdTag',
      //   name: 'evse.table.parentIdTag',
      //   headerClass: 'col-15p',
      //   class: 'text-left col-15p',
      //   direction: 'desc',
      //   sortable: true,
      //   formatter: (value: string) => (value ? value : '-'),
      // },
      // {
      //   id: 'startTime',
      //   name: 'general.start_time',
      //   formatter: (startTime: Date) =>
      //     moment(`${this.currentDate} ${startTime}`).format('hh:mm A'),
      //   headerClass: 'd-none d-xl-table-cell col-15p',
      //   class: 'd-none d-xl-table-cell col-15p',
      // },
      // {
      //   id: 'closingTime',
      //   name: 'general.closing_time',
      //   formatter: (closingTime: Date) =>
      //     moment(`${this.currentDate} ${closingTime}`).format('hh:mm A'),
      //   headerClass: 'col-15p',
      //   class: 'text-left col-15p',
      //   direction: 'desc',
      //   sortable: true,
      // },
      {
        id: 'expireDate',
        name: 'general.expired_on',
        formatter: (expireDate: Date) => this.datePipe.transform(expireDate, 'longDate'),
        headerClass: 'col-15p',
        class: 'text-left col-15p',
        direction: 'desc',
        sortable: true,
      },
    ];
    return columns;
  }

  public buildTableActionsDef(): TableActionDef[] {
    const tableActionsDef = super.buildTableActionsDef();
    tableActionsDef.unshift(this.createAction);
    return tableActionsDef;
  }

  public buildTableDynamicRowActions(
    ChargingStationSchedules: ChargingStationSchedules
  ): TableActionDef[] {
    const rowActions: TableActionDef[] = [];

    // if (true) {
    //   // if (ChargingStationSchedules.canUpdate) {
    //   rowActions.push(this.editAction);
    // }
    if (true) {
      // if (registrationToken.canDelete) {
      rowActions.push(this.deleteAction);
    }
    return rowActions;
  }

  public actionTriggered(actionDef: TableActionDef) {
    // Action
    switch (actionDef.id) {
      case ChargingStationSchedulesButtonAction.CREATE_TOKEN:
        if (actionDef.id) {
          (actionDef as TableCreateChargingStationSchedulesActionDef).action(
            ChargingStationsChargingStationSchedulesDialogComponent,
            this.dialog,
            { authorizations: this.ChargingStationSchedulesAuthorizations },
            this.refreshData.bind(this)
          );
        }
        break;
    }
  }

  public rowActionTriggered(
    actionDef: TableActionDef,
    ChargingStationSchedules: ChargingStationSchedules
  ) {
    console.log('ChargingStationSchedules:::::::', ChargingStationSchedules);
    switch (actionDef.id) {
      case ChargingStationSchedulesButtonAction.DELETE_TOKEN:
        if (actionDef.action) {
          ChargingStationSchedules.id = `${ChargingStationSchedules.chargingStationID}#${ChargingStationSchedules.reservationId}`;
          console.log('updated object-ChargingStationSchedules:::::::', ChargingStationSchedules);
          (actionDef as TableDeleteChargingStationSchedulesActionDef).action(
            ChargingStationSchedules,
            this.dialogService,
            this.translateService,
            this.messageService,
            this.centralServerService,
            this.spinnerService,
            this.router,
            this.refreshData.bind(this)
          );
        }
        break;
      case ChargingStationSchedulesButtonAction.EDIT_TOKEN:
        if (actionDef.action) {
          (actionDef as TableCreateChargingStationSchedulesActionDef).action(
            ChargingStationsChargingStationSchedulesDialogComponent,
            this.dialog,
            { dialogData: ChargingStationSchedules },
            // { dialogData: itemData, authorizations: this.paymentSettlementAuthorizations },
            this.refreshData.bind(this)
          );
        }
        break;
    }
  }

  public buildTableActionsRightDef(): TableActionDef[] {
    return [
      new TableAutoRefreshAction(false).getActionDef(),
      new TableRefreshAction().getActionDef(),
    ];
  }

  public buildTableFiltersDef(): TableFilterDef[] {
    // const issuerFilter = new IssuerFilter().getFilterDef();
    this.ChargingStationFilterIDFilter = new ChargingStationFilter().getFilterDef();
    this.dateRangeFilter = new DateRangeTableFilter({
      translateService: this.translateService,
    }).getFilterDef();
    const filters: TableFilterDef[] = [this.dateRangeFilter, this.ChargingStationFilterIDFilter];
    return filters;
  }
}
