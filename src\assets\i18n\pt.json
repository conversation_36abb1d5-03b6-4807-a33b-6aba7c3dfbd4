{"general": {"generate": "Generate", "captcha_text_1": "Este site esta protegido com reCAPTCHA e Google", "captcha_text_2": "Politica Privada", "captcha_text_3": " e ", "captcha_text_4": "Termos de Serviço", "captcha_text_5": " aplicar", "not_authorized": "Você não está autorizado a realizar esta ação", "created_on": "C<PERSON><PERSON> em", "created_by": "<PERSON><PERSON><PERSON> por", "changed_on": "Modificado em", "changed_by": "Modificado por", "expired_on": "<PERSON><PERSON><PERSON> em", "revoked_on": "Revogado em", "description": "Descrito", "activate": "Ativo", "deactivate": "Inativo", "upload_in_progress": "Upload in progress, please wait...", "body": "Body", "day": "d", "hour": "h", "minute": "m", "second": "s", "more_records": "<PERSON><PERSON>", "click_here": "click aqui", "selected_records": "Selecionado", "nbr_of_records": "Registos", "connectors": "Conexões", "notifications": "Notificações", "dashboard.title": "Dashboard", "dashboard-transport.title": "Transport Dashboard", "general": "Main", "start_time": "Start Time", "available_date": "Available Date", "charging_station_id": "Charging Station ID", "closing_time": "Closing Time", "expire_date": "Expire Date", "app_name": "Operations Manager", "last_app_name": "(C) CERO SMART MOBILITY", "version": "Vers<PERSON>", "about_us": "So<PERSON> Nós", "unexpected_error_backend": "Ocurreu um erro inesperado", "select_at_least_one_record": "Você deve selecionar pelo menos um registo da lista", "mandatory_field": "Compo Obrigatório", "no_record_found": "Não foram encontrados registos", "no_image_found": "No images have been found", "no_connector_available": "Conexão não disponível", "error_max_length": "O tamanho máximo é {{length}}", "error_min_length": "O ta<PERSON>ho m<PERSON>imo <PERSON> {{length}}", "error_max_value": "O valor maximo é {{value}}", "error_min_value": "O valor mínimo <PERSON> {{value}}", "error_url_pattern": "URL incorreto", "error_number_pattern": "Numero Inválido", "invalid_date": "Data inválida", "error_not_a_number": "Número não disponível", "error_duplicate": "Entrada duplicada", "id": "Id", "ok": "Ok", "cancel": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "add": "<PERSON><PERSON><PERSON><PERSON>", "select": "Selecionar", "set_filter": "<PERSON><PERSON><PERSON>", "remove": "Remover", "location": "Localização", "address": "<PERSON><PERSON>", "address1": "Morada 1", "address2": "Morada 2", "postal_code": "Codigo Postal", "city": "Cidade", "region": "Região", "department": "Departamento", "country": "<PERSON><PERSON>", "tan_gst": "Tan/Gst", "value": "Value", "latitude": "Latitude", "longitude": "Longitude", "show_place": "<PERSON><PERSON>", "invalid_value": "<PERSON><PERSON>", "home": "Home", "details": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Refresh", "generate_qr": "Generate QR code", "display_qr": "Display QR code", "save": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "export": "Exportar", "import": "Importar", "synchronize": "Sincronizar", "force_synchronize": "Forçar <PERSON>ron<PERSON>", "download": "Download", "test_connection": "<PERSON><PERSON>", "upload": "Upload", "change": "<PERSON><PERSON><PERSON>", "reload": "<PERSON><PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON>", "unlock": "Desb<PERSON>que<PERSON>", "refund": "Restituição", "open": "Abe<PERSON>o", "open_refunding_system": "<PERSON>r para as suas despesas", "connect": "Conectar", "register": "Registar", "unregister": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Voltar", "next": "Proximo", "previous": "Anterior", "info": "Informação", "now": "<PERSON><PERSON><PERSON>", "default": "Padrão", "yes": "<PERSON>m", "no": "Não", "apply": "Aplicar", "clear": "Limpar", "warning": "Aviso", "success": "Sucesso", "error": "Erro", "drag_drop": "Arraste & Solte", "picture": "<PERSON><PERSON><PERSON>", "pictures": "Images", "change_picture": "<PERSON><PERSON>", "search": "Pesquisa", "search_place": "<PERSON><PERSON><PERSON><PERSON>", "use_place": "<PERSON>ar", "check_place": "Verificar", "search_date_from": "De", "search_date_until": "Para", "search_date_range": "Datas", "search_charging_station": "Estação de Carregamento", "search_user": "Utilizador", "search_site": "Site", "all": "<Tudo>", "max_lines": "Numero Maximo de Linhas", "auto_refresh": "Pesquisa Automática", "sort_date_desc": "Ordenar por Data Decrescente.", "search_placeholder": "Pesquisa...", "backend_not_running": "Não consigo conectar ao servidor central", "error_backend": "O Erro ocorreu enquanto tentava conectar ao servidor central", "error_backend1": "Please configure this in billing settings ", "car_catalog_error": "Ocurreu um erro ao recuperar o carro", "car_catalogs_error": "Ocurreu um erro ao recuperar o carros", "cars_error": "O curreu um error ao recuperar o carro do utilizador", "car_image_error": "O<PERSON>rreu um erro ao recuprar as imagens do carro", "success_backend": "A conexão ao servidor central foir restablecida", "success_import": "File imported successfully", "import_unexpected_error": "Error occurred while importing the file, check the logs", "choose_file": "Choose <PERSON>", "user_or_tenant_updated": "Você foi descontactado porque o seu perfil ou as configurações da sua organização foram atualizadas", "invalid_content": "Conteúdo <PERSON>", "open_in_maps": "Localização", "change_pending_title": "Alterações pendentes", "change_pending_text": "Algumas alterações estão pendentes.", "change_invalid_pending_title": "Alterações pendentes com valor inválido(s)", "change_invalid_pending_text": "Algumas alterações com valore(s) inválido estão pendentes.", "save_and_close": "Gravar & Fechar", "do_not_save_and_close": "Não Gravar & Fechar", "set_coordinates": "Alterar", "reset_filters": "Redefinit Filtros", "browser_not_supported": "Este browser não é suportado! Por favor utilise Google Chrome ou Mozilla Firefox", "status": "Status", "revoke": "Reivocar", "edit": "<PERSON><PERSON>", "import_instructions": "The file that you are going to import must have the following caracteristics:", "import_instructions_file_type": "File type must be CSV", "import_instructions_header_required": "CSV Header is required", "import_instructions_coma_separator": "Comma separator is used ','", "import_instructions_required_fields": "Required field(s): '{{properties}}'", "import_instructions_optional_fields": "Optional field(s): '{{properties}}'", "invalid_file_error": "The provided file is invalid", "invalid_file_csv_header_error": "At least one of the required header was not provided in the CSV file, operation has been aborted", "import_already_ongoing": "Cannot upload the badges while an import is ongoing", "menu": {"dashboard": "Painel de <PERSON>o", "dashboard-transport": "Transport Dashboard", "charging_stations": "Estações de Carga", "transactions": "Sessões", "pricing": "Pricing", "active": "Ativo", "history": "Hist<PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "consumption": "Consumos", "usage": "<PERSON><PERSON>", "inactivity": "Inatividade", "organisations": "Organizações", "companies": "Empresas", "sites": "Sites", "site_areas": "Site Areas", "users": "Utilisadores", "transports": "Transportes", "manufacturers": "Fabricantes", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logs": "Hist<PERSON><PERSON><PERSON>", "tenants": "Organizações", "carbon-credit": "Carbon Credit", "wallet-report": "Wallet Report", "revenue": "<PERSON><PERSON><PERSON>", "components": "Componentes", "settings": "Configurações", "integration_settings": "Integration Settings", "technical_settings": "Technical Settings", "assets": "Ativos", "organization": "Organização", "template": "Templates", "cars": "<PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON>", "tags": "Badges", "charging_station_templates": "Charging Station Templates", "payment-settlement": "Payment Settlement"}, "tooltips": {"push_cdr": "Buscar CDR", "activate": "Ativo", "deactivate": "Inativo", "export": "Exportar parametros OCPP", "assign_site_area": "Adicionar site", "more": "<PERSON><PERSON>", "smart_charging": "Carregamento Inteligente", "clear_cache": "Limpar cache", "soft_reset": "Reset", "reboot": "Reboot", "more_actions": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "auto_refresh": "Atualização automática", "create": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "edit_chargers": "Editar Estação de carregamento", "display_chargers": "Mostrar estações de carregamento", "edit_assets": "<PERSON>ar at<PERSON>s", "display_assets": "Mostrar ativos", "assign_site": "Atribuir ao Site", "edit_users": "<PERSON><PERSON><PERSON> util<PERSON>", "edit_siteArea": "Editar Areas do Site", "display_siteAreas": "Mostrar areas do site", "no_action": "Sem ações spossíveis", "open": "Abrir", "open_in_maps": "Abrir no google maps", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "generate_qr": "Generate QR code", "refund": "Restituir", "register": "Registar", "unregister": "<PERSON><PERSON><PERSON>", "remove": "Remover", "revoke": "Reivocar", "settings": "Configurações", "synchronize": "Sincronizar", "start": "Iniciar", "stop": "<PERSON><PERSON>", "unlock": "Desb<PERSON>que<PERSON>", "view": "Verificar", "send": "Enviar", "clear": "Limpar", "save": "<PERSON><PERSON><PERSON>", "download": "Download", "test_connection": "<PERSON><PERSON>", "force_available_status": "Forçar estado disponível", "force_unavailable_status": "Forçar estado indisponível", "display_users": "Display users", "display_sites": "Display sites", "pricing": "Pricing"}}, "issuer": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "local": "Organização Atual", "foreign": "Organização Externa"}, "dashboard": {"active_stations_gauge_title": "Pontos de Carregamento", "active_stations_gauge_unit": "Ocupado", "occupied_charging_points_gauge_footer": "Numero de carregadores ocupados", "occupied_charging_points_gauge_title": "Ocupado", "all_site": "Todos sites", "co2_footer": "Economizar CO2 desde 2017 com base em uma emissão de 120g/km", "co2_title": "CO2", "consumption_gauge_footer": "Poder instantaneo em kW", "consumption_gauge_title": "Poder Instantâneo", "consumption_title": "Poder Instantâneo", "gas_footer": "Economizou combustível usando um veículo térmico que consome 8L / 100km", "gas_title": "Combustível", "km_footer": "Distância percorrida por veículo elétrico consumindo 18kW.h / 100km", "km_title": "Distância", "location": "Localização", "metrics": "Métricas atuais", "realtime_title": "Estatisticas de hoje", "realtime": {"button": {"consumption": "Consu<PERSON>", "utilization": "Utilização"}}, "statistics_title": "Histórico de Consumos", "statistics": {"button": {"day": "<PERSON>a", "month": "<PERSON><PERSON>", "week": "Se<PERSON>", "year": "<PERSON><PERSON>"}}, "today_duration": "Tempo de Carregamento", "today_duration_footer": "Tempo total despidido em carregamentos hoje", "today_inactivity": "Inatividade de carga", "today_inactivity_footer": "Tempo total gasto hoje por veículos que terminaram de carregar, mas permaneceram conectados", "today_energy_delivered": "Energia consumida", "today_energy_delivered_footer": "Energia total consumida hoje", "trends_of_day_avg": "Média", "trends_of_day_duration_avg": "Média", "trends_of_day_duration_max": "<PERSON><PERSON>", "trends_of_day_duration_min": "Minimo", "trends_of_day_duration_title": "{{todayDay}} tendência", "trends_of_day_inactivity_avg": "Média", "trends_of_day_inactivity_max": "<PERSON><PERSON>", "trends_of_day_inactivity_min": "<PERSON><PERSON><PERSON>", "trends_of_day_inactivity_title": "{{todayDay}} tendência", "trends_of_day_max": "<PERSON><PERSON>", "trends_of_day_min": "Minimo", "trends_of_day_title": "{{todayDay}} tendência", "tabs": {"dashboard": "Dashboard"}, "flters": {"dropdown": {"all": "All", "current": "Current", "external": "External"}}, "cards": {"charging_stations": "Charging Stations", "connection_loss": "Faults/Connection Loss", "active_users": "Active Users", "active_sessions": "Active Sessions", "charging_sessions_month": "Charging Sessions (Current Month)", "charging_sessions_year": "Charging Sessions (Current Year)", "revenue_today": "Revenue (Today)", "revenue_month": "Revenue (Current Month)", "revenue_year": "Revenue (Current Year)", "energy_today": "Energy (Today)", "energy_month": "Energy (Current Month)", "energy_year": "Energy (Current Year)", "carbon_credit_today": "Carbon Credit (Today)", "carbon_credit_month": "Carbon Credit (Current Month)", "vehicle_charging_now": "Vehicle Charging Now", "vehicle_charging_history": "Vehicle Charging (History)"}}, "companies": {"no_companies": "Nenhuma empresa encontrada", "company_number": "Empresas)", "number_of_sites": "Nb Sites", "logo": "Logo", "name": "Nome", "company": "Empresa", "general.value": "Tax Level Value", "taxLevel.value": "TAN/GST", "title": "Empresa", "titles": "Empresas", "created_on": "C<PERSON><PERSON> em", "created_by": "<PERSON><PERSON><PERSON> por", "changed_on": "Modificado em", "changed_by": "Modificado por", "delete_title": "Eliminar", "delete_button": "Eliminar", "delete_confirm": "Você realmente deseja excluir a empresa '{{companyName}}'?", "delete_success": "A empresa '{{companyName}}' foi eliminado com sucesso", "delete_error": "O erro ocorreu enquanto tentava eliminar a empresa", "changed_by_other_user": "A empresa '{{companyName}}' modificada por outro utilizador vai ser rearregado", "update_success": "A empresa '{{companyName}}' foi atualizada com sucesso", "update_error": "O error ocorreu enquanto se efetuava o update da empresa", "company_not_found": "A empresa não foi encontrada", "create_success": "A empresa '{{companyName}}' foi criada com sucesso", "create_error": "O erro ocorreu enquanto creava a empresa", "logo_size_error": "O limite de tamanho de logotipo foi excedido, o limite está abaixo {{maxPictureKb}} Kb", "select_companies": "Selecione a empresa"}, "assets": {"no_assets": "Nenhum ativo encontrado", "asset_number": "Ativo(s)", "logo": "Logo", "name": "Nome", "asset_type": "Tipo de Ativo", "fluctuation_percent": "Fluctuation (%)", "variation_percent": "Variation Threshold (%)", "static_value_watt": "Static Power (W)", "produce": "Produção de Energia", "consume": "Consumo de Energia", "consume_and_produce": "Consume and Produce Energy", "dynamic_asset": "Ativo Dinâ<PERSON>", "uses_push_api": "Uses Push API", "instant_power": "Poder Instantâneo", "asset_connection": "Conexão de Ativos", "meter_id": "Medidor ID", "asset": "Ativo", "title": "Ativo", "titles": "Ativos", "created_on": "<PERSON><PERSON><PERSON> em", "created_by": "<PERSON><PERSON>o por", "changed_on": "Modificado em", "changed_by": "Modificado por", "delete_title": "Eliminar", "delete_button": "Eliminar", "delete_confirm": "Você quer mesmo eliminar o ativo '{{assetName}}'?", "delete_success": "O ativo '{{assetName}}' foi eliminado com sucesso", "delete_error": "O erro ocorreu enqaunto tentava eliminar o ativo", "changed_by_other_user": "O ativo '{{assetName}}' foi modificado por outro utilizador e recarregado", "update_success": "O ativo '{{assetName}}' foi atualizado com sucesso", "update_error": "O erro ocorreu enquanto atualizava o ativo", "asset_not_found": "O ativo não foi encontrado", "asset_settings_error": "Ocorreu um erro ao obter conexões de ativos", "create_success": "O ativo '{{assetName}}' foi criado com sucesso", "create_error": "O error ocurreu enquanto criava o ativo", "refresh_success": "The asset's consumption or production has been successfully retrieved", "refresh_error": "Error occurred while retrieving the asset's consumption or production'", "consumption_error": "Error occured while retrieving the consumption", "logo_size_error": "O limite de tamanho da imagem foi excedido, o limite está abaixo de {{maxPictureKb}} Kb", "select_assets": "Selecionar Ativo", "consumption_date": "Data", "dialog_tabs": {"connection": "Conexão"}, "errors": {"missing_site_area": {"title": "Não atríbuido a uma Area de site", "description": "O ativo precisa ser atribuído a uma área de site", "action": "Edite o ativo e manter a área do site"}}}, "sites": {"assign_company": "Atribuir Empresa", "auto_user_site_assignment": "Atribuição automática de utilizador a este site", "public": "Este site é publico", "public_site": "Publico", "auto_assignment": "Atribuição automática de utilizador", "user_list": "Utilizadores autorizados a usar estações de carregamento neste site", "user_list_source_title": "Disponível", "user_list_target_title": "Atribu<PERSON><PERSON>", "no_sites": "Selecionar Site", "select_sites": "Selecionar Site", "site": "Site", "assigned_users_to_site": "Utilizador assignado a {{siteName}}", "unassigned": "Não atribuído", "no_connected_chargers": "Você não está conectado a uma estção de carregamento", "site_number": "Site(s)", "number_of_site_areas": "Nb <PERSON><PERSON><PERSON>", "number_of_users": "Nb Utilizadores", "users": "Utilizadores", "image": "Imagem", "name": "Nome", "title": "Site", "titles": "Sites", "company_invalid": "Company invalid", "created_on": "Created On", "created_by": "Created By", "changed_on": "Changed On", "changed_by": "Changed By", "delete_title": "Delete", "site_not_found": "O site não foi encontrado", "delete_button": "Eliminar", "delete_confirm": "Você quer mesmo eliminar o site '{{siteName}}'?", "delete_success": "O site '{{siteName}}' eliminado com sucesso", "delete_error": "Ocurreu um erro enquanto eliminava o site", "remove_users_title": "Remover Utilizadore(s)", "remove_users_confirm": "Você tem mesmo a certeza que quer remover os utilizadore(s) deste site?", "remove_users_success": "Os utilizadore(s) foram removidos com sucesso", "remove_users_error": "Ocurreram erros enquanto os utilizadore(s) eram removidos do site", "update_users_success": "Os utilizadores foram adicionados com sucesso", "changed_by_other_user": "O site '{{siteName}}' modificado por outro utilizador foi recarregado", "update_success": "O site '{{siteName}}' atualizado com sucesso", "update_error": "Ocurreu um error durante a atualização do site", "update_users_error": "Ocurreu um erro enquanto se tentava adicionar um utilizador ao site", "update_public_site_error": "Site '{{siteName}}' cannot be made private and having public charging stations", "create_success": "O site '{{siteName}}' foi criado com sucesso", "create_error": "Ocurreu um erro na criação do site", "image_size_error": "O limite de tamanho da imagem foi excedido, o limite está abaixo {{maxPictureKb}} Kb", "admin_role": "Administratdor do <PERSON>", "owner_role": "Dono do Siste", "update_set_site_admin_success": "O utilizador '{{userName}}' é agora o administrador deste site", "update_remove_site_admin_success": "O utilizador '{{userName}}' não é mais administrador deste site", "update_site_users_role_error": "Ocorreu um erro ao tentar atualizar as autorizações do utilizador '{{userName}}'", "update_set_site_owner_success": "O utilizador '{{userName}}' é agora dono do site", "update_remove_site_owner_success": "O utilizador '{{userName}}' não é mais dono do site", "update_site_users_owner_error": "Ocorreu um erro ao tentar atualizar as permissões do utilizador '{{userName}}' como dono do site", "export_all_params_title": "Exportar parametros OCPP", "export_all_params_confirm": "Você realmente deseja exportar todos os parâmetros OCPP de todas as estações de carregamento pertencentes ao site '{{siteName}}' ?", "export_all_params_error": "Ocurreu um erro ao tentar exportar todos os parâmetros OCPP", "display_users": "Display users", "private_limitation": "Some features are not available when the site is private"}, "site_areas": {"limits": "Limits", "assign_site": "Atribuir Site", "smart_charging": "Carregamento Inteligente", "export_all_ocpp_params": "Exportar todos os parametros OCPP", "edit_assets": "<PERSON><PERSON> contas", "display_assets": "<PERSON><PERSON>", "no_site_areas": "Não foi encontrado nenhum site", "site_areas_number": "Area(s) Site", "show_all_chargers": "<PERSON>rar todas as estações de carga", "enable_access_control": "Habilitar o Controlo de Acesso (o utilizador deve-se autenticar com o badge)", "enable_smart_charging": "Habilitar o carregamento Inteligente", "access_control": "Controlo de acessos", "charger_list_source_title": "Disponível", "charger_list_target_title": "Atibuido", "number_of_charging_stations": "Carregadores NB", "site_area_not_found": "A Area de Site não foi encontrada", "select_site_areas": "Selecionar Area de Site", "unassigned": "Cancelar <PERSON>ri<PERSON>", "image": "Image", "assigned_chargers": "Estações de Carga", "assigned_chargers_to_site_area": "Estações de carga assignadas a {{siteAreaName}}", "assigned_assets_to_site_area": "Contas assigandas a {{siteAreaName}}", "name": "nome", "title": "Area do Site", "titles": "Areas do Site", "parent_site_area": "Parent Site Area", "select_parent_site_area": "Select Parent Site Area", "site_area_hierarchy_error_title": "Site Area Hierarchy Error", "carbon_credit": "Carbon Crdit", "site_area_tree_error": ["An error occurred while modifying the Site Area chain.", "Avoid circular dependencies within the tree.", "For more information please check the logs."], "site_area_tree_error_site": ["An error occurred while modifying the Site Area chain.", "Site Area has children with a different Site.", "Do you want to:", "1 - Update all Site Area chain with the new Site", "2 - Assign the current parent Site Area to all children", "3 - Clear the parent Site Area in all children"], "site_area_tree_error_site_update": "Update (1)", "site_area_tree_error_site_attach": "Assign (2)", "site_area_tree_error_site_clear": "Clear (3)", "site_area_tree_error_smart_charging": ["An error occurred while modifying the Site Area chain.", "Site Area must have the same Smart Charging value as its parent.", "Do you want change the Smart Charging value to the whole Site Area chain?"], "site_area_tree_error_voltage": ["An error occurred while modifying the Site Area chain.", "Site Area must have the same Voltage as its parent."], "site_area_tree_error_number_of_phases": ["An error occurred while modifying the Site Area chain.", "Site Area must have the same Number of Phases as its parent."], "site_area_tree_error_multiple_actions_not_supported": ["An error occurred while modifying the Site Area chain.", "Multiple changes are currently not supported (eg. change of Site and Smart Charging.)", "Perform one change at a time."], "site_invalid": "Area do Site Inválida", "site_area_does_not_exist": "A are ado site não existe", "max_limit_kw": "Limite <PERSON>", "created_on": "C<PERSON><PERSON> em", "created_by": "<PERSON><PERSON><PERSON> por", "changed_on": "Modificado em", "changed_by": "Modificado por", "delete_title": "Eliminar", "delete_button": "Eliminar", "delete_confirm": "Você quer mesmo eliminar a area do site '{{siteAreaName}}'?", "delete_success": "A area do site '{{siteAreaName}}' foi eliminada com sucesso", "delete_error": "Ocorreu um erro enquanto tentava eliminar a Area do site", "remove_chargers_title": "Remover Estação/ões de Carga", "remove_chargers_confirm": "Você tem a certeza que prentende remover as estações de carga selecionadas, desta áre ado site", "remove_chargers_success": "As estação/ões de carga foram removidas com sucesso", "remove_chargers_error": "Ocorreu um erro enquanto tentava eliminar as estação/ões de carga do site", "update_chargers_success": "A estação/ões de carga foram adicionadas com sucesso", "remove_assets_title": "Remover ativo(s)", "remove_assets_confirm": "Você tem a certeza que quer remover os ativos selecionado(s) para esta area do site?", "remove_assets_success": "O ativo(s) foi removido com sucesso", "remove_assets_error": "Ocurreu um erro enquanto removia os ativo(s) desta area do site", "update_assets_success": "Os ativo(s) foram adicionos com sucesso", "changed_by_other_user": "A are ado site '{{siteAreaName}}' foi modificada e recarregada com sucesso", "update_success": "A area de site '{{siteAreaName}}' foi atualizada com sucesso", "update_error": "Ocurreu um error enquanto atualizava a area do site", "update_phase_error": "A area de site contem estações de carga com tres fases", "create_success": "A area do site '{{siteAreaName}}' foi criada com sucesso", "create_error": "Ocurreu um erro na criação da area do site", "image_size_error": "O tamanho limite da imagem foir execidod o limete deve estar abiaxo {{maxPictureKb}} Kb", "maximum_energy_watts": "Poder maximo (W)", "maximum_energy_amps_per_phase": "Maximum Amperage per phase (A)", "maximum_energy_amps_total": "Maximum total Amperage (A)", "number_of_phases": "Numero de Fases", "export_all_params_title": "Exportar parametros OCPP", "export_all_params_confirm": "Você realmente deseja exportar todos os parâmetros OCPP de todas as estações de carregamento pertencentes à área do site '{{siteAreaName}}' ?", "export_all_params_error": "Ocurreu um erro enquanto tentava exportar todos os parametros OCPP", "consumption_date": "Data", "single_phased": "Fase Unica", "three_phased": "Fase <PERSON>", "redirect": "Navigate to Site Area"}, "authentication": {"accept": "Eu aceito o", "eula": "Acordo de licenciamento unico", "must_accept_eula": "Você deve aceitar o licneciamento unico", "no_space_in_password": "Nenhum espaço é permitido no início e no final", "title": "Faça login no Open e-Mobility", "register": "Registar", "signup_link": "Novo utilizador? Registar!", "signin_link": "Você ja tem uma conta? Faça Login!", "password": "Password", "invalid_password": "Password inválida", "repeat_password": "Repeat Password", "password_rule": " A Password deve conter no minimo 8 caracateres, uma minuscula, um numero e um carcater especial", "password_not_equal": "As Password não são identicas", "sign_in": "<PERSON><PERSON>", "sign_up": "Registar", "mobile_register": "Mobile Registeration", "sign_out_short": "<PERSON><PERSON>", "sign_out": "<PERSON><PERSON><PERSON>", "email": "Email", "invalid_email": "Introduza um email valido", "forgot_password": "Esque<PERSON>u a password?", "email_already_exists": "O email já existe", "email_does_not_exist": "O email não existe", "cannot_check_email_in_backend": "Este email não pode ser verificado pelo servidor de backend", "wrong_email_or_password": "<PERSON>ail ou password incorretos", "technical_user_cannot_login_to_ui": "API user and cannot login using the application", "account_suspended": "A sua conta está suspensa", "account_pending": "A sua conta esta pendente, chekc o seu email", "super_user_account_pending": "A sua conta esta pendente, chekc o seu email", "account_locked": "A sua conta esta bloqueada", "account_blocked": "A sua conta esta suspensa", "account_inactive": "A sua conta esta inativa", "define_password_title": "Definir a sua password", "define_password_button": "<PERSON><PERSON><PERSON>", "define_password_hash_not_valid": "O seu pedido não está valido por favor peça uma nova passowrd", "define_password_success": "A sua password foi atualizada com sucesso", "define_password_error": "O<PERSON>rreu um erro quando mudava de password", "reset_password_title": "Reset de password", "reset_password_button": "Reset", "reset_password_confirm": "Você quer mesmo fazer o reset da password?", "reset_password_success": "Se o seu email foi valido voce irá receber um link para reinicializar a sua password.", "password_strengh": "Password", "password_one_maj": "A", "password_one_min": "a", "password_one_special": "@", "password_one_number": "0", "password_one_length": ">= 8", "register_user_success": "A sua conta foi criada com sucesso por favor verifique o seu email", "register_super_user_success": "A sua conta foi criada com sucesso! Um Administador vai criar a conta e ativala", "register_user_error": "O erro ocurreu enquanto criava a conta", "verify_email_title": "Ativação de Conta", "verify_email_already_active": "A sua conta ja esta ativa!", "verify_email_success": "A sua conta foi ativada com sucesso!", "verify_email_success_inactive": "Your account has been verified with success, an administrator will check and activate your account.", "verify_email_success_set_password": "A sua conta foi ativada, por favor crie a sua password para poder reinicializar a conta", "verify_email_token_not_valid": "O seu link de ativação da conta não está valido", "verify_email_email_not_valid": "Este email não existe", "verify_email_error": "Ocurreu um erro enquanto ativava a conta", "verify_email_resend_button": "Enviar", "verify_email_resend_confirm": "A sua conta não está ativa! Você quer pedir um link de ativação de novo?", "verify_email_resend_success": "O seu link de ativação de conta foi enviado com sucesso", "verify_email_resend_error": "Ocorreu um erro ao enviar o link de ativação da sua conta", "verify_email_proceed_with_activation": "A Sua conta ainda está inativa! Quer reenviar o e-mail de ativação?", "verify_email_proceed_with_activation_button": "<PERSON><PERSON><PERSON><PERSON>", "invalid_captcha_token": "<PERSON><PERSON> de captcha inválid<PERSON>", "mercedes_data_usage": "SAP Labs France is accessing your car’s data via Mercedes-Benz for the purpose of providing you a high quality of service during the charging session of your vehicle. SAP Labs France is processing the following data: EV battery level and range of your car. For allowing the processing of your data, you’ll have to give the explicit consent in the following steps. For any further information please note our End-user Agreement Usage of the e-Mobility Software."}, "statistics": {"title": "Estatísticas", "description": "Statistics of charging sessions", "charger_kw_h": "kW.h", "carbon_credit_tons": "kg", "legend_select_unselect_all": "Deselecionar/Selecionar tudo", "graphic_title_consumption_y_axis": "Consumo (kW.h)", "graphic_title_carbon_credit_y_axis": "Consumption (kg)", "graphic_title_usage_y_axis": "<PERSON><PERSON> (hours)", "graphic_title_inactivity_y_axis": "Inatividade (hours)", "graphic_title_transactions_y_axis": "Numero de sessões", "graphic_title_pricing_y_axis": "Preço por sessão em {{currency}}", "multiple_currencies": "(Multiple Currencies)", "graphic_title_month_x_axis": "Meses", "transactions_years": "<PERSON><PERSON>", "total_consumption_year": "Consumo Total {{year}}", "total_usage_year": "Uso Total {{year}}", "consumption_per_cs_month_title": "Estações de Carga Consumo por mês(Overall: {{total}} kW.h)", "consumption_per_cs_year_title": "Estações de Carga Consumo por ano: {{total}} kW.h", "consumption_per_cs_timeFrame_title": "Estações de Carga Consumo num instante de tempo selecionado (Overall: {{total}} kW.h)", "consumption_per_cs_total_title": "Estações de Carga Consumo Total: {{total}} kW.h", "consumption_per_user_month_title": "Utilizadores Consumo por mês (Overall: {{total}} kW.h)", "consumption_per_user_year_title": "Utilizadores Consumo por ano: {{total}} kW.h", "consumption_per_user_timeFrame_title": "Utilizadores Consumo num instante de tempo selecionado: {{total}} kW.h", "consumption_per_user_total_title": "Utilizadores Consumo Total: {{total}} kW.h", "carbon_credit_per_cs_month_title": "Charging Stations Consumption per Month (Overall: {{total}} kg)", "carbon_credit_per_cs_year_title": "Charging Stations Consumption per Year: {{total}} kg", "carbon_credit_per_cs_timeFrame_title": "Charging Stations Consumption in selected time frame (Overall: {{total}} kg)", "carbon_credit_per_cs_total_title": "Charging Stations Total Consumption: {{total}} kg", "carbon_credit_per_user_month_title": "Users Consumption per Month (Overall: {{total}} kg)", "carbon_credit_per_user_year_title": "Users Consumption per Year: {{total}} kg", "carbon_credit_per_user_timeFrame_title": "Users Consumption in selected time frame: {{total}} kg", "carbon_credit_per_user_total_title": "Users Total Consumption: {{total}} kg", "usage_per_cs_month_title": "Estações de Carga Uso por mês (Overall: {{total}} horas)", "usage_per_cs_year_title": "Estações de Carga Uso por ano: {{total}} horas", "usage_per_cs_timeFrame_title": "Estações de Carga Uso num instante de tempo selecionado: {{total}} horas", "usage_per_cs_total_title": "Estações de Carga Uso Total: {{total}} horas", "usage_per_user_month_title": "Utilizadores Uso por mês  (Overall: {{total}} horas)", "usage_per_user_year_title": "Utilizadores Uso por ano: {{total}} horas", "usage_per_user_timeFrame_title": "Utilizadores Uso num instante de tempo selecionado: {{total}} horas", "usage_per_user_total_title": "Utilizadores Uso Total: {{total}} horas", "inactivity_per_cs_month_title": "Estações de Carga Inatividade por mês (Overall: {{total}} horas)", "inactivity_per_cs_year_title": "Estações de Carga Inactividade por ano: {{total}} horas", "inactivity_per_cs_timeFrame_title": "Estações de Carga Inatividade num instante de tempo selecionado: {{total}} horas", "inactivity_per_cs_total_title": "Estações de Carga Inatividade Total: {{total}} horas", "inactivity_per_user_month_title": "Utilizadores Inatividade por mês (Overall: {{total}} horas)", "inactivity_per_user_year_title": "Utilizadores Inactividade por ano: {{total}} horas", "inactivity_per_user_timeFrame_title": "Utilizadores Inatividade num instante de tempo selecionado: {{total}} horas", "inactivity_per_user_total_title": "Utilizadores Inatividade Total: {{total}} horas", "transactions_per_cs_month_title": "Estações de Carga Sessões por mês (Overall: {{total}})", "transactions_per_cs_year_title": "Estações de Carga Sessões por ano: {{total}}", "transactions_per_cs_timeFrame_title": "Estações de Carga Sessões num instante de tempo selecionado: {{total}}", "transactions_per_cs_total_title": "Estações de Carga Total de Sessões: {{total}}", "transactions_per_user_month_title": "Utilizadores Sessões por mês (Overall: {{total}})", "transactions_per_user_year_title": "Utilizadores Sessões por ano: {{total}}", "transactions_per_user_timeFrame_title": "Utilizadores Sessões num instante de tempo selecionado: {{total}}", "transactions_per_user_total_title": "Utilizadores Total de Sessões: {{total}}", "pricing_per_cs_month_title": "Estações de Carga Preço por mês (Overall: {{total}})", "pricing_per_cs_year_title": "Estações de Carga Preço por ano: {{total}}", "pricing_per_cs_timeFrame_title": "Estações de Carga Preço num instante de tempo selecionado: {{total}}", "pricing_per_cs_total_title": "Estações de Carga Peço Total: {{total}}", "pricing_per_user_month_title": "Users Preço por mês (Overall: {{total}})", "pricing_per_user_year_title": "Users Preço por ano: {{total}}", "pricing_per_user_timeFrame_title": "Users Preço num instante de tempo selecionado: {{total}}", "pricing_per_user_total_title": "Users Peço Total: {{total}}", "hours": "horas", "total": "Total", "others": "Outros", "category_label": "Categoria", "dialog": {"consumption": {"export": {"title": "Exportar dados de consumo", "confirm": "Você quer mesmo exportar todos os dados de consumo que correspondem com os filtros escolhidos?"}}, "carbon_credit": {"export": {"title": "Export Carbon Credit Data", "confirm": "Do you really want to export all the Carbon Credit data matching the filters?"}}, "usage": {"export": {"title": "Exportar dados de utilização", "confirm": "Você quer mesmo exportar todos os dados de consumo que correspondem com os filtros escolhidos?"}}, "inactivity": {"export": {"title": "Exportar dados de inatividade", "confirm": "Você quer mesmo exportar todos os dados de consumo que correspondem com os filtros escolhidos?"}}, "transactions": {"export": {"title": "Exportar dados de sessões", "confirm": "Você quer mesmo exportar todos os dados de consumo que correspondem com os filtros escolhidos?"}}, "pricing": {"export": {"title": "Exportar dados de preços", "confirm": "Você quer mesmo exportar todos os dados de consumo que correspondem com os filtros escolhidos?"}}}}, "chargers": {"chargers": "Estações de Carregamento", "token": "Token", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charger_not_found": "A estação de carregamento não foi encontrada", "vendor": "Fornecedor", "model": "<PERSON><PERSON>", "name": "Nome", "inactivity": "Inatividade", "heartbeat_title": "Estado", "charger_disconnected": "Desconectado", "charger_connected": "<PERSON><PERSON><PERSON><PERSON>", "connectors_title": "Contectados", "consumption_title": "Poder Instantâneo", "total_consumption_title": "Consu<PERSON>", "firmware_version": "Versão fo Firmware", "firmware_status": "Estado do Firmware", "connector_status": "<PERSON><PERSON><PERSON>", "ocpp_version": "Versão do OCCPP", "ocpp_protocol": "Protocolo OCCPP", "ocpp_version_title": "OCPP", "private_url": "URL Privado", "nb_connected_phase": "Numero de fases do conector", "current_type": "Tipo de Atual", "phase_assignment": "Atribuição de fase", "phase_combinations": {"three_phased": {"cs_1_g_1": "RST (L1, L2, L3)", "cs_1_g_2": "STR (L2, L3, L1)", "cs_1_g_3": "TRS (L3, L1, L2)"}, "single_phased": {"cs_1_g_1": "R (L1)", "cs_1_g_2": "S (L2)", "cs_1_g_3": "T (L3)"}}, "cant_charge_in_parallel": "Não podem ser feitos carregamentos em paralelo", "share_power_to_all_connectors": "Partilhar o Poder com todos os conectores", "exclude_from_power_limitation": "Não se pode limitar o poder", "update_public_cs_error": "Charging Station cannot be made public on a private site", "public": "Esta estação de carregamento é publica", "public_charger": "Publica", "exclude_smart_charging": "Exlucido do carregamento Rapido", "force_inactive": "Esta estação de carregamento está inativa", "manual_configuration": "Manual configuration", "master_slave": "Master / Slave", "public_url": "URL Publico", "current_ip": "Rota de IP corrente", "select_chargers": "Selecionar estações de carga", "serial_number": "Numero de Série", "last_reboot": "<PERSON><PERSON><PERSON>", "capabilities": "Capacidades", "ocpp_standard_params": "Parametros Standard OCPP", "ocpp_vendor_params": "Parametros Avançados OCPP", "ocpp_advanced_command": "Comando Avançados OCPP", "unassigned": "Não atribuído", "unassigned_chargers": "Carregamentos", "maximum_energy": "<PERSON><PERSON> (Watts)", "maximum_energy_amps": "Amperagem minima (Amps)", "efficiency": "Eficiência (%)", "invalid_efficiency": "A eficiência deve ser um número positivo não maior que 100", "connector": "Connector", "charge_point": "Ponto de Carregamento", "connector0": "Carregador", "connector_type": "Tipo de Carregador", "connector_error_title": "Erro", "connector_info_title": "Informação", "connector_vendor_error_code_title": "<PERSON><PERSON>", "connector_max_power": "<PERSON><PERSON> maximo (Watts)", "charger_url": "URL", "charger_param_key": "Chave", "charger_param_value": "Valor", "title": "Estações de Carregamento", "titles": "Estações de Carregamento", "status_occupied": "O<PERSON><PERSON><PERSON>", "status_available": "Disponível", "status_preparing": "Preparada", "status_charging": "Em Carregamento", "status_suspendedevse": "EVSE Suspenso", "status_suspendedev": "EV Suspenso", "status_finishing": "Carregamento Finalizado", "status_reserved": "Reservado", "show_active_only": "Conectado", "status_unavailable": "Indisponível", "status_faulted": "<PERSON><PERSON>", "status_unknown": "Desconhecido", "status_error_connector_lock_failure": "Falha de bloqueio do <PERSON>ctor", "status_error_ev_communication_error": "EV Erro de Comunicação", "status_error_ground_failure": "<PERSON><PERSON><PERSON> de Aterramento", "status_error_high_temperature": "Alta Temperatura", "status_error_internal_error": "<PERSON><PERSON>", "status_error_local_list_conflict": "Conflito de Lista local", "status_error_none": "Sem erros", "status_error_other_error": "Outro erro", "status_error_over_current_failure": "Falha de sobrecorrente", "status_error_over_voltage": "<PERSON><PERSON><PERSON><PERSON>", "status_error_power_meter_failure": "Falha do medidor de energia", "status_error_power_switch_failure": "Falha do interruptor de alimentação", "status_error_reader_failure": "Falha do leitor", "status_error_reset_failure": "Falha de reinicialização", "status_error_under_voltage": "<PERSON><PERSON>", "status_error_weak_signal": "Sinal fraco", "status_error_unknown": "Desconhecido", "status_firmware_idle": "Inativo", "status_firmware_downloading": "A efetuar Download", "status_firmware_downloadfailed": "Falha ao efetuar download", "status_firmware_installing": "Efetuando instalação", "status_firmware_installationfailed": "Falha na instalação", "status_firmware_downloaded": "Download efetuado", "status_firmware_installed": "Instalado", "reboot_title": "Reiniciar estação de carregamento", "soft_reset_title": "Reiniciar estação de carregamento", "charger_id_not_found": "A estção de carregamento ID '{{chargerID}}' não foi encontrada", "no_transaction_found": "Não foram encontradas sessões na estação '{{chargerID}}'", "dialog": {"export": {"title": "Exportar estações de carga", "confirm": "Você realmente deseja exportar todas as estações de carregamento que correspondem aos filtros ?", "error": "Ocorreu um erro ao tentar exportar as estações de carregamento"}, "exportConfig": {"title": "Exportar parametros de carregamento da estação de carga", "confirm": "Deseja realmente exportar todos os parâmetros que correspondem ao filtro ?"}, "localisation": {"title": "Localização das estações de carga"}, "settings": {"fixed_url_for_ocpp": "A partir do OCPP 1.6, a URL é fixada pelo servidor", "callback_url_for_ocpp": "URL de retorno de chamada para enviar solicitação HTTP para a estação de carregamento"}, "enable_manual_configuration": {"title": "Enable manual configuration", "confirm": "If you enable the manual configuration, the charging station will not be adjusted with the automatic configuration template anymore. Do you want to continue?"}, "disable_manual_configuration": {"title": "Disable manual configuration", "confirm": "If you disable the manual configuration, the charging station will be adjusted with the automatic configuration template, rebooted and your manual changes will be lost. Do you want to continue?"}, "manual_configuration_error": {"title": "Manual Configuration/Template error", "confirm": "Manual Configuration has been already disabled for this station. Please make sure it boots correctly and that the template can be applied"}}, "force_available_status_action": "Conjunto disponível", "force_unavailable_status_action": "Conjunto indisponível", "reboot_action": "Reboot", "soft_reset_action": "Reinicialização suave", "clear_cache_action": "<PERSON><PERSON>", "smart_charging_action": "limitar a Carga", "more_actions_action": "<PERSON><PERSON>", "assign_sitearea_action": "Atribuir", "assign_sitearea_action_tooltip": "Atribuir Area de Site", "smart_charging": {"slider_power_disabled": "O limite não é suportado", "date_not_in_past": "A data de término não deve ser no passado", "date_out_of_limit": "O plano não deve ultrapassar 24 horas", "not_supported": "Esta funcionalidade não é compatível com esta marca de estação de carregamento", "smart_charging_enabled_static_limitation": "A limitação estática agora é gerida pelo carregamento inteligente", "charging_station_inactive": "A estação de carregamento não está conectada ao servidor", "smart_charging_enabled_charging_profiles": "Os planos de carregamento agora são geridos pelo carregamento inteligente", "static_limit": "Limitação Estática", "charging_profile_limit": "Plano de Carregamentos", "debug_charging_station": "Avançado", "debug_charging_station_title": "Recupere o plano de carregamento da estação de carregamento '{{chargeBoxID}}'", "reset_button": "Redefinir", "maximum_energy_limit": "Limite de potência de '{{chargeBoxID}}'", "invalid_min_duration": "A duração minima é {{minDuration}} min", "invalid_max_duration": "A duração máxima é {{maxDuration}} mins", "power_limit_title": "Limite de potência", "power_limit_confirm": "Você realmente deseja limitar o potencia de '{{chargeBoxID}}'?", "power_limit_success": "A potencia da estação de carregamento '{{chargeBoxID}}' foi limitada com suecesso", "power_limit_error": "Ocorreu um erro ao limitar a energia da estação de carregamento", "power_limit_has_charging_plan_title": "Plano de carregamento existente", "power_limit_has_charging_plan_confim": ["Há um conflito com um plano de cobrança existente!", "Você quer mudar o plano e ajustá-lo ao novo limite?"], "power_limit_plan_title": "Planear Limite de Potencia", "power_limit_plan_confirm": "Você realmente deseja limitar o potencia de'{{chargeBoxID}}' com este plano de carregamento?", "power_limit_plan_clear": "Você realmente deseja limpar o perfil de carregamento de'{{chargeBoxID}}'?", "power_limit_plan_success": "Perfis de carregamento para '{{chargeBoxID}}' foram aplicados com sucesso", "power_limit_plan_error": "Ocorreu um erro ao aplicar os perfis de carregamento", "power_limit_plan_not_accepted": "O perfil não foi aceito pela estação de carregamento '{{chargeBoxID}}'", "clear_profile_title": "<PERSON><PERSON> Carregamento", "clear_profile_confirm": "Você realmente deseja limpar TODOS os perfis de carregamento de'{{chargeBoxID}}'?", "clear_profile_success": "Perfis de carregamento da estação de carregamento '{{chargeBoxID}}' foram eliminados com sucesso", "clear_profile_error": "Ocorreu um erro ao limpar os perfis de carregamento", "clear_profile_not_accepted": "A solicitação não foi aceita pela estação de carregamento '{{chargeBoxID}}'.", "clear_profiles_button": "<PERSON><PERSON> per<PERSON>", "current_limit": "Planejamento de limite de potência real", "current_limit_title": "Limite de potência real", "limit_title": "Limite", "limit_in_amps": "({{limitInAmps}}A)", "limit_in_watts": "{{limitInWatts}}kW", "no_current_limit": "Limite não encontrado", "planning_energy_limit": "Planos de Carregamento", "from_date": "De", "to_date": "Para", "profile_type": "Tipo", "stack_level": "Level", "profile_id": "Perfil ID", "duration": "Duração", "duration_with_unit": "<PERSON><PERSON><PERSON> (mins)", "start_date": "<PERSON><PERSON>o", "end_date": "Fim", "add_schedule_button": "<PERSON><PERSON><PERSON><PERSON>", "remove_schedule_button": "Remover Cronograma", "schedule_start_header": "Iniciar data no Cronograma", "schedule_index_header": "Numero do Cronograma", "schedule_duration_header": "Duração/ões do Cronograma", "schedule_limit_header": "Cronograma de limite de potencia", "empty_schedule_list_error": "Voce deve criar pelo menos um cronograma", "connectors_all": "todos", "charging_profile_not_found": "Perfil de carregemento não foi encontrado", "profile_types": {"relative": "Relativo", "absolute": "Planeamento Temporário", "recurring_daily": "Planeamento Diário", "recurring_weekly": "Recorrente <PERSON>"}, "retrieve_schedule": "Reativação de Planos", "charging_schedule": "Planos de Carregamento de '{{chargeBoxID}}'", "enable_smart_charging_for_site_area_title": "Ativar Carreegamento Inteligente", "enable_smart_charging_for_site_area_body": ["Se você habilitar o carregamento inteligente para esta área do site, o algoritmo de otimização assumirá a limitação estática atual e os planos de carregamento.", "Você quer continuar?"], "disable_smart_charging_for_site_area_title": "Desativar carregamento inteligente", "disable_smart_charging_for_site_area_body": ["Se você desativar o carregamento inteligente para esta área de local, todos os planos de carregamento serão excluídos.", "Você quer continuar?"], "clearing_charging_profiles_not_successful_title": "Ocorreu um erro ao eliminar os planos de cobrança", "clearing_charging_profiles_not_successful_body": ["Falha ao excluir os planos de carregamento das estações de carregamento pertencentes à área do local '{{siteAreaName}}'!", "Verifique os registos e proceda manualmente com a exclusão do (s) plano (s) de carregamento."], "trigger_smart_charging_title": "Acionar carregamento inteligente", "trigger_smart_charging_confirm": "Você realmente deseja ativar o algoritmo de carregamento inteligente?", "trigger_smart_charging": "Ativar carregamento inteligenteg", "trigger_smart_charging_success": "O algoritmo de carregamento inteligente foi ativado com sucesso", "trigger_smart_charging_error": "Ocorreu um erro ao acionar o algoritmo de carregamento inteligente, verifique os registos", "minutes": "min(s)", "charging_plans": {"charging_plan_name": "Nome", "charging_station_id": "Estação de Carregamento", "connector_id": "Conetor ID", "current_limit": "Limite <PERSON>", "kind": "Tipo", "purpose": "Objetivo", "stack_level": "<PERSON>ível <PERSON>ha", "site_area": "Area de Site", "site_area_limit": "Limite Area do Site", "redirect": "Navigate to Charging Plans"}}, "more_actions": {"get_diagnostics_title": "Diagnostico de Carga '{{chargeBoxID}}'", "get_diagnostics_url": "URL para carregar ficheiro", "get_diagnostics_download_button": "Download", "get_diagnostics_dialog_title": "Recuperar diagn<PERSON><PERSON>", "get_diagnostics_dialog_confirm": "Você realmente deseja efetuar o download do ficheiro de diagnostico da estação de carregamento '{{chargeBoxID}}'?", "get_diagnostics_success": "Download de diagnóstico iniciado com sucesso", "get_diagnostics_error": "Ocorreu um erro ao iniciar o download de diagnóstico"}, "action_error": {"transaction_start_title": "Erro de inicio de sessão", "transaction_in_progress": "Uma sessão está em andamento, não é possível iniciar uma nova sessão", "no_active_transaction": "Não existem sessões ativas", "transaction_start_not_available": "O conector não está disponível, não é possível iniciar uma sessão", "transaction_start_charger_inactive": "O carregador não está disponível, não é possível iniciar uma sessão", "transaction_start_not_authorized": "Você não está autorizado a iniciar uma sessão", "transaction_stop_title": "Erro de inicio de sessão", "transaction_stop_not_authorized": "Você não está autorizado a interromper esta sessão", "transaction_stop_not_available": "O conector não está disponível, não é possível interromper uma sessão", "transaction_stop_charger_inactive": "O carregador não está disponível, não é possível interromper uma sessão", "delete_title": "Excluir erro de estação de carregamento", "delete_active_transaction": "A exclusão da estação de carregamento não é permitida quando uma sessão está em andamento", "command_title": "Erro de comando da estação de carregamento", "command_charger_disconnected": "A estação de carregamento não está disponível, nenhum comando pode ser enviado", "smart_charging_title": "Erro de carregamento inteligente", "smart_charging_charger_disconnected": "A estação de carregamento não está disponível, nenhum comando pode ser enviado", "smart_charging_charger_version": "O carregamento inteligente precisa de pelo menos a versão 1.6 OCPP na estação de carregamento", "session_details_title": "Erro de detalhes da sessão", "session_details_not_authorized": "Você não está autorizado a exibir os detalhes da sessão", "not_authorized": "Você não está autorizado a realizar esta ação", "ocpp_parameters_should_not_be_empty": "A chave e o valor do parâmetro OCPP não devem estar vazias", "ocpp_parameters_change_title": "Erro Parametro OCPP"}, "reboot_confirm": "Você realmente deseja reiniciar a estação de carregamento'{{chargeBoxID}}'?", "reboot_success": "A estação de carregamento '{{chargeBoxID}}' foi reiniciada com sucesso", "reboot_error": "Ocorreu um erro ao reiniciar a estação de carregamento", "retrieve_configuration_title": "Obter configuração", "retrieve_configuration_confirm": "Você realmente deseja recuperar a configuração da estação de carregamento '{{chargeBoxID}}'?", "reboot_required_title": "Reinicialização necessária", "reboot_required_confirm": "Você quer reiniciar a estação de carregamento '{{chargeBoxID}}'?", "ocpp_params_list_error": "Nenhum parâmetro OCPP para exibir", "ocpp_params_update_from_template_title": "Atualizar parâmetros OCPP", "ocpp_params_update_from_template_confirm": "Você realmente deseja atualizar os parâmetros OCPP da estação de carregamento '{{chargeBoxID}}' com o último modelo?", "ocpp_params_update_from_template_success": "Os parâmetros OCPP foram atualizados com sucesso a partir da estação de carregamento'{{chargeBoxID}}'", "ocpp_params_update_from_template_error": "Ocorreu um erro ao atualizar os parâmetros OCPP da estação de carregamento", "set_configuration_title": "Gravar a mudança de configuração", "set_configuration_confirm": "Você realmente deseja alterar o parâmetro '{{key}}' na estação de carregamento '{{chargeBoxID}}'?", "soft_reset_confirm": "Você realmente deseja redefinir a estação de carregamento '{{chargeBoxID}}'?", "soft_reset_success": "A estação de carregamento '{{chargeBoxID}}' foir reiniciada com sucesso", "soft_reset_error": "Ocorreu um erro ao reiniciar a estação de carregamento", "clear_cache_title": "<PERSON><PERSON>", "clear_cache_confirm": "Você realmente deseja limpar o cache da estação de carregamento'{{chargeBoxID}}'?", "clear_cache_success": "A cache da estação de carregamento '{{chargeBoxID}}' foi apagado com sucesso", "clear_cache_error": "Ocorreu um erro ao limpar o cache da estação de carregamento", "force_available_status_title": "Forçar status para disponível", "force_available_status_confirm": "Você realmente deseja alterar o status de todos os conectores da estação de carregamento '{{chargeBoxID}}' para disponívele?", "force_available_status_success": "O status dos conectores da estação de carregamento '{{chargeBoxID}}' foi mudado com sucesso", "force_available_status_error": "Ocorreu um erro ao alterar o status dos conectores da estação de carregamento", "force_unavailable_status_title": "Forçar status para indisponível", "force_unavailable_status_confirm": "Você realmente deseja alterar o status de todos os conectores da estação de carregamento '{{chargeBoxID}}' para indisponível?", "force_unavailable_status_success": "O status dos conectores da estação de carregamento '{{chargeBoxID}}' a mudança foi efetuada com sucesso", "force_unavailable_status_error": "Ocorreu um erro ao alterar o status dos conectores da estação de carregamento", "retrieve_config_success": "A configuração da estação de carregamento '{{chargeBoxID}}' foi recuperado com sucesso", "change_config_success": "A estação de carregamento '{{chargeBoxID}}' foi guardado com sucesso", "change_config_error": "Cannot change the configuration of the charging station, check the logs", "change_config_phase_error": "Não é possível atribuir estação de carregamento de três fases a uma área de local de fase única", "charge_point_connectors_error": "Error occurred while saving the charging station. The charge points properties do not match its connectors", "change_params_success": "O parametro '{{paramKey}}' foi atualizado com sucesso na estação de carregamento '{{chargeBoxID}}'", "change_params_error": "Ocorreu um erro ao tentar atualizar o parâmetro da estação de carregamento", "delete_title": "Eliminar", "delete_confirm": "Você realmente deseja excluir a estação de carregamento '{{chargeBoxID}}'?", "delete_success": "A estação de carregamento '{{chargeBoxID}}' foi excluída com sucesso", "delete_error": "Ocorreu um erro ao excluir a estação de carregamento", "properties_title": "<PERSON><PERSON><PERSON><PERSON>", "ocpp_parameters_title": "OCPP Parâmetros", "firmware_update_title": "Atualização Firmware", "update_firmware_title": "Atualização Firmware", "update_firmware_confirm": "Você realmente deseja atualizar o firmware para a estação de carregamento '{{chargeBoxID}}'?", "button_update_firmware": "Atualização", "update_firmware_error": "Ocorreu um erro ao tentar enviar o comando de atualização de firmware para a estação de carregamento", "update_firmware_success": "O pedido para atualizar o firmware da estação de carregamento '{{chargeBoxID}}' foi enviado com sucesso", "save_charger": "<PERSON><PERSON><PERSON>", "stop_transaction_title": "<PERSON><PERSON>", "stop_transaction_confirm": "Tem certeza de que deseja interromper a sessão da estação de carregamento '{{chargeBoxID}}'?", "stop_transaction_success": "A sessão da estação de carregamento '{{chargeBoxID}}' foi interrompida com sucesso", "stop_transaction_error": "Ocorreu um erro ao interromper a sessão", "stop_transaction_user_not_allowed": "Você não tem permissão para interromper esta sessão", "stop_transaction_missing_active_tag": "The session cannot be stopped on the charging station '{{chargeBoxID}}', you need to have at least one active RFID card", "start_transaction_title": "Iniciar", "start_transaction_button": "Initiar", "start_transaction_confirm": "Você tem a certeza de que deseja iniciar uma nova sessão para o utilizador '{{userName}}' na estação de carregamento '{{chargeBoxID}}'?", "start_transaction_success": "A sessão na estação de carregamento '{{chargeBoxID}}' foi iniciada com sucesso", "start_transaction_error": "Ocorreu um erro ao tentar iniciar uma nova sessão", "start_transaction_missing_active_tag": "A transação não pode ser iniciada para o usuário '{{userName}}' na estação de carregamento '{{chargeBoxID}}', o usuário precisa ter pelo menos um badge ativo", "start_transaction_user_not_allowed": "Você não tem permissão para iniciar uma sessão nesta estação de carregamento", "unlock_connector_title": "Desb<PERSON>que<PERSON>", "unlock_connector_button": "Desb<PERSON>que<PERSON>", "unlock_connector_confirm": "Deseja mesmo desbloquear o conector '{{connectorId}}' da estação de carregamento '{{chargeBoxID}}'?", "unlock_connector_success": "O conector '{{connectorId}}' da estação de carregamento '{{chargeBoxID}}' foi desbloqueado com sucesso", "unlock_connector_error": "The Charging Station has not been able to unlock the connector", "unlock_connector_not_supported_error": "This Charging Station does not support the unlock of the connector", "connector_type_unknown": "Não configurado", "connector_type_all": "All connectors", "connector_type_type2": "Tipo 2", "connector_type_type1": "Tipo 1", "connector_type_type1ccs": "Tipo 1 - Combo CCS", "connector_type_type3c": "Tipo 3C", "connector_type_domestic": "Domestico", "connector_type_combo": "Tipo 2 - Combo CCS", "connector_type_chademo": "CHAdeMO", "connector_type_gta": "GB T AC", "connector_type_gtd": "GB T DC", "connector_type_tesla": "Tesla", "direct_current": "<PERSON><PERSON><PERSON>", "alternating_current": "<PERSON><PERSON>nte <PERSON>", "direct_and_alternating_current": "DCorrente Direta e Alternada", "single_phase": "Fase Unica", "tri_phases": "Trifasica", "site_area": "Site Area", "assign_site_area": "Adicionar Area de Site", "change_site_area": "<PERSON><PERSON>", "site": "Site", "invalid_url": "URL Invalido", "invalid_power": "O valor da potência deve ser um número positivo", "invalid_voltage": "O valor da tensão deve ser um número positivo", "invalid_amperage": "O valor da amperagem deve ser um número positivo", "invalid_amperage_phases": "A amperagem deve ser divisível pelo número de fases", "unsaved_title": "Mudanças não gravadas", "unsaved_confirmed": "Você quer gravar as mud<PERSON><PERSON><PERSON>?", "changes_cancelled": "Mudanças canceladas", "unsaved_changes": "<PERSON><PERSON><PERSON> quer gravar as mud<PERSON><PERSON>. <PERSON><PERSON><PERSON> ou Cancelar.", "button_retrieve_configuration": "OCPP Retrieve Configuration", "button_force_ocpp_params_update_from_template": "Update OCPP Configuration from Template", "created_on": "Criar Data", "start_transaction_details_title": "Start session on charging station '{{chargeBoxID}}'", "start_transaction": "Start session", "power_limit_unit": "Conectar Unidade de Potência", "watt": "<PERSON>", "amper": "Ampere", "voltage": "Phase/Neutral Voltage (V)", "amperage": "Amperagem Total (A)", "amperagePerPhase": "Amperagem por fase (A)", "session_details": "Sessão", "qr_code_generation_error": "Error occurred while generating QR code", "tabs": {"list": "Estações de Carregamento", "charging_plans": "Planos de Carregamento", "in_error": "<PERSON>", "connection": "Onboard New Station", "evse": "Reservation", "evsee": "Reservation"}, "errors": {"missing_settings": {"title": "Configurações em Falta", "description": "As configurações necessárias são", "action": "<PERSON><PERSON> as configurações necessárias ausentes na configuração da estação de carregamento"}, "connection_broken": {"title": "Conexão perdida", "description": "O servidor não consegue alcançar a estação de carregamento", "action": "A intervenção manual é necessária. Verifique sua rede e / ou a configuração da estação de carregamento"}, "missing_site_area": {"title": "Não atribuído a uma área de site", "description": "A estação de carregamento deve ser atribuída a uma área local", "action": "Manter a área do local na configuração da estação de carregamento"}, "connector_error": {"title": "<PERSON><PERSON>", "description": "A estação de carregamento enviou um código de erro para um de seus conectores", "action": "Verifique os detalhes do código de erro e, eventualmente, reinicie a estação de carregamento"}}, "connections": {"registration_tokens_title": "Tokens de registro da estação de carregamento", "registration_token_site_area_name": "Token para a área do site '{{siteAreaName}}'", "registration_token_creation_title": "Criar token de registo", "registration_token_creation_confirm": "Você realmente deseja criar um token de registo para estações de carregamento desta área do site?", "registration_token_creation_success": "O token de registro foi criado com sucesso", "registration_token_creation_error": "Ocorreu um erro ao criar um token de registo", "registration_token_site_admin_creation_error": "You must provide a site linked to this registration token", "registration_token_revoke_title": "Revogar token de registo", "registration_token_revoke_confirm": "Você realmente deseja revogar o token de registo ?", "registration_token_revoke_success": "O token de registo foi revogado com sucesso", "registration_token_revoke_error": "Ocorreu um erro ao revogar um token de registo", "registration_token_update_title": "Update Registration Token", "registration_token_update_confirm": "Do you really want to update the registration token ?", "registration_token_update_success": "Registration token has been updated successfully", "registration_token_update_error": "Error occurred while updating a registration token, check the logs", "registration_token_not_found": "Registration token has not been found", "registration_token_delete_title": "Eliminar registo <PERSON>", "registration_token_delete_confirm": "Você realmente deseja excluir o token de registo ?", "registration_token_delete_success": "O token de registo foi excluído com sucesso", "registration_token_delete_error": "Ocorreu um erro ao excluir um token de registo", "registration_token_url": "Registar URL", "registration_token_valid": "Validar", "registration_token_expired": "<PERSON><PERSON><PERSON>", "registration_token_revoked": "Rev<PERSON><PERSON>", "registration_token_error": "Error occurred while retrieving the token, check the logs", "token_restrict_site_area": "Acesso retrito a Area de Site", "url": "OCPP URL", "copy_url_tooltip": "Copiar OCPP URL", "url_copied": "O URL OCPP foi copiado para sua área de transferência", "generate_connection_url": "Conecte uma estação de carregamento", "ocpp_15_soap": "OCPP 1.5 SOAP", "ocpp_16_json": "OCPP 1.6 JSON", "ocpp_16_soap": "OCPP 1.6 SOAP", "ocpp_15_soap_secure": "Secure OCPP 1.5 SOAP", "ocpp_16_json_secure": "Secure OCPP 1.6 JSON", "ocpp_16_soap_secure": "Secure OCPP 1.6 SOAP", "charging_station_shedules_delete_title": "Delete Scheduling", "charging_station_shedules_delete_confirm": "Do you really want to delete the Scheduling?", "charging_station_shedules_delete_success": "Scheduling has been deleted successfully", "charging_station_shedules_delete_error": "Error occurred while deleting a Scheduling, check the logs", "charging_station_shedules_creation_success": "Station schedule has been created successfully", "charging_station_shedules_creation_error": "Error occurred while creating a Station schedule, check the logs", "get_Stations_error": "Error at Stations fetching time", "charging_station_shedules_update_success": "Station schedule has been updated successfully", "charging_station_shedules_update_error": "Error occurred while updating a Station schedule, check the logs", "charging_station_shedules_not_found": "Station schedule has not been found"}}, "logs": {"logging_number": "Registo(s)", "debug": "Debug", "info": "Informação", "warning": "Aviso", "error": "Erro", "status": "Estados", "level": "Nível", "levels": "Níveis", "date": "Data", "source": "Source", "host": "Host", "process": "Processo", "user": "Utilizador", "users": "Uitlizadores", "on_user": "Ação do utilizador", "action": "Ações", "actions": "Ações", "type": "Tipo", "type_security": "Segurança", "type_regular": "Regular", "message": "Mensagem", "module": "<PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON>", "search_one_minute": "<PERSON><PERSON><PERSON>", "search_10_minutes": "Ultimos 10 minutos", "search_30_minutes": "Ultimos 30 minutos", "search_one_hour": "<PERSON><PERSON><PERSON>", "search_24_hours": "Last 24 hours", "search_today": "Hoje", "search_yesterday": "Ontem", "search_this_week": "<PERSON><PERSON>", "search_last_week": "<PERSON><PERSON><PERSON>", "search_this_month": "<PERSON><PERSON> mês", "search_last_month": "<PERSON><PERSON><PERSON>", "select_actions": "Selecionar Ações", "redirect": "Verificar Registos", "no_logs": "Não existem registos que correspondem com o filtros", "dialog": {"export": {"title": "Exportar Registos", "confirm": "Você realmente deseja exportar todos os registros que correspondem aos filtros?", "error": "Ocorreu um erro ao tentar exportar os registos"}}}, "users": {"id": "Id", "tabs": {"list": "Utilizadores", "in_error": "Erro", "tags": "Badge", "billing": "Billing"}, "security": "Security", "import_users": "Import users", "import_users_message": "The import will discard all other previously uploaded users not yet imported in the database, do you want to continue?", "import_users_message_auto_activate": "The import will discard all other previously uploaded users not yet imported in the database and users will be automatically activated, do you want to continue?", "import_users_auto_activation": "Automatically activate new users", "import_users_success": "{{inSuccess}} users have been uploaded successfully and will be processed asynchronously", "import_users_partial": "{{inSuccess}} users have been uploaded successfully and will be processed asynchronously and {{inError}} failed, check the logs", "import_users_error": "{{inError}} users failed to be uploaded", "import_no_users": "No user have been uploaded", "access_mode": "Access Mode", "allow_free_access": "Free access to all charging stations", "user_with_freeAccess": "Free access", "user_without_freeAccess": "Paying access", "number_of_sites": "Nb Sites", "number_of_transactions": "Nb Sessões", "eula_accepted_on": "EULA Aceite em", "edit_profile_short": "EP", "wallet": "Wallet", "razorpay_wallet": "Razorpay Wallet", "virtual_wallet": "Virtual Wallet", "profile": "Perfil", "edit_profile": "<PERSON><PERSON>", "user_number": "U<PERSON><PERSON><PERSON>(s)", "technical_user": "API User", "non_technical_user": "Normal User", "role": "Função", "roles": "Funções", "role_admin": "Admin", "role_comp_admin": "Administrador Corporativo", "role_super_admin": "Super Admin", "role_basic": "Basico", "role_demo": "Demonstração", "role_emp": "Employee", "company": "Company", "role_company_admin": "Company Admin", "role_transport_admin": "Transport Admin", "role_transport_manager": "Transport Manager", "role_invalid": "Função Invalida", "role_mult_all": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "role_mult_admin_demo": "Admin or Demo", "user_not_found": "O utilizador não foi encontrado", "user_id_not_found": "Utilizador '{{userId}}' não foi encontrado", "invalid_phone_number": "Numero de telefone Invalido", "missing_tag": "Esta lista deve conter pelo menos um badge", "invalid_tag_id": "Somente números e letras são permitidos", "invalid_plate_id": "Apenas letras maiús<PERSON>s, números e '-' são permitidos", "locale": "Linguagem", "locale_invalid": "Local inválido", "locale_desc_en_US": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locale_desc_fr_FR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locale_desc_es_ES": "Espanhol", "locale_desc_de_DE": "Alemão", "locale_desc_pt_PT": "Português", "locale_desc_it_IT": "Italiano", "locale_desc_cs_CZ": "Czech", "locale_desc_en_AU": "Australian", "role_unknown": "Desconhecido", "locale_unknown": "Desconhecido", "status": "Esatdos", "picture": "Imagem", "show_image": "Mostrar Imagens", "sites": "Sites", "assigned_sites_to_user": "Sites atríbuidos a {{userName}}", "status_invalid": "Estado inválido", "status_active": "Ativo", "status_blocked": "Suspenso", "status_inactive": "Inativo", "status_locked": "Bloqueado", "status_pending": "Pendente", "status_unknown": "Desconhecido", "email": "Email", "name": "Nome", "first_name": "Primeiro Nome", "plate_id": "Plate", "technical_title": "API User", "technical": "API User (used for backend to backend communications only)", "tags": "Badges", "tag_ids_help": "Insira uma lista de IDs de badge separados por uma vírgula", "inumber": "Número Corporativo", "cost_center": "Centro de Custos", "phone": "Telefone", "mobile": "Telemóvel", "notifications_active": "Habilite as notifica<PERSON><PERSON><PERSON> (e-mail, telefone telemóvel)", "created_on": "C<PERSON><PERSON> em", "created_by": "<PERSON><PERSON><PERSON> por", "changed_on": "Mudado em", "changed_by": "Mudado por", "save_profile": "<PERSON><PERSON><PERSON>", "save_address": "<PERSON><PERSON><PERSON>", "save_password": "Gravar Password", "save_miscs": "<PERSON><PERSON><PERSON>", "miscs": "Diversos", "email_already_used": "O e ja está a ser utilizado por outro utilizador", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "delete_title": "Eliminar Utilizador", "delete_button": "Eliminar", "delete_confirm": "Tem certeza de que deseja excluir o usuário '{{userFullName}}'?", "delete_success": "O usuário '{{userFullName}}' foi excluído com sucesso", "delete_billing_error": "Ocorreu um erro ao excluir o usuário devido ao sistema de faturamento, verifique os logs", "delete_error": "Ocorreu um erro ao excluir o usuário", "remove_sites_title": "Remover Site(s)", "remove_sites_confirm": "Tem certeza de que deseja remover o (s) site (s) selecionado (s) deste utilizador?", "remove_sites_success": "Os sites foram removidos com sucesso", "remove_sites_error": "Ocorreu um erro ao remover o (s) site (s) do utilizador", "export_users_title": "Export Users", "export_users_confirm": "Do you really want to export the User list to a csv format?", "export_users_error": "Error occurred while trying to export the Users", "update_success": "O utilizador '{{userFullName}}' foi atualizado com sucesso", "update_sites_success": "Os sites foram adicionados com sucesso", "changed_by_other_user": "O utilizador '{{userFullName}}' foi alterado por outro usuário e será recarregado", "update_error": "Ocorreu um erro ao atualizar o utilizador", "user_do_not_exist": "Utilizador não existe", "title": "Utilizador", "picture_size_error": "O limite de tamanho da imagem foi excedido, o limite está abaixo {{maxPictureKb}} Kb", "create_success": "O utilizador {{userFullName}} foi criado com sucesso", "create_error": "Ocorreu um erro ao criar o utilizador", "no_users": "Utilizador não encontrado", "select_users": "Selecionar Utilizador", "update_set_site_admin_success": "O utilizador agora é administrador do site '{{siteName}}'", "update_remove_site_admin_success": "O utilizador não é mais administrador do site '{{siteName}}'", "update_site_admin_role_error": "Ocorreu um erro ao tentar atualizar o direito do utilizador no site '{{siteName}}'", "update_set_site_owner_success": "O utilizador agora é o proprietário do site '{{siteName}}", "update_remove_site_owner_success": "O utilizador não é mais proprietário do site '{{siteName}}'", "update_site_owner_role_error": "Ocorreu um erro ao tentar atualizar o direito do utilizador no site '{{siteName}}'", "redirect": "Navigate to User", "display_sites": "Display sites", "connectors": {"connect": "Conectado", "revoke": "<PERSON><PERSON><PERSON>", "created_on": "C<PERSON><PERSON> em", "expired_on": "<PERSON><PERSON><PERSON> em", "not_connected": "<PERSON><PERSON>"}, "invoicing": {"errors": {"no_invoice_found": "Nenhuma fatura encontrada para a sua conta", "unable_to_get_invoice": "Ocorreu um erro ao recuperar sua fatura"}}, "errors": {"inactive_user": {"title": "Utilizadores Inativos", "description": "Este utilizador está inativo.", "action": "Verificar o estado deste utilizador."}, "unassigned_user": {"title": "Não atribuído a um site", "description": "Este utilizador não está atribuído a um site.", "action": "Atribuir este utilizador a pelo menos um site, pois o componente da organização está ativo."}, "inactive_user_account": {"title": "Conta de utilizador inativa", "description": "Este utilizador não faz login há pelo menos 6 meses.", "action": "Exclua ou desative esta conta de utilizador."}, "failed_billing_synchro": {"title": "A sincronização de fatura falhou", "description": "A sincronização deste utilizador com o sistema de fatura falhou.", "action": "Sincronize novamente manualmente."}}, "export_tags_title": "Export Tags", "export_tags_confirm": "Do you really want to export the Tag list to a CSV format?", "export_tags_error": "Error occurred while trying to export the Tags, check the logs"}, "invoices": {"tabs": {"list": "<PERSON><PERSON>"}, "tooltips": {"download": "Download de faturas", "pay": "<PERSON><PERSON> fatura"}, "status": {"paid": "Pago", "unpaid": "<PERSON><PERSON>", "draft": "Esboço", "deleted": "Deleted", "uncollectible": "Uncollectible", "void": "Cancelled"}, "number": "Numero de fatura", "createdOn": "Data da fatura", "price": "Preço da fatura", "amount": "Valor", "tax": "Tax", "taxedAmount": "Taxed Amount", "cannot_retrieve_invoices": "Não é possível recuperar a lista de faturas", "cannot_download_invoice": "Não é possível baixar a fatura", "number_of_items": "Sessões", "pay": "Pago", "user": "Utilizador", "failed_download": "Invoice download failed."}, "transfers": {"tabs": {"list": "Transfers"}, "tooltips": {"download": "Download commission invoice", "finalize": "Finalize transfer", "send": "Send transfer"}, "status": {"draft": "Draft", "pending": "Pending", "finalized": "Finalized", "transferred": "Transferred"}, "id": "ID", "accountExternalID": "Connected Account ID", "collected_funds": "Collected Funds", "document_number": "Invoice", "platform_fee_amount": "Collected <PERSON><PERSON>", "platform_fee_tax_inclusive": "Fees (incl. taxes)", "transferAmount": "Transfer", "number_of_items": "Sessions", "transferExternalID": "External ID", "accountOwner": "Business Owner", "cannot_finalize_transfer": "Operation failed - The transfer could not be finalized", "cannot_send_transfer": "Operation failed - The transfer has not been sent to the billing system", "cannot_download_commission_incoice": "Cannot download commission invoice"}, "tags": {"id": "Tag ID", "not_active": "This badge is not active", "issuer": "Dono da Organização", "visual_id": "Visual Tag ID", "visual_id_instruction": "Please fill the ID that is displayed on the badge", "status": "Estados", "activated": "Active", "deactivated": "Inactive", "sessions": "Sessões", "activate_title": "Ativação de Badge", "activate_confirm": "Tem certeza de que deseja ativar o emblema '{{tagID}}'? Uma vez ativado, este emblema poderá ser usado para iniciar as sessões.", "deactivate_title": "Desativação de Badge", "deactivate_confirm": "Tem certeza de que deseja desativar o emblema '{{tagID}}'? Uma vez desativado, este emblema não poderá mais ser usado para iniciar as sessões.", "deactivate_success": "O badge '{{tagID}}' foi desativado com sucesso", "deactivate_error": "Ocorreu um erro ao desativar o badge", "activate_success": "O badge '{{tagID}}' foi ativado com sucesso", "activate_error": "Ocorreu um erro ao ativar o badge", "delete_confirm": "Tem certeza de que deseja excluir o badge '{{id}}'?", "delete_title": "Apagar badge", "delete_success": "O badge '{{id}}' foi excluído com sucesso", "delete_error": "O<PERSON><PERSON>u um error ao eliminar o badge", "create_success": "O badge '{{tagID}}' foi criado com sucesso", "create_error": "Ocorreu um erro ao criar o badge", "register_success": "The badge '{{visualID}}' has been registered successfully", "register_error": "Error occurred while registering the badge", "update_success": "O badge '{{tagID}}' foi atualizado com sucesso", "update_by_visual_id_success": "The badge '{{visualID}}' has been updated successfully", "update_error": "Ocorreu um erro ao atualizar o badge", "tag_error": "Ocorreu um erro ao recuperar o badge", "tag_not_found": "O badge não foi encontrado", "select_tags": "Selecionar Tag", "tag_id_already_used": "O badge '{{tagID}}' já existe", "tag_visual_id_already_used": "The visualID '{{visualID}}' already exists", "tag_visual_id_does_not_match_tag": "The visualID '{{visualID}}' does not match any badge", "tag_inactive": "The badge '{{visualID}}' is inactive and cannot be assigned", "redirect": "Navegue até as tags", "title": "Badge", "default_tag": "Badge padr<PERSON>", "delete_tags_title": "Delete Badge(s)", "delete_tags_confirm": "Do you really want to delete '{{quantity}}' badge(s)?", "delete_tags_success": "{{inSuccess}} badges have been deleted successfully", "delete_tags_partial": "{{inSuccess}} badges have been deleted successfully and {{inError}} badges have encountered an error, check the logs", "delete_tags_error": "{{inError}} badges failed to be deleted, check the logs", "delete_tags_unexpected_error": "Unexpected error occurred while deleting the badges, check the logs", "delete_no_tag": "No badge has been deleted", "import_tags": "Import Badges", "import_tags_message": "The import will discard all other previously uploaded badges not yet imported in the database, do you want to continue?", "import_tags_message_auto_activate": "The import will discard all other previously uploaded badges not yet imported in the database, do you want to continue?", "import_tags_success": "{{inSuccess}} badges have been uploaded successfully and will be processed asynchronously", "import_tags_partial": "{{inSuccess}} badges have been uploaded successfully and will be processed asynchronously and {{inError}} failed, check the logs", "import_tags_error": "{{inError}} badges failed to be uploaded", "import_no_tags": "No badge have been uploaded", "import_tags_auto_activation": "Automatically activate new tags"}, "tenants": {"title": "Organização", "name": "Nome", "logo": "Logo", "tenant_management": "Gestão da Organização", "tenant_not_found": "Esta organziação não foi encontrada", "email": "Email", "subdomain": "Subdominio", "create_success": "A organização '{{name}}' foi criada com sucesso", "create_error": "Ocorreu um erro ao criar a organização", "subdomain_already_used": "O subdomínio '{{subdomain}}' já está em uso", "update_error": "Ocorreu um erro ao atualizar a organização", "error_subdomain": "Somente letras minúsculas e números são permitidos", "update_success": "A organização '{{name}}' foi atualizada com sucesso", "redirectWebsiteUrl": "Redirect Website URL", "delete_confirm": "Tem certeza de que deseja excluir a organização '{{name}}'?", "delete_title": "Excluir Organização", "delete_success": "A organização '{{name}}' foi excluída com sucesso", "delete_error": "Ocorreu um erro ao excluir a organização", "save_error_refund": "O preço deve estar ativo para usar o reembolso", "save_error_roaming": "You cannot activate both the OCPI and OICP roaming components", "save_error_billing": "O preço deve estar ativo para usar na fatura", "save_error_billing_platform": "Billing must be active to use Billing Platform", "save_error_smart_charging": "A organização deve estar ativa para usar o Carregamento Inteligente", "save_error_asset": "A organização deve estar ativa para usar o ativo", "save_error_car_connector": "Car Management must be active to use Car Connector", "smart_charging_still_active_for_site_area": ["O Carregamento inteligente Charging ainda está ativo para determinadas áreas do site.", " Desative o Carregamento Inteligente para cada área do site no locatário."], "logo_size_error": "O limite de tamanho do logotipo foi excedido, o limite está abaixo de {{maxPictureKb}} Kb"}, "accounts": {"title": "Connected Accounts", "list": {"select_account": "Select Connected Account", "add_account": "Add Connected Account", "business_owner": "Business Owner", "company_name": "Company Name", "account_status": "Status", "account_id": "Connected Account ID", "billing_account": "Connected Account"}, "message": {"create_success": "Account was added successfully", "create_error": "Error occurred while adding the account, check the logs", "onboard_success": "The onboarding process has been triggered. A mail has been sent to the business owner", "onboard_error": "Error occurred while onboarding the account, check the logs"}, "status": {"account_idle": "Idle", "account_pending": "Pending", "account_active": "Active"}, "onboarding": {"onboard_action": "Send the onboarding email to the business owner", "onboarding_title": "Open e-Mobility - Account Onboarding", "onboarding_message": "You have been invited to onboard your company as an account connected to the Open e-Mobility platform.", "onboarding_message_to_proceed": "Click the link below to complete the onboarding process.", "onboarding_congratulations": "Congratulations", "onboarding_process_completed": "The onboarding of your account has been completed.", "onboarding_button_proceed": "Proceed", "onboarding_navigate_to_dashboard": "Navigate to the dashboard", "onboarding_process_failed": "The activation of your account has failed - contact the support to get help!"}, "platform_fee": {"platform_fee_group_title": "Platform Fee", "platform_flat_fee": "Flat fee per session", "platform_flat_fee_invalid": "Flat fee entered must be a positive number", "platform_fee_percentage": "Fee percentage per session", "platform_fee_percentage_invalid": "Fee percentage must be between 0% to 100%"}}, "settings": {"settings_not_saved_title": "Configurações não gravadas", "settings_not_saved": "Você deve gravar suas configurações primeiro", "tabs": {"ocpi": "Roaming", "oicp": "Roaming Hubject", "refund": "<PERSON><PERSON>bol<PERSON><PERSON>", "pricing": "Preço", "billing": "Faturação", "analytics": "<PERSON><PERSON><PERSON>", "sms": "SMS", "smart_charging": "Carregamento Inteligente", "asset": "Ativos", "car_connector": "Car Connector", "taxes": "Taxes", "smtp": "SMTP"}, "ocpi": {"gireve": {"title": "<PERSON><PERSON><PERSON>"}, "setting_do_not_exist": "OCPI Configurações não encontradas", "setting_not_found": "OCPI Configurações não encontradas", "create_success": "OCPI Configurações criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações OCPI", "update_success": "As configurações OCPI foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações OCPI", "description": ["E-Mobility fornece um serviço certificado para se conectar a uma plataforma E-Roaming externa.", "Este recurso expõe em tempo real a infraestrutura da estação de carregamento e a disponibilidade por meio do protocolo padrão OCPI (Open Charge Point Interface).", "E-Mobility está atualmente usando a Gireve Inter-Operability Platform (IOP)."]}, "oicp": {"hubject": {"title": "Hubject"}, "setting_do_not_exist": "OICP Settings not found", "setting_not_found": "OICP settings has not been found", "create_success": "OICP settings have been created successfully", "create_error": "Error occurred while creating the OICP settings, check the logs", "update_success": "OICP settings have been updated successfully", "update_error": "Error occurred while updating the OICP settings, check the logs", "description": ["E-Mobility provides a certified service to connect to an external E-Roaming Platform.", "This feature exposes real-time the charging station infrastructure and availability via the OICP protocol (Open InterCharge Protocol).", "E-Mobility is currently using the Hubject B2B Service Platform (HBS)."]}, "refund": {"description": ["O E-Mobility fornece um serviço de reembolso para um sistema de despesas externas.", "Este recurso permite que o utilizador envie suas sessões para um sistema de despesas e seja reembolsado pela eletricidade que consumiu.", "O E-Mobility está usando o SAP Concur. Mais informações técnicas disponíveis em"], "not_found": "Configurações de reembolso não encontradas", "create_success": "As configurações de reembolso foram criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações de Reembolso", "update_success": "As configurações de reembolso foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações de reembolso", "synchronize_dialog_refund_title": "Sincronizar transações reembolsadas", "synchronize_dialog_refund_confirm": "Você realmente deseja sincronizar as transações reembolsadas ?", "synchronize_started": "A sincronização das transações reembolsadas foi iniciada", "synchronize_success": "As transações reembolsadas foram sincronizadas com sucesso", "synchronize_error": "Ocorreu um erro ao sincronizar as transações reembolsadas", "connection_error": "Error in connecting to the refunding system, check your connection in your profile", "concur": {"title": "SAP Concur", "authentication_url": "Url de Autenticação", "app_url": "Url da Aplicação", "api_url": "API Url", "client_id": "Cliente ID", "client_secret": "Client Secret", "payment_type_id": "ID Tipo de Pagamento", "expense_type_code": "Codigo do tipo de Despesa", "policy_id": "Policy ID", "report_name": "Nome do Relatório", "link_success": "Sua conta foi vinculada com sucesso ao Concur", "link_error": "Ocorreu um erro ao vincular sua conta ao Concur", "revoke_success": "Sua conexão com o Concur foi revogada com sucesso", "revoke_error": "Ocorreu um erro ao revogar sua conexão com o Concur"}}, "pricing": {"title": "Pricing", "description": "A interface de preços permite recursos de preços para fornecedores de estações de carregamento.", "not_found": "Configurações de preços não encontradas", "create_success": "As configurações de preços foram criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações de preços", "update_success": "As configurações de preços foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações de preços", "simple_pricing_title": "Built-in Pricing Module", "full_description": "Open E-Mobility provides a pricing module which allows a flexible definition of your company's pricing strategy.", "pricing_currency_changed_title": "Modification of the currency", "pricing_currency_changed_confirm": ["You are about to change the currency. This will force you to reconnect after saving.", "<strong>WARNING !</strong>Once saved, the currency cannot be modified anymore!", "Do you confirm that the selected value is correct?"], "settings": {"title": "Pricing Settings", "price": "Preço", "currency": "<PERSON><PERSON>"}, "end_date_error": "The date must be greater than the valid-from date", "valid_from": "<PERSON><PERSON>", "valid_to": "<PERSON><PERSON>", "flat_fee": "Flat Fee", "energy": "Energy", "charging_time": "Charging Time", "parking_time": "Parking Time", "create_title": "Create Pricing Definition", "pricing_definition_description": "Description", "pricing_definition_name": "Pricing Definition Name", "pricing_definition_creation_confirm": "Do you really want to create a pricing definition?", "pricing_definition_creation_success": "Pricing definition has been created successfully", "pricing_definition_creation_error": "Error occurred while creating a pricing definition, check the logs", "pricing_definition_site_admin_creation_error": "You must provide a site linked to this pricing definition", "pricing_definition_update_title": "Update Pricing definition", "pricing_definition_update_confirm": "Do you really want to update the pricing definition?", "pricing_definition_update_success": "Pricing definition has been updated successfully", "pricing_definition_update_error": "Error occurred while updating a pricing definition, check the logs", "pricing_definition_not_found": "Pricing definition has not been found", "pricing_definition_delete_title": "Delete Pricing Definition", "pricing_definition_delete_confirm": "Do you really want to delete the pricing definition?", "pricing_definition_delete_success": "Pricing definition has been deleted successfully", "pricing_definition_delete_error": "Error occurred while deleting a pricing definition, check the logs", "pricing_definition_error": "Error occurred while retrieving the pricing definition, check the logs", "pricing_definition_time_range_error": "Start and end time must be different", "pricing_max_energy_error": "Maximum energy must be greater than the minimum energy", "pricing_max_duration_error": "Maximum duration must be greater than the minimum duration", "connector_type": "Connector Type", "connector_power": "Connector Power", "value": "Value", "unit": "Unit", "charging_station": "Charging Station", "pricing_dimensions_title": "Dimensions", "pricing_definition_title": "Pricing Definition", "step_size": "Step Size", "connector_power_unit": "kW", "flat_fee_unit": "{{currency}}/session", "energy_unit": "{{currency}}/kWh", "charging_time_unit": "{{currency}}/hour", "parking_time_unit": "{{currency}}/hour", "flat_fee_formatted_price": "{{price}}/session", "energy_formatted_price": "{{price}}/kWh", "charging_time_formatted_price": "{{price}}/hour", "parking_time_formatted_price": "{{price}}/hour", "energy_step_unit": "Wh", "time_step_unit": "Minutes", "restrictions_title": "Restrictions", "restriction_max_energy": "Max Energy", "restriction_max_energy_unit": "kWh", "restriction_min_energy": "Min Energy", "restriction_min_energy_unit": "kWh", "restriction_max_duration": "Max duration", "restriction_max_duration_unit": "Minutes", "restriction_min_duration": "Min duration", "restriction_min_duration_unit": "Minutes", "restriction_time_range": "Time range", "restriction_start_time": "Start time", "restriction_end_time": "End time", "restriction_days_of_week": "Days of week"}, "billing": {"description": "A interface de faturamento permite recursos de faturamento para fornecedores de estação de carregamento.", "deactivated_setting_message": "The billing of the charging sessions is active, the connection settings cannot be changed anymore.", "not_found": "Configurações de faturamento não encontradas", "not_properly_set": "Billing settings not properly set", "create_success": "As configurações de faturamento foram criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações de faturamento", "update_success": "As configurações de faturamento foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações de faturamento", "check_connection": "Verifique a conexão", "connection_success": "A conexão com o sistema de faturamento foi testada com sucesso", "connection_error": "Impossível conectar ao sistema de faturamento. Verifique sua chave secreta.", "force_synchronize": "Forçar sincronização de faturamento", "payment_methods_create_title": "Create a payment method", "payment_methods_create_success": "The payment method ending with '{{last4}}' has been created successfully", "payment_methods_create_error": "Error occurred while creating the payment method, check the logs", "payment_methods_create_error_card_declined": "Your card has been declined, please provide a valid payment method", "payment_methods_card_declined": "Card declined", "payment_methods_delete_title": "Delete payment method", "payment_methods_delete_confirm": "Do you really want to delete the payment method ending with '{{last4}}'?", "payment_methods_delete_success": "The payment method ending with '{{last4}}' has been deleted successfully", "payment_methods_delete_error": "Error occurred while deleting the payment method, check the logs", "payment_methods_expired": "Expired", "payment_methods_valid": "<PERSON><PERSON>", "payment_methods_expire_soon": "Expires soon", "payment_methods_expiring_on": "Expires on", "payment_methods_ending_with": "Ending with", "payment_methods_status": "Status", "payment_methods_type": "Type", "payment_methods_brand": "Brand", "payment_methods_card": "Card", "payment_methods_verification_code": "Verification code", "payment_methods_conditions": "For security reasons, we do not store nor process your bank and card details directly. The collected information is sent via a secure channel to our STRIPE partner which provides the billing and payment infrastructure.", "payment_methods_check": "I have read the End-user License Agreement and agree that my bank details will be used for paying future EV Charging Sessions.", "payment_methods_card_number_error": "Invalid card number", "payment_methods_expiration_date_error": "Invalid expiration date", "payment_methods_cvc_error": "Invalid verification code", "transaction_billing_activation": "Billing Activation", "transaction_billing_activation_title": "Billing Activation", "transaction_billing_activation_confirm": ["Do you really want to activate the billing of charging sessions?", "<strong> WARNING ! </strong> Once activated, you will NOT be able to deactivate it!"], "transaction_billing_activation_success": "Billing of charging sessions has been successfully activated", "transaction_billing_activation_error": "Billing of charging sessions cannot be activated", "billing_clear_test_data": "Delete Test Data", "billing_clear_test_data_title": "Test Data Deletion", "billing_clear_test_data_confirm": "Do you really want to delete all billing test data?", "billing_clear_test_data_success": "The test data has been deleted", "billing_clear_test_data_error": "Operation failed. The test data deletion has been aborted", "payment_methods": "Payment Methods", "user": {"force_synchronize_user_dialog_title": "Sincronizar utilizador de cobrança", "force_synchronize_user_dialog_confirm": "Você realmente deseja sincronizar o utilizador '{{userFullName}}' no sistema de faturamento?", "force_synchronize_user_success": "O utilizador '{{userFullName}}' foi sincronizado com sucesso", "force_synchronize_user_failure": "Falha ao sincronizar o utilizador, verifique os registos"}, "stripe": {"description": "E-Mobility permite enviar faturas para os usuários da estação de carregamento com Stripe ...", "title": "Stripe", "url": "URL do Stripe Dashboard", "public_key": "Chave de API publicável", "invalid_public_key": "A chave de API publicável não é plausível", "secret_key": "Chave secreta de API", "invalid_secret_key": "A chave secreta da API não é plausível", "billing_method": "Método de cobrança", "immediate_billing": "Faturamento Imediato", "periodic_billing": "Faturamento periódico", "tax": "Imposto", "platform_fee_tax": "Platform Fee Tax", "no_tax": "<PERSON><PERSON>"}, "tap": {"title": "tap", "url": "URL de su cueanta en Tap", "public_key_web": "Publishable API Key Web", "invalid_public_key_web": "Publishable API key Web is not plausible", "public_key": "<PERSON><PERSON>e publicable", "invalid_public_key": "La clave publicable no es plausible", "secret_key": "Clave secreta", "invalid_secret_key": "La clave secreta no es plausible", "billing_method": "Método de facturación", "immediate_billing": "Facturación inmediata", "periodic_billing": "Facturación periódica", "tax": "Impuesto", "platform_fee_tax": "Platform Fee Tax", "no_tax": "Sin impuestos"}}, "analytics": {"sac": {"title": "SAP Analytics", "settings": "Configurações"}, "description": ["A E-Mobility expõe seus dados a ferramentas analíticas externas.", "Este recurso expõe em tempo real todos os dados da sessão por meio de um conector para ferramentas analíticas de dados externas.", "A E-Mobility está usando o SAP Analytics Cloud por meio de um conector OData.", "SAP Analytics Cloud suporta o uso inteligente de dados de cobrança para simplificar a tomada de decisões operacionais.", "Mais informações técnicas disponíveis em"], "setting_do_not_exist": "A configuração não foi encontrada", "setting_not_found": "As configurações do SAP Analytics não foram encontradas", "create_success": "As configurações do SAP Analytics Cloud foram criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações do SAP Analytics Cloud", "update_success": "As configurações do SAP Analytics Cloud foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações do SAP Analytics Cloud"}, "smart_charging": {"sap_smart_charging": {"title": "Carregamento Inteligente", "settings": "Configurações", "user": "Utilizador", "password": "Password", "additional_settings": "Additional settings:", "sticky_limitation": "Adjust limit according to current consumption", "limit_buffer_dc": "Buffer for Limit DC (%)", "limit_buffer_ac": "Buffer for Limit AC (%)"}, "description": "E-Mobility pode usar algoritmo de carregamento inteligente para otimizar a distribuição de energia e horários de carregamento com base em restrições complexas", "setting_do_not_exist": "A configuração não foi encontrada", "setting_not_found": "As configurações do SAP Carregamento Inteligente não foram encontradas", "create_success": "As configurações do SAP Carregamento Inteligente foram criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações do SAP Smart Charging", "update_success": "As configurações do SAP Carregamento Inteligente foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações do SAP Smart Charging", "connection_success": "A conexão com o serviço de carregamento inteligente foi testada com sucesso", "connection_error": "Impossível conectar ao serviço de carregamento inteligente. Verifique sua configuração.", "check_connection": "Verifique a conexão"}, "asset": {"connection": {"title": "Conexões de ativos", "name": "Nome", "description": "Descrição", "type": "Tipo", "refreshIntervalMins": "Refresh <PERSON> (mins)", "base_url": "URL", "user": "Utilizador", "password": "Password", "client_id": "Client ID", "client_secret": "Client Secret", "authentication_url": "Authentication URL", "delete_title": "Excluir conexão de ativo", "delete_confirm": "Tem certeza de que deseja excluir a conexão '{{assetConnectionName}}'?"}, "types": {"schneider": "Schneider Building Management System", "greencom": "GreenCom Asset Management System", "iothink": "ioThink Asset Management System", "wit": "WIT Asset Management System", "lacroix": "LACROIX Asset Management System"}, "connection_success": "A conexão com o sistema foi testada com sucesso", "unknown_connection_error": "Erro de conexão desconhecido", "invalid_grant": "U<PERSON><PERSON><PERSON> ou senha incorreta", "connection_failed": "Conexão falhou", "description": "O E-Mobility pode gerenciar o consumo de seus ativos e usar essas informações com o algoritmo de carregamento inteligente para equilibrar os consumos da estação de carregamento para uma determinada área do local.", "setting_do_not_exist": "A configuração não foi encontrada", "setting_not_found": "As configurações de ativos não foram encontradas", "create_success": "As configurações do ativo foram criadas com sucesso", "create_error": "Ocorreu um erro ao criar as configurações do ativo", "update_success": "As configurações do ativo foram atualizadas com sucesso", "update_error": "Ocorreu um erro ao atualizar as configurações do ativo"}, "car_connector": {"connection": {"title": "Car connectors", "name": "Name", "description": "Description", "type": "Type", "authentication_url": "Authentication Url", "api_url": "API Url", "client_id": "Client ID", "client_secret": "Client Secret", "delete_title": "Delete Connector", "delete_confirm": "Do you really want to delete the connector '{{carConnectorConnectionName}}'?"}, "types": {"mercedes": "Mercedes Connector", "tronity": "Tronity Connector", "targa_telematics": "Targa Telematics Connector"}, "mercedes": {"link_success": "Your account has been successfully linked with Mercedes", "link_error": "Error occurred while linking your account with Mercedes, check the logs", "revoke_success": "Your connection with Mercedes has been successfully revoked", "revoke_error": "Error occurred while revoking your connection with Mercedes, check the logs"}, "description": "E-Mobility can get real time data of your cars like the current state of charge and use this information with the smart charging algorithm to optimize charging sessions.", "setting_do_not_exist": "Configuration has not been found", "setting_not_found": "Car connector settings have not been found", "create_success": "Car connector settings have been created successfully", "create_error": "Error occurred while creating the Car connector settings, check the logs", "update_success": "Car connector settings have been updated successfully", "update_error": "Error occurred while updating the Car connector settings, check the logs"}, "taxes": {"title": "Taxes", "name": "Name", "rate": "Rate", "status": "Status", "api_url": "API Url", "taxes_not_found": "Taxes not found", "create_success": "The Tax '{{name}}' has been created successfully", "create_error": "Error occurred while creating the Tax, check the logs", "update_success": "The Tax '{{name}}' has been updated successfully", "update_error": "Error occurred while updating the Tax, check the logs", "delete_title": "Delete Tax", "delete_confirm": "Do you really want to delete the Tax '{{name}}'?", "delete_success": "The Tax '{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Tax, check the logs"}, "information": "Informação", "activation_contact_msg": "Para ativar esse recurso, entre em contato com seu representante E-Mobility.", "car": {"synchronize_car_catalogs": "Sincronizar carros", "synchronize_car_catalogs_dialog_title": "Sincronizar carros", "synchronize_car_catalogs_dialog_confirm": "Você realmente deseja iniciar a sincronização de todos os veículos elétricos?", "assign_user_to_car_dialog_title": "Atribuir utilizador ao carro", "assign_user_to_car_dialog_confirm": "Este carro já existe, você realmente deseja reutilizá-lo?"}}, "technical_settings": {"tabs": {"crypto": "Crypto", "users": "Users", "organization": "Organização"}, "crypto": {"crypto_key": {"title": "Crypto settings", "subtitle": "Crypto Key", "key": "Key", "block_cypher": "Block Cypher", "block_size": "Block Size", "operation_mode": "Operation Mode"}, "setting_do_not_exist": "Crypto Technical Settings not found", "update_success": "Crypto Technical Settings have been updated successfully", "update_error": "Error occurred while updating the Crypto Technical Settings, check the logs", "crypto_check_error": "Crypto check has failed, check your settings", "crypto_key_length_error": "Crypto key length is invalid, check your settings", "crypto_algorithm_error": "Crypto algorithm is not supported, check your settings", "crypto_migration_in_progress_error": "Crypto settings cannot be changed while migration is in progress"}, "user": {"title": "Account <PERSON><PERSON>", "auto_activation": "Automatically activate new user at registration time", "update_success": "User settings have been updated successfully", "update_error": "Error occurred while updating the user settings, check the logs", "setting_do_not_exist": "User settings not found"}}, "payment_cards": {"payment_cards_delete_title": "Delete Payment Cards", "payment_cards_delete_confirm": "Do you really want to delete the Payment Cards?", "payment_cards_delete_success": "Payment Cards has been deleted successfully", "payment_cards_delete_error": "Error occurred while deleting a Payment Cards, check the logs"}, "ocpiendpoints": {"platform_fee": "Platform Fee", "name": "Nome", "base_url": "URL", "role": "Função", "country_code": "Codigo do País", "party_id": "Party ID", "status": "Estado", "version": "Vers<PERSON>", "local_token": "Token Local", "token": "<PERSON><PERSON>", "new": "Novo", "registered": "Registado", "unregistered": "<PERSON><PERSON>", "last_patch_job_on": "Data da ultima atualização", "patch_job_last_status": "Último estado de envio", "patch_job_status": "Job Status", "patch_job_result": "Sucesso / Total", "patch_job_checkbox": "Ativar tarefa de atualização de status EVSE", "status_active": "Ativo", "status_inactive": "Inativo", "generate_new_token": "Gerar novo token", "test_connection": "<PERSON><PERSON>", "create_success": "O IOP '{{name}}' foi criado com sucesso", "create_error": "Ocorreu um erro ao criar o IOP", "update_success": "O IOP '{{name}}' foi atualizado com sucesso", "update_error": "Ocorreu um erro ao atualizar o IOP", "delete_confirm": "Tem certeza de que deseja excluir o IOP '{{name}}'?", "delete_title": "Excluir IOP", "delete_success": "O IOP '{{name}}' foi excluído com sucesso", "delete_error": "Ocorreu um erro ao excluir o IOP", "register_confirm": "Deseja acionar o processo de registro com o IOP '{{name}}'?", "unregister_confirm": "Deseja acionar o processo de cancelamento de registro com o IOP '{{name}}'?", "register_title": "Registar IOP", "unregister_title": "Cancelar registro de IOP", "register_success": "O IOP '{{name}}' foi registado com sucesso", "unregister_success": "O IOP '{{name}}' foi cancelado com sucesso", "register_error": "Ocorreu um erro ao registar o IOP", "unregister_error": "Ocorreu um erro ao cancelar o registo do IOP", "update_credentials_title": "Update Credentials IOP", "update_credentials_confirm": "Do you want to update the credentials in the IOP '{{name}}'?", "update_credentials_success": "The credentials have been updated successfully in the IOP '{{name}}'", "update_credentials_error": "Error occurred while updating the credentials with the IOP, check the logs", "push_evse_statuses_title": "Enviar status EVSE", "push_tokens_title": "Enviar tokens de usuário", "push_evse_statuses_confirm": "Deseja enviar todos os status EVSE para IOP '{{name}}'?", "push_tokens_confirm": "Deseja enviar todos os tokens de usuário para IOP '{{name}}'?", "trigger_jobs_title": "Executar trabalhos", "trigger_jobs_confirm": "Deseja acionar todos os trabalhos de IOP '{{name}}'?", "trigger_ocpi_action": "The action has been successfully sent and may take time to be processed", "ocpi_action_in_progress": "You cannot start twice the same action, there is already one running in the background", "pull_locations_title": "Localizações de extração", "pull_locations_confirm": "<PERSON><PERSON><PERSON> recuperar todas as localizações do IOP '{{name}}'?", "pull_locations_success": "The command has been accepted and the location(s) will be pulled asynchronously", "pull_locations_error": "Ocorreu um erro ao atualizar os locais IOP - consulte os registos para obter mais detalhes", "get_sessions_title": "<PERSON><PERSON><PERSON>", "get_sessions_confirm": "<PERSON><PERSON><PERSON> recuperar todas as sessões do IOP '{{name}}'?", "get_sessions_success": "The command has been accepted and the sessionss will be pulled asynchronously", "get_sessions_error": "Ocorreu um erro ao atualizar as sessões IOP - consulte os logs para obter mais detalhes", "pull_tokens_title": "<PERSON><PERSON><PERSON>", "pull_tokens_confirm": "Deseja recuperar todos os tokens do IOP '{{name}}'?", "pull_tokens_success": "The command has been accepted and the tokens will be pulled asynchronously", "pull_tokens_error": "Ocorreu um erro ao atualizar os tokens IOP - consulte os logs para obter mais detalhes", "pull_cdrs_title": "Pull Charge Detail Records", "pull_cdrs_confirm": "Deseja recuperar todos os registos de detalhes de cobrança do IOP '{{name}}'?", "pull_cdrs_success": "The command has been accepted and the charge detail record(s) will be pulled asynchronously", "pull_cdrs_error": "Ocorreu um erro ao atualizar os registos de detalhes de carga do IOP - consulte os registos para obter mais detalhes", "pull_tariffs_title": "Pull Tariffs Detail Records", "pull_tariffs_confirm": "Do you want to retrieve all Tariffs detail records from IOP '{{name}}'?", "pull_tariffs_success": "The command has been accepted and the Tariffs detail record(s) will be pulled asynchronously", "pull_tariffs_error": "Error occurred while updating the IOP Tariffs detail records, check the logs", "check_cdrs_title": "Verificar registos de detalhes de cobrança", "check_cdrs_confirm": "Deseja verificar todos os registos de detalhes de cobrança com IOP '{{name}}'?", "check_cdrs_success": "The command has been accepted and the charge detail records will be checked asynchronously", "check_cdrs_error": "Ocorreu um erro ao verificar os registos de detalhes de carga do IOP - consulte os registos para obter mais detalhes", "check_sessions_title": "Verificar sessões", "check_sessions_confirm": "<PERSON>eja verificar todas as sessões com IOP '{{name}}'?", "check_sessions_success": "The command has been accepted and the sessions will be checked asynchronously", "check_sessions_error": "Ocorreu um erro ao verificar as sessões de IOP - consulte os registos para obter mais detalhes", "check_locations_title": "Verificar locais", "check_locations_confirm": "Deseja verificar todos os locais com IOP '{{name}}'?", "check_locations_success": "The command has been accepted and the locations will be checked asynchronously", "check_locations_error": "Ocorreu um erro ao verificar os locais de IOP - consulte os registos para obter mais detalhes", "trigger_jobs_error": "Ocorreu um erro durante a execução de trabalhos - consulte os registos para obter mais detalhes", "success_ping": "<PERSON> bem sucedido", "error_ping_401": "Falha no ping: não autorizado", "error_ping_404": "Falha no ping: não encontrado", "error_ping_412": "Falha no ping: Respost<PERSON> inválida", "error_ping": "<PERSON><PERSON><PERSON> de ping", "error_generate_local_token": "Ocorreu um erro ao gerar um novo token local", "push_evse_statuses_success": "The command has been accepted and the EVSE's statuses will be pushed asynchronously", "push_tokens_success": "The command has been accepted and the token(s) will be pushed asynchronously", "push_evse_statuses_error": "Ocorreu um erro ao atualizar os status EVSE - consulte os registos para obter mais detalhes", "push_tokens_error": "Ocorreu um erro ao atualizar os tokens do usuário - consulte os logs para obter mais detalhes", "start_stop_job": "Ativar / desativar trabalho em segundo plano", "background_job_activated": "Trabalho em segundo plano ativado com sucesso", "background_job_deactivated": "Trabalho em segundo plano desativado com sucesso", "background_job_no_run": "Não executado", "start_background_job_title": "Iniciar trabalho em segundo plano", "start_background_job_confirm": "Deseja iniciar o trabalho em segundo plano para o IOP '{{name}}'?", "stop_background_job_title": "Parar trabalho em segundo plano", "stop_background_job_confirm": "Deseja parar o trabalho em segundo plano para o IOP '{{name}}'?", "total_charge_points": "Charging Stations", "total_tokens": "RFID Cards", "total_locations": "Sites", "total": "Total", "succeeded": "Sucesso", "failed": "<PERSON><PERSON><PERSON>"}, "ocpi": {"title": "Roaming OCPI", "type_placeholder": "Tipo", "roaming_platforms": "plataforma de Inter Operabilidade", "cpo": "Charging Point Operator", "enable_cpo": "Enable Charging Point Operator", "emsp": "e-Mobility Service Provider", "enable_emsp": "Enable e-Mobility Service Provider", "tariff_id": "Tariff ID", "connector_id": "Connector ID", "remarks": "Remarks", "owner_name": "Owner Name", "description": "Interface de ponto de carga aberta", "details": "Identificação", "country_code": "Código do país", "party_id": "Party ID", "business_details": "Detalhes da empresa", "logo": "Imagem", "sync_all": "Sin<PERSON><PERSON><PERSON>r tudo", "push_evse_statuses": "<PERSON><PERSON>es", "push_tokens": "<PERSON><PERSON>", "pull_cdrs": "Puxar CDRs", "pull_tariffs": "Pull Tariffs", "pull_locations": "Localizações de extração", "pull_sessions": "<PERSON><PERSON>", "pull_tokens": "Puxar tokens", "check_locations": "Verificar locais", "check_sessions": "Verificar sessões", "check_cdrs": "Verificar Cdrs", "update_credentials": "Update Credentials", "businessdetails": {"name": "Nome do operador", "website": "Link para o site da operadora", "logo": {"url": "URL da imagem em escala reale", "thumbnail": "URL da imagem em miniatura", "category": "Categoria da imagem", "type": "Tipo de imagem (gif, jpeg, png, svg,...)", "width": "<PERSON>rgura da imagem em escala real", "height": "Altura da imagem em escala real"}}}, "oicpendpoints": {"name": "Name", "base_url": "URL", "role": "Role", "country_code": "Country Code", "party_id": "Party ID", "status": "Status", "version": "Version", "new": "New", "registered": "Registered", "unregistered": "Unregistered", "last_patch_job_on": "Last Update Date", "patch_job_last_status": "Last Push Status", "patch_job_status": "Job Status", "patch_job_result": "Success / Total", "patch_job_checkbox": "Activate EVSE Status Update Job", "status_active": "Active", "status_inactive": "Inactive", "test_connection": "Connection Test", "create_success": "'{{name}}' has been created successfully", "create_error": "Error occurred while creating the IOP, check the logs", "update_success": "'{{name}}' has been updated successfully", "update_error": "Error occurred while updating the IOP, check the logs", "delete_confirm": "Do you really want to delete the IOP '{{name}}'?", "delete_title": "Delete", "delete_success": "'{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting, check the logs", "register_confirm": "Do you want to trigger registration process with '{{name}}'?", "unregister_confirm": "Do you want to trigger unregistration process with '{{name}}'?", "register_title": "Register", "unregister_title": "Unregister", "register_success": "'{{name}}' has been registered successfully", "unregister_success": "'{{name}}' has been unregistered successfully", "register_error": "Error occurred while registring, check the logs", "unregister_error": "Error occurred while unregistring, check the logs", "push_evse_statuses_title": "Send EVSE Statuses", "push_evses_title": "Send EVSEs", "push_evse_statuses_confirm": "Do you want to send All EVSE statuses to Hubject '{{name}}'?", "push_evses_confirm": "Do you want to send All EVSEs to Hubject '{{name}}'?", "trigger_jobs_title": "Execute Jobs", "trigger_jobs_confirm": "Do you want to trigger all jobs of Hu<PERSON> '{{name}}'?", "trigger_jobs_error": "Error occurred while executing jobs, check the logs", "success_ping": "Ping successful", "error_ping_401": "<PERSON> failed: Unauthorized", "error_ping_404": "<PERSON> failed: Not Found", "error_ping_412": "Ping failed: Invalid Response", "error_ping": "Ping failure", "push_evse_statuses_success": "Successfully updated {{success}} EVSE statuses", "push_evse_statuses_partial": "{{success}} EVSE(s) updated - {{error}} EVSE(s) not updated, check the logs", "push_evses_success": "Successfully updated {{success}} EVSEs", "push_evses_partial": "{{success}} EVSE(s) updated - {{error}} EVSE(s) not updated, check the logs", "push_evse_statuses_error": "Error occurred while updating EVSE statuses, check the logs", "push_evses_error": "Error occurred while updating EVSEs, check the logs", "start_stop_job": "Enable/Disable background job", "background_job_activated": "Background job activated successfully", "background_job_deactivated": "Background job disabled successfully", "background_job_no_run": "Not Executed", "start_background_job_title": "Start Background Job", "start_background_job_confirm": "Do you want to start the background job for '{{name}}'?", "stop_background_job_title": "Stop Background Job", "stop_background_job_confirm": "Do you want to stop the background job for '{{name}}'?", "total_charge_points": "Total Charge Points", "total": "Total", "succeeded": "Succeeded", "failed": "Failed"}, "oicp": {"title": "Roaming Hubject (OICP)", "type_placeholder": "Type", "roaming_platforms": "Roaming Platforms", "cpo": "Charging Point Operator Identification", "emsp": "E-Mobility Service Provider Identification", "description": "Open InterCharge Protocol", "details": "Identification", "country_code": "Country code", "party_id": "Party ID", "business_details": "Business Details", "logo": "Image", "key": "Private Key", "cert": "Certificate", "sync_all": "Synchronize All", "push_evses": "Push EVSE Data", "push_evses_statuses": "Push EVSE Status Data", "businessdetails": {"name": "Name of the operator", "website": "Link to the operator website", "logo": {"url": "URL of the full scale image", "thumbnail": "URL of the thumbnail image", "category": "Category of the image", "type": "Image type (gif, jpeg, png, svg,...)", "width": "Width of the full scale image", "height": "Height of the full scale image"}}}, "organization": {"title": "Organização", "description": "Gerir organização: empresas, sites e áreas de site", "tabs": {"companies": "Empresas", "sites": "Sites", "siteareas": "Areas de Sites"}, "graph": {"power": "Poder Instantâneo (kW)", "no_consumption": "Sem dados de consumo disponíveis", "asset_consumption_watts": "Asset Consumption (kW)", "asset_production_watts": "Asset Production (kW)", "charging_station_consumption_watts": "Charging Stations Consumption (kW)", "net_consumption_watts": "Net Consumption (kW)", "limit_watts": "Limite (kW)", "asset_consumption_amps": "Asset Consumption (A)", "asset_production_amps": "Asset Production (A)", "charging_station_consumption_amps": "Charging Stations Consumption (A)", "net_consumption_amps": "Net Consumption (A)", "limit_amps": "Limite (A)"}}, "asset": {"title": "Ativos", "description": "Manage assets (buildings, solar panels, batteries)", "tabs": {"assets": "Ativos", "in_error": "Em erro"}, "graph": {"power": "Poder Instantaneo (kW)", "limit_watts": "<PERSON>rid Limit (kW)", "no_consumption": "Sem dados de consumo disponíveis"}}, "cars": {"vin": "VIN", "license_plate": "<PERSON><PERSON><PERSON>", "user": "User", "assign_user": "Assign User", "vehicle_model": "<PERSON><PERSON>", "vehicle_make": "<PERSON><PERSON>", "vehicle_model_version": "Vers<PERSON>", "battery_capacity_full": "Capacidade da Bateria", "fast_charge_speed": "Velocidade de Carreegamento", "performance_top_speed": "Velocidade Maxima", "range_real": "Alcance Real", "range_wltp": "Alcance WLTP", "efficiency_real": "Eficiência", "performance_acceleration": "0-100 km/h", "image": "Imagem", "drivetrain_propulsion": "Propulsão", "drivetrain_torque": "Couple total", "battery_capacity_useable": "Capacidade utilizável", "charge_plug": "Plugue AC", "drivetrain_power_hp": "<PERSON><PERSON>", "fast_charge_plug": "Plugue DC", "charge_plug_location": "Localização do plug de carga", "charge_standard_power": "Alimentação AC", "charge_alternative_power": "Energia AC Alternativa", "charge_option_power": "Opção de alimentação CA", "charge_standard_charge_speed": "Velocidade AC", "charge_standard_charge_time": "Tempo AC", "fast_charge_power_max": "DC Power", "misc_seats": "<PERSON>sent<PERSON>", "misc_body": "Carroçaria", "misc_isofix": "Isofix", "misc_turning_circle": "Turning Circle", "misc_segment": "Segmento", "misc_isofix_seats": "Isofix Seats", "miscellaneous": "Miscellaneous", "battery": "Bateria", "debug_car": "Avançado", "charge_standard_tables": "Conversores AC", "evse_phase_volt": "Voltagem", "evse_phase_amp": "Amperagem", "evse_phase": "Fase(s)", "evse_phase_ac_standard": "AC Fase (s)", "evse_phase_ac_alternative": "Fase (s) AC alternativa (s)", "evse_phase_ac_option": "Opção Fase (s) AC", "charge_phase_volt": "Fase de carga Volt", "charge_phase_amp": "Charge Phase Amp", "charge_phase": "Fase de Carga", "charge_power": "Poder de carga", "charge_time": "<PERSON><PERSON> de <PERSON>", "charge_speed": "Velocidade de carga", "synchronize_car_catalogs_success": "The car synchronization request has been accepted and will be performed asynchronously", "synchronize_car_catalogs_error": "Cars failed to be synchronized, check the logs", "synchronize_car_catalogs_ongoing": "The synchronization of the cars is already ongoing", "synchronize_car_catalogs_up_to_date": "Carros já atualizados", "select_car_maker": "Selecione fabricante de automóveis", "car_makers": "Fabricantes de automóveis", "create_success": "O carro '{{carName}}' foi criado com sucesso", "update_success": " carro '{{carName}}' foi atualizado com sucesso", "delete_success": "O carro '{{carName}}' foi excluído com sucesso", "delete_error": "Ocorreu um erro ao excluir o carro", "assign_car_catalog": "Atribuir modelo de carro", "create_error": "Ocorreu um erro ao criar o carro", "update_error": "Ocorreu um erro ao atualizar o carro", "car_exist": "O carro já existe", "user_not_owner": "Apenas o proprietário pode atualizar isso (o padrão pode ser alterado)", "car_exist_different_car_catalog": "O carro já existe com um modelo de carro diferente", "user_already_assigned": "Você já atribuiu este carro a um utilizador", "company_car": "Carro da empresa", "invalid_vin": "VIN inválido", "assign_users": "Atribuir utilizadores", "car_owner": "Propriet<PERSON><PERSON>", "type": "Tipo", "default_car": "<PERSON><PERSON>", "private_car": "Carro particular", "pool_car": "Pool car", "remove_users_title": "Remover utilizador (s)", "remove_users_confirm": "Tem certeza de que deseja remover o (s) utilizador (s) selecionado (s) deste carro?", "assign_users_car_partial": "{{atribuído}} utilizador (s) foram atribuídos com sucesso e {{inError}} utilizador (s) encontraram um erro", "assign_users_car_success": "{{atribuí<PERSON>}} utilizador (s) foram atribuídos com sucesso", "assign_users_car_error": "Ocorreu um erro ao atribuir os utilizadores", "update_users_car_partial": "{{atribuído}} utilizador (s) foram atualizados com sucesso e {{inError}} utilizador (s) encontraram um erro", "update_users_car_success": "{{atribu<PERSON><PERSON>}} utilizador (s) foram atualizados com sucesso", "update_users_car_error": "Ocorreu um erro ao atualizar os utilizadores", "remove_users_car_partial": "{{atribuí<PERSON>}} utilizador (s) foram removidos com sucesso e {{inError}} utilizador (s) encontraram um erro", "remove_users_car_success": "{{atri<PERSON><PERSON><PERSON>}} utilizador (s) foram removidos com sucesso", "remove_users_car_error": "Ocorreu um erro ao remover os utilizadores", "car_not_found": "Carro não foi encontrado", "car_error": "Ocorreu um erro ao recuperar o carro", "car_user_error": "Ocorreu um erro ao recuperar os utilizadores do carro", "assign_converter": "Atribuir conversor", "converter": "Conversor", "delete_title": "Excluir carro", "delete_confirm": "Tem certeza de que deseja excluir o carro '{{carName}}'?", "users": "Utilizador", "select_car": "Select Car", "car_connector_name": "Connection Name", "car_connector_meter_id": "Connection Meter ID", "unit": {"meters": "m", "seconde": "sec(s)", "secondes": "segundos", "minutes": "minutos", "newton_metre": "Nm", "horse_power": "hp", "kilowatt_heure": "kWh", "kilometer_per_hour": "km/h", "kilometer": "km", "kilowatt": "kW", "drivetrain_power_hp_unit": "hp"}}, "car": {"title": "Car", "description": "Manage the user's cars", "tabs": {"car_catalogs": "Catálogos de carros", "cars": "Car Management"}}, "car_connector": {"title": "Car Connector", "description": "Use APIs to gain real time data of your cars"}, "analytics": {"title": "<PERSON><PERSON><PERSON>", "type_placeholder": "Tipo", "description": "Interface de análise estendida", "mainurl": "Página inicial do SAP Analytics Cloud", "timezone": "<PERSON>so hor<PERSON>rio usado no SAP Analytics Cloud", "links": "Links SAP Analytics Cloud", "set_link": "Definir link", "delete_title": "Excluir link", "delete_confirm": "Tem certeza de que deseja excluir o link '{{linkName}}'?", "link": {"name": "Nome", "description": "Descrição", "role": "Funções do utilizador", "url": "URL"}}, "smart_charging": {"title": "Carregamento Inteligente", "type_placeholder": "Tipo", "description": "Smart-Charging of electric vehicles", "optimizerUrl": "URL do otimizador"}, "sms": {"title": "SMS", "type_placeholder": "Type", "description": "SMS of electric vehicles", "username": "Api Id", "password": "Api Password", "Sender_password": "Sender Id", "smsType1": "Text Local", "smsType2": "sms Ala", "smsType3": "SmsGatewayHub", "setting_do_not_exist": "Configuration has not been found", "create_error": "Error occurred while creating the SMS settings, check the logs", "update_success": "SMS settings have been updated successfully", "update_error": "Error occurred while updating the SMS settings, check the logs", "create_success": "SMS settings have been created successfully", "description_blank": "CERO SMART MOBILITY can need of your sms like the current state of charge and see this information with the sms to your enhancement fuctionality."}, "smtp": {"title": "SMTP", "type_placeholder": "Type", "description": "SMTP for mail server", "from": "From", "host": "Host", "port": "Port", "secure": "Secure", "requireTLS": "Require TLS", "user": "User", "password": "Password", "smtpType1": "SMTP", "setting_do_not_exist": "Configuration has not been found", "create_error": "Error occurred while creating the SMTP settings, check the logs", "update_success": "SMTP settings have been updated successfully", "update_error": "Error occurred while updating the SMTP settings, check the logs", "create_success": "SMTP settings have been created successfully", "description_blank": "CERO SMART MOBILITY can need of your SMTP like the current state of charge and see this information with the SMTP to your enhancement fuctionality."}, "refund": {"title": "Reembolsar", "type_placeholder": "Tipo", "description": "Interface para Reembolsar"}, "pricing": {"title": "Preços", "type_placeholder": "Tipo", "description": "Interface de Preços", "price_kw_h_label": "Preço kW.h", "price_unit_label": "<PERSON><PERSON>", "update_success": "O preço foi atualizado com sucesso", "update_error": "Ocorreu um erro ao atualizar o preço"}, "billing": {"title": "Faturação", "type_placeholder": "Tipo", "description": "Interface de Faturação", "id": "ID de Faturação", "updated_on": "Sincronizado em", "tabs": {"invoices": "<PERSON><PERSON><PERSON>", "in_error": "Em erro"}}, "billingPlatform": {"title": "Billing Platform", "description": "Manage several billing accounts"}, "transactions": {"id": "Id", "date_from": "Data", "duration": "Duração", "inactivity": "Inatividade", "user": "Utilizador", "badge_id": "RFID", "connector": "Connector", "total_consumption": "Consu<PERSON>", "current_consumption": "Power", "state": "Estado", "price": "Preço", "taxedPrice": "Taxed Price", "state_of_charge": "Bateria", "date": "Data", "charging_station": "Estação de Carregamento", "started_at": "Iniciado em", "end_date": "E Finalizado", "stop_reason": "Reason", "consumption": "Consu<PERSON>", "transactions_button": "Sessões", "transaction_today": "Hoje", "transaction_one_week_ago": "1 Semana Atráz", "transaction_one_month_ago": "1 Mês Atráz", "transaction_three_months_ago": "3 Meses Atráz", "transaction_six_months_ago": "6 Meses Atráz", "transaction_one_year_ago": "1 Ano Atráz", "tab_current": "Atual", "tab_history": "Historico", "stopped_by": "Parado por", "soft_stop": "Parado", "soft_stop_transaction_title": "<PERSON><PERSON> (suave)", "soft_stop_transaction_confirm": "Você realmente deseja parar a sessão (soft) que pertence a '{{userFullName}}' em '{{chargeBoxID}}'?", "soft_stop_transaction_success": "A sessão foi interrompida com sucesso. Você pode desbloquear o conector", "soft_stop_transaction_error": "Ocorreu um erro ao interromper a sessão", "rebuild_transaction_consumptions_title": "Reconstruir consumos", "rebuild_transaction_consumptions_confirm": "Você realmente deseja reconstruir todos os consumos da sessão?", "rebuild_transaction_consumptions_success": "Os consumos da sessão foram reconstruídos com sucesso", "rebuild_transaction_consumptions_error": "Ocorreu um erro ao reconstruir os consumos da sessão", "delete": "Eliminar", "error_code": "Erro", "delete_transaction_title": "Eliminar Se<PERSON>ão", "delete_transaction_confirm": "Tem certeza de que deseja excluir a sessão que pertence a '{{userFullName}}' em '{{chargeBoxID}}'?", "delete_transaction_success": "A sessão foi excluída com sucesso", "delete_transaction_error": "Ocorreu um erro ao excluir a sessão", "delete_transactions_title": "<PERSON><PERSON><PERSON><PERSON> (ões)", "delete_transactions_confirm": "Deseja realmente excluir {{quantidade}} sess<PERSON> (ões)?", "delete_transactions_success": "{{inSuccess}} sessões foram excluídas com sucesso", "delete_transactions_partial": "{{inSuccess}} sessões foram excluídas com sucesso e {{inError}} sessões encontraram um erro", "delete_transactions_error": "{{inError}} failed to be deleted, check the logs", "delete_transactions_unexpected_error": "Unexpected error occurred while deleting the sessions, check the logs", "delete_no_transaction": "No session has been deleted", "export_ocpi_cdr_button_title": "Export Cdr", "export_ocpi_cdr_title": "Export Transaction's Cdr", "export_ocpi_cdr_confirm": "Do you really want to export the Cdr of the Session '{{transactionID}}'", "export_ocpi_cdr_error": "Error occurred while exporting the Cdr of the Session '{{transactionID}}'", "inactivity_info": "<PERSON><PERSON> as inatividades", "inactivity_warning": "Inatividades médias", "inactivity_error": "Altas inatividades", "refund": "Restituição", "refund_transaction_title": "Sessão de Reembolso", "refund_transaction_confirm": "Deseja realmente reembolsar a sessão de '{{userFullName}}' em '{{chargeBoxID}}'?", "refund_transaction_success": "A sessão foi reembolsada com sucesso", "refund_transaction_error": "Ocorreu um erro ao reembolsar a sessão", "refund_undefined": "Não submetido", "refund_approved": "<PERSON><PERSON><PERSON>", "refund_cancelled": "Cancelado", "refund_submitted": "Submetido", "transaction_number": "Se<PERSON><PERSON><PERSON>(s)", "total_consumption_kw": "Consumo Total", "transaction_not_found": "Sessão não encontrada", "transaction_id_not_found": "ID de sessão '{{sessionID}}' não foi encontrado", "load_transactions_error": "Ocorreu um erro ao recuperar as sessões", "load_transaction_error": "Ocorreu um erro ao recuperar a sessão", "total_duration": "Duração total", "refund_transactions": "Reembolso", "pending_transactions": "Pendente", "count_refunded_reports": "Relatórios", "hours": "hora(s)", "mins": "minuto(s)", "hour_short": "h", "no_active_transaction": "Não existem sessões ativas", "no_history": "Sem história durante este período", "refundDate": "Transferido em", "reportId": "ID do relatório", "select_report": "Selecione Relatório", "create_invoice_success": "A fatura foi criada com sucesso para esta sessão", "create_invoice_error": "Ocorreu um erro ao criar uma fatura para esta sessão", "redirect": "Navigate to Sessions", "error_start_no_payment_method": "Service unavailable, please go to your profile to provide a payment method.", "error_start_general": "Service temporarily unavailable, please contact the support.", "filter": {"type": {"name": "Tipo", "refunded": "Reembolsado", "not_refunded": "Não Reembolsado"}}, "tabs": {"history": "Hist<PERSON><PERSON><PERSON>", "in_progress": "Em Progresso", "in_error": "Erro", "refund": "<PERSON><PERSON>bol<PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON>"}, "graph": {"battery": "Bateria (%)", "power": "Rede de energia (kW)", "power_l1": "Rede de energia L1 (kW)", "power_l2": "Rede de energia L2 (kW)", "power_l3": "Rede de energia L3 (kW)", "amps": "<PERSON><PERSON> (A)", "amps_l1": "Grid Amps L1 (A)", "amps_l2": "Grid Amps L2 (A)", "amps_l3": "Grid Amps L3 (A)", "energy": "Energia entregue (kW.h)", "energy_amps": "Energia entregue (A.h)", "reset_zoom": "Reset", "no_consumption": "Sem dados de consumo disponíveis", "cumulated_amount": "Amount", "amperage": "Amperagem (A)", "amperage_dc": "Amperagem DC (A)", "amperage_l1": "Amperagem L1 (A)", "amperage_l2": "Amperagem L2 (A)", "amperage_l3": "Amperagem L3 (A)", "voltage": "Voltagem (V)", "voltage_dc": "Voltagem DC (V)", "voltage_l1": "Voltagem L1 (V)", "voltage_l2": "Voltagem L2 (V)", "voltage_l3": "Voltagem L3 (V)", "plan_watts": "Connector Limit (kW)", "plan_amps": "Connector Limit (A)", "limit_plan_watts": "Limite do plano (kW)", "limit_plan_amps": "Limite do plano (A)", "limit": "Limite", "limit_watts": "<PERSON><PERSON> de Potência (kW)", "limit_amps": "Limite de Potência (A)", "load_all_consumptions": "Mostra a resolução completa do gráfico", "unit_kilowatts": "kW", "unit_amperage": "Amp"}, "dialog": {"delete": {"title": "<PERSON><PERSON><PERSON>", "confirm": "Tem certeza de que deseja excluir a sessão de {{utilizador}} ?", "rejected_refunded_msg": "Você não pode excluir uma sessão que já foi reembolsada!", "rejected_billed_msg": "Você não pode excluir uma sessão que já foi faturada!"}, "export": {"title": "Exportar Sessões", "confirm": "Deseja realmente exportar todas as sessões que correspondem aos filtros ?", "error": "Ocorreu um erro durante a exportação das sessões"}, "soft_stop": {"title": "<PERSON><PERSON>", "confirm": "Tem certeza de que deseja interromper a sessão de {{user}}?"}, "refund": {"title": "Restituição", "confirm": "Deseja realmente transferir {{quantidade}} sessão (ões) para o seu sistema de reembolso?"}, "roaming": {"title": "Roaming", "confirm": "Você realmente deseja enviar o CDR da ID de sessão '{{sessionID}}' para a plataforma de roaming?"}, "create_invoice": {"title": "<PERSON><PERSON><PERSON> recibo", "confirm": "Você realmente deseja criar uma fatura para esta sessão?"}, "session": {"tooltips": {"amount": "Amount", "start_date": "Data de Inicio", "total_consumption": "Consumo Total", "current_consumption": "<PERSON><PERSON><PERSON> atual", "state_of_charge": "Nível de Bateria", "total_duration": "Duração Total", "inactivity": "Duração de Inatividade"}, "pricing_detail_description": "Description", "pricing_detail_entity_type": "Entity Type", "pricing_detail_entity_id": "Entity ID", "pricing_detail_entity_name": "Entity Name", "pricing_detail_unit_price": "Unit Price", "pricing_detail_consumption": "Consumption", "pricing_detail_duration": "Duration", "pricing_detail_amount": "Amount", "pricing_detail_view_all": "View all"}}, "notification": {"delete": {"success": "A sessão de {{user}} foi excluída com sucesso", "error": "Ocorreu um erro durante a exclusão da sessão"}, "roaming": {"success": "O CDR do ID de sessão '{{sessionID}}' foi enviado com sucesso para a plataforma de roaming", "error": "Ocorreu um erro durante o envio do CDR, verifique os logs", "error_not_from_tenant": "Esta sessão não pertence a este inquilino", "error_no_ocpi": "Esta sessão não contém dados OCPI", "error_cdr_already_pushed": "O CDR desta sessão já foi enviado"}, "soft_stop": {"success": "A sessão de {{user}} foi interrompida com sucesso.", "error": "Ocorreu um erro durante a parada da sessão."}, "refund": {"success": "{{inSuccess}} sess<PERSON><PERSON> foram transferidas com sucesso", "partial": "{{inSuccess}} sessões foram transferidas com sucesso e {{inError}} sessão (s) encontraram um erro", "forbidden_refund_another_user": "Você não pode transferir a sessão de outro utilizador", "not_authorized": "Você não está autorizado a realizar esta solicitação", "error": "O aplicativo E-Mobility não conseguiu se conectar ao sistema de reembolso. Contate um administrador.", "concur_connection_invalid": "A conexão do sistema de reembolso não está configurada corretamente. Contate um administrador.", "tenant_concur_connection_invalid": "A organização da configuração do seu sistema de reembolso não está completa. Contate um administrador"}}, "errors": {"no_consumption": {"title": "Sem consumos", "description": "A sessão não contém valores de medidor.", "action": "Verifique as configurações da estação de carregamento. Certifique-se de que os valores do medidor são enviados pela estação de carregamento."}, "low_consumption": {"title": "Low Consumption", "description": "The session has a consumption < 1 kWh.", "action": "Check the charging station settings. Ensure that meter values are sent by the charging station."}, "average_consumption_greater_than_connector_capacity": {"title": "<PERSON><PERSON><PERSON> muito alto", "description": "A potência média de carga registrada é maior do que a capacidade do conector.", "action": "Verifique as configurações da estação de carregamento. Certifique-se de que a potência máxima do conector está definida e válida."}, "negative_inactivity": {"title": "Inatividade Negativa", "description": "A inatividade da sessão é negativa.", "action": "Verifique as configurações da estação de carregamento. Certifique-se de que os valores do medidor são enviados na ordem correta."}, "long_inactivity": {"title": "High Inactivity", "description": "A inatividade da sessão é superior a 24 horas.", "action": "Verifique a sessão"}, "negative_duration": {"title": "Duração Negativa", "description": "A duração da sessão é negativa.", "action": "Verifique as configurações da estação de carregamento. Certifique-se de que os valores do medidor são enviados na ordem correta."}, "low_duration": {"title": "Low Duration", "description": "The duration of the session is less than 1 minute.", "action": "Check the charging station settings. Ensure that meter values are sent correctly."}, "missing_price": {"title": "Ausência de Preço", "description": "A transação não foi avaliada.", "action": "Mesmo admitindo que o preço esteja ativado na organização, a transação não foi precificada. Contate o administrador do seu site."}, "incorrect_starting_date": {"title": "Data de início incorreta", "description": "O valor da data de início está incorreto ou impossível.", "action": "Verifique as configurações da estação de carregamento. Certifique-se de que a data enviada no início da transação está correta."}, "missing_user": {"title": "Sessão sem utilizador", "description": "O utilizador é desconhecido para esta sessão.", "action": "Exclua a Sessão ou desative o controle de acesso na Área do Site correspondente."}, "no_billing_data": {"title": "Sessão sem dados de faturação", "description": "Não há dados de faturação para esta sessão.", "action": "Exclua a sessão ou crie uma fatura a partir dela."}}}, "templates": {"title": "Charging Station Template", "tabs": {"charging_station_templates": "Charging Station Templates"}, "invalid_json": "Invalid JSON format", "template_not_found": "Template has not been found", "create_success": "The template '{{template}}' has been created successfully", "create_error": "Error occurred while creating the template, check the logs", "update_success": "The template '{{template}}' has been updated successfully", "update_error": "Error occurred while updating the template, check the logs", "delete_confirm": "Do you really want to delete the template '{{template}}'?", "delete_success": "The template '{{template}}' has been deleted successfully", "delete_error": "Error occurred while deleting the template, check the logs", "delete_title": "Delete Template", "dialog": {"export": {"title": "Export charging station templates", "confirm": "Do you really want to export all the charging station templates matching the filters?", "error": "Error occurred while trying to export the charging station templates, check the logs"}}, "chargePointVendor": "<PERSON><PERSON><PERSON>", "chargePointModel": "Model Filter", "chargeBoxSerialNumber": "Serial Number Filter"}, "geomap": {"dialog_geolocation_title": "Definir geolocalização de {{componentName}} '{{itemComponentName}}'", "select_geolocation": "Selecione a localização geográfica", "search": "Pesquisar lugar", "max_zoom": "Max Zoom", "min_zoom": "<PERSON>"}, "errors": {"title": "Erro", "details": "<PERSON><PERSON><PERSON>", "description": "Descrição", "action": "Ação"}, "notifications": {"tabs": {"notifications": "Notifications"}, "form": {"title": "Notification Title", "body": "Notification Body"}, "table": {"title": "Title", "body": "Body", "user": "User", "userID": "ID", "timestamp": "Sent Date", "channel": "Channel", "sourceId": "Source", "sourceDescr": "Source Description", "chargeBoxID": "charge Box ID"}, "sent_success": "Notification has been sent successfully", "error_in_sent": "Error in notification send", "admin_only": "Apenas administradores", "session_started": "Avise-me quando minha sessão de carregamento começar", "optimal_charge_reached": "Notifique-me quando o nível ideal da bateria for alcançado (85%)", "end_of_charge": "Notifique-me quando meu veículo elétrico não estiver mais carregando (o veículo elétrico ainda estiver conectado)", "end_of_session": "Notifique-me quando minha sessão de carregamento terminar (o veículo elétrico não está mais conectado)", "user_account_status_changed": "Notifique-me quando o status do meu utilizador for alterado por um administrador (ativo, bloqueado ...)", "new_registered_user": "Avise-me quando um novo utilizador tiver acabado de se registar", "unknown_user_badged": "Notifique-me quando um utilizador desconhecido colocar o selo em uma estação de carregamento", "charging_station_status_error": "Notifique-me quando ocorrer um erro no conector de uma estação de carregamento", "charging_station_registered": "Notifique-me quando uma estação de carregamento for registrada no back-end ou reiniciada", "charging_stations_offline": "Notifique-me quando uma estação de carregamento estiver offline", "preparing_session_not_started": "Notifique-me quando uma sessão não tiver sido iniciada (apenas proprietário do site)", "ocpi_patch_status_error": "Notifique-me se a transferência dos status de uma estação de carregamento falhar para a plataforma de roaming (OCPI)", "oicp_patch_status_error": "Notifique-me se a transferência dos status de uma estação de carregamento falhar para a plataforma de roaming (OICP)", "billing_synchronization_failed": "Notifique-me quando uma sincronização com o sistema de faturamento falhar", "billing_periodic_operation_failed": "Notify me when a periodic billing operation has failed", "session_not_started": "Notifique-me se nenhuma sessão tiver sido iniciada após o selo", "user_account_inactivity": "Notifique-me quando eu não fizer login no aplicativo por mais de 6 meses", "car_catalog_synchronization_failed": "Notifique-me se ocorrer um erro durante os carros de sincronização", "compute_and_apply_charging_profiles_failed": "Notify me when applying the smart charging profiles fails for a site area", "end_user_error_notification": "Notifique-me quando um utilizador relatar um erro", "billing_new_invoice": "Notify me when a new invoice is available", "admin_account_verification": "Notify me when new account creation needs to be verified"}, "payment_settlement": {"table": {"transactionNo": "Transaction No", "amount": "Amount", "invoiceId": "Invoice ID", "corporateName": "Corporate", "paymentMode": "Payment Mode", "remark": "Remark", "settlementDate": "Settlement Date", "transactionDate": "Transaction Date", "id": "ID"}, "form": {"corporateID": "Corporate", "transactionNo": "Transaction No", "amount": "Amount", "invoiceId": "Invoice ID", "paymentMode": "Payment Mode", "remark": "Remark", "settlementDate": "Settlement Date", "transactionDate": "Transaction Date", "id": "ID"}, "payment_methods": {"online": "Online", "cash": "Cash", "credit_card": "Credit Card", "debit_card": "Debit Card"}, "tabs": {"payment": "Payment Settlement"}, "export": {"title": "Export Settled Payments", "confirm": "Do you really want to export the list to a csv format?", "error": "Error occurred while trying to export"}, "record_not_found": "Payment recored not found", "create_success": "The Payment '{{id}}' has been created successfully", "create_error": "Error occurred while creating the Payment, check the logs", "update_success": "The Payment '{{id}}' has been updated successfully", "update_error": "Error occurred while updating the Payment, check the logs", "delete_title": "Delete Payment", "delete_confirm": "Do you really want to delete the Payment '{{id}}'?", "delete_success": "The Payment '{{id}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Payment, check the logs", "get_corporate_error": "Error at corporate fetching time"}, "evse": {"header_title": "Reservation"}, "wallet_report": {"search": {"accountName": "Account Name"}, "searchDilog": {"title": "Select Corporate Admin", "CorporateAdmin": "Corporate Admin"}, "table": {"Date": "Date", "SessionId": "SessionId", "TransactionId": "TransactionId", "currency": "currency", "Amount": "Amount", "paymentType": "Debit/Debit", "ClosingBalance": "ClosingBalance"}}, "virtual_wallet": {"title": "Virtual Wallet", "add_balance": "Add Money", "create_success": "Amount added successfully", "create_error": "Error in amount add", "transactions_not_found": "Transactions not found", "form_field": {"amount": "Amount", "select_user": "Select User", "transactionRef": "Transaction Ref ID", "transactionDate": "Transaction Date", "remarks": "Remarks"}, "errors": {}, "table": {"id": "Transaction ID", "user": "User", "addedBy": "Added By", "amount": "Amount", "select_user": "Select User", "paymentId": "Transaction Ref ID", "transactionDate": "Transaction Date", "remarks": "Remarks"}}}