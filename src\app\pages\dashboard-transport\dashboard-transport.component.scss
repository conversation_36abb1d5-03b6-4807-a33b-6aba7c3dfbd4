.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #3577be;
  padding: 8px;
  margin-bottom: 20px;
}

.dashboard-box {
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 5px;
  color: #2372c7;
  display: inline-flex;
  align-items: center;
}

.dashboard-box i {
  margin-right: 10px;
}

.dropdown-container {
  justify-content: flex-start;
}

.dropdown-mat-box {
  width: 23%;
  margin-left: 1%;
  margin-right: 1%;
}

.dashboard-content {
  padding: 20px;
  background-color: #fff;
}

.info-cards {
  padding-top: 20px;
}

.card {
  width: 23%;
  float: left;
  border-radius: 5px;
  margin-left: 1%;
  margin-right: 1%;
  border: #555555 solid 1px;
  height: 135px;
  // box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.7);
  // box-shadow: 0 0 8px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  margin-top: 11px;
  margin-bottom: 3px;
  font-size: 16px;
  font-weight: bold;
  color: #555;
}

.card-body {
  padding: 0 0 0 10px;
  height: 62px;
}

.card-value {
  color: #1976d2;
}

.large-icon {
  font-size: 32px;
  height: 32px;
  width: 32px;
  color: #5c5c5c;
}

@keyframes blink-red {
  0%, 100% { color: red; }
  50% { color: transparent; }
}

@keyframes blink-green {
  0%, 100% { color: green; }
  50% { color: transparent; }
}

.blink-red {
  animation: blink-red 3s infinite;
}

.blink-green {
  animation: blink-green 3s infinite;
}
.view-text {
  color: #1976d2;
  cursor: pointer;
  font-size: 22px;
  margin-top: 10px;
}
#map {
  height: 700px;
  width: 98%;
  margin-left: 1%;
  margin-top: 0px;
  background-color: #eee;
}

.map-section {
  padding: 0rem 15px 0px 15px;
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
}

// .chargin-stations {
//   box-shadow: 0 0 8px 8px rgba(41, 145, 210, 0.52);
// }

// .warning {
//   box-shadow: 0 0 8px 8px rgba(193, 210, 41, 0.52);
// }

.error {
  color: #ff0000;
  // box-shadow: 0 0 8px 8px rgba(210, 41, 41, 0.52);
}

// .success {
//   box-shadow: 0 0 8px 8px rgba(41, 210, 92, 0.52);
// }

// .active {
//   box-shadow: 0 0 8px 8px rgba(41, 210, 92, 0.52);
// }

// .blue {
//   box-shadow: 0 0 8px 8px #0000ff55;
// }

// .money {
//   box-shadow: 0 0 8px 8px #00000055;
// }

// .pink {
//   box-shadow: 0 0 8px 8px #ffc0cb55;
// }

// .energy {
//   box-shadow: 0 0 8px 8px #0097ff55;
// }

.clickable {
  cursor: pointer;
}

.dashboard-icon {
  margin-top: -26px;
  height: 50px;
  color: rgb(11, 11, 11);
}

// / On screens that are 992px or less, set the background color to blue /
@media screen and (max-width: 992px) {
  .dropdown-mat-box {
    width: 31%;
    padding-left: 12px;
  }

  .card {
    width: 31%;
  }

  .dropdown-mat-box {
    width: 31%;
  }
}


// / On screens that are 600px or less, set the background color to olive /
@media screen and (max-width: 600px) {
  .dropdown-mat-box {
    width: 46%;
    padding-left: 12px;
  }

  .card {
    width: 46%;
  }

  .dropdown-mat-box {
    width: 46%;
  }
}
