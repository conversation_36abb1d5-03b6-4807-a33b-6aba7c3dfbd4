import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { StatusCodes } from 'http-status-codes';
import { ReCaptchaV3Service } from 'ngx-captcha';

import { CentralServerService } from '../../services/central-server.service';
import { ConfigService } from '../../services/config.service';
import { MessageService } from '../../services/message.service';
import { SpinnerService } from '../../services/spinner.service';
import { WindowService } from '../../services/window.service';
import { RestResponse } from '../../types/GlobalType';
import { HTTPError } from '../../types/HTTPError';
import { User } from '../../types/User';
import { Constants } from '../../utils/Constants';
import { ParentErrorStateMatcher } from '../../utils/ParentStateMatcher';
import { Users } from '../../utils/Users';
import { Utils } from '../../utils/Utils';

@Component({
  selector: 'app-authentication-mobile-register',
  templateUrl: 'authentication-mobile-register.component.html',
})
export class AuthenticationMobileRegisterComponent implements OnInit, OnDestroy {
  public parentErrorStateMatcher = new ParentErrorStateMatcher();
  public formGroup: UntypedFormGroup;
  public name: AbstractControl;
  public firstName: AbstractControl;
  public mobile: AbstractControl;
  public acceptEula: AbstractControl;
  public countryCode: AbstractControl<string, string>;
  public registerNum: string | null;
  public tenantLogo = Constants.NO_IMAGE;
  public countryCodes: { label: string; value: string }[];

  private messages: Record<string, string>;
  private subDomain: string;

  private siteKey: string;

  public constructor(
    private centralServerService: CentralServerService,
    private router: Router,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private spinnerService: SpinnerService,
    private translateService: TranslateService,
    private reCaptchaV3Service: ReCaptchaV3Service,
    private windowService: WindowService,
    private configService: ConfigService
  ) {
    // Load the translated messages
    this.translateService.get('authentication', {}).subscribe((messages) => {
      this.messages = messages;
    });
    this.countryCodes = Constants.COUNTRY_CODES;
    // Get the Site Key
    this.siteKey = this.configService.getUser().captchaSiteKey;
    // Keep the sub-domain
    this.subDomain = this.windowService.getSubdomain();
    // Init Form
    this.formGroup = new UntypedFormGroup({
      name: new UntypedFormControl('', Validators.required),
      firstName: new UntypedFormControl('', Validators.required),
      mobile: new UntypedFormControl('', [Validators.required, Users.validatePhone]),
      acceptEula: new UntypedFormControl('', Validators.requiredTrue),
      countryCode: new UntypedFormControl('+91', Validators.required),
    });
    // Form Controls
    this.name = this.formGroup.controls['name'];
    this.firstName = this.formGroup.controls['firstName'];
    this.mobile = this.formGroup.controls['mobile'];
    this.acceptEula = this.formGroup.controls['acceptEula'];
    this.countryCode = this.formGroup.controls['countryCode'];
    this.registerNum = this.route.snapshot.queryParamMap.get('mobile');
    setTimeout(() => {
      const card = document.getElementsByClassName('card')[0];
      // After 700 ms we add the class animated to the login/register card
      card.classList.remove('card-hidden');
    }, 700);
  }

  public ngOnInit() {
    const body = document.getElementsByTagName('body')[0];
    body.classList.add('lock-page');
    body.classList.add('off-canvas-sidebar');
    if (this.registerNum) {
      this.mobile.setValue(this.registerNum);
    }
    if (this.subDomain) {
      // Retrieve tenant's logo
      this.centralServerService.getTenantLogoBySubdomain(this.subDomain).subscribe({
        next: (tenantLogo: string) => {
          if (tenantLogo) {
            this.tenantLogo = tenantLogo;
          }
        },
        error: (error) => {
          this.spinnerService.hide();
          switch (error.status) {
            case StatusCodes.NOT_FOUND:
              this.tenantLogo = Constants.NO_IMAGE;
              break;
            default:
              Utils.handleHttpError(
                error,
                this.router,
                this.messageService,
                this.centralServerService,
                'general.unexpected_error_backend'
              );
          }
        },
      });
    } else {
      this.tenantLogo = Constants.MASTER_TENANT_LOGO;
    }
  }

  public ngOnDestroy() {
    const body = document.getElementsByTagName('body')[0];
    body.classList.remove('lock-page');
    body.classList.remove('off-canvas-sidebar');
  }

  public toUpperCase(control: AbstractControl) {
    control.setValue(control.value.toUpperCase());
  }

  public firstLetterToUpperCase(control: AbstractControl) {
    control.setValue(Utils.firstLetterInUpperCase(control.value));
  }

  public mobileregister(user: User) {
    if (this.formGroup.valid) {
      this.spinnerService.show();
      user['captcha'] = '';
      this.centralServerService.mobileRegister(user).subscribe({
        next: (response) => {
          // Hide spinner
          this.spinnerService.hide();
          // Ok?
          if (response.status && response.status === RestResponse.SUCCESS) {
            // Show success
            if (Utils.isEmptyString(this.subDomain)) {
              // Super User in Master Tenant
              this.messageService.showSuccessMessage(this.messages['register_super_user_success']);
            } else {
              // User in Tenant
              this.messageService.showSuccessMessage(this.messages['register_user_success1']);
            }
            // Navigate to main page
            void this.router.navigate(['/auth/verify-otp'], {
              queryParams: { mobile: this.mobile.value, countryCode: this.countryCode.value },
            });
          } else {
            // Unexpected Error
            Utils.handleError(
              JSON.stringify(response),
              this.messageService,
              this.messages['register_user_error']
            );
          }
        },
        error: (error) => {
          // Hide spinner
          this.spinnerService.hide();
          // Check status
          switch (error.status) {
            // User Agreement not checked
            case HTTPError.USER_EULA_ERROR:
              this.messageService.showErrorMessage(this.messages['must_accept_eula']);
            case HTTPError.USER_MOBILE_ALREADY_EXIST_ERROR:
              this.messageService.showErrorMessage(this.messages['mobile_number_already_exists']);
              break;
            // Unexpected error
            default:
              Utils.handleHttpError(
                error,
                this.router,
                this.messageService,
                this.centralServerService,
                'general.unexpected_error_backend'
              );
          }
        },
      });
    }
  }
}
