import { Address } from './Address';
import { UserAuthorizationActions } from './Authorization';
import { BillingUserData } from './Billing';
import { Car } from './Car';
import CreatedUpdatedProps from './CreatedUpdatedProps';
import { TableData } from './Table';
import { Tag } from './Tag';
import { SmartChargingSessionParameters, StartTransactionErrorCode } from './Transaction';

export interface User extends TableData, CreatedUpdatedProps, UserAuthorizationActions {
  subDomain: string;
  acceptEula: boolean;
  password: string;
  identification_id: any;
  identification_type: any;
  date_of_birth: any;
  id: string;
  issuer: boolean;
  name: string;
  firstName: string;
  fullName: string;
  tags: Tag[];
  plateID: string;
  email: string;
  phone: Date;
  mobile: string;
  notificationsActive: boolean;
  notifications: UserNotifications;
  dashboard: boolean;
  address: Address;
  iNumber: string;
  costCenter: boolean;
  status: string;
  countryCode: string;
  image: string | null;
  role: UserRole;
  corporateID: string;
  corporateName?: string;
  companyID: string;
  companyName?: string;
  locale: string;
  language: string;
  numberOfSites: number;
  activeComponents?: string[];
  scopes: string[];
  companies: string[];
  sites: string[];
  sitesAdmin: string[];
  sitesOwner: string[];
  userHashID: number;
  tenantHashID: number;
  eulaAcceptedHash: string;
  eulaAcceptedVersion: number;
  eulaAcceptedOn: Date;
  billingData: BillingUserData;
  technical: boolean;
  freeAccess: boolean;
  razorpayWallet: any;
  billing?: {
    type: string;
  };
}

export interface UserNotifications {
  sendSessionStarted: boolean;
  sendOptimalChargeReached: boolean;
  sendEndOfCharge: boolean;
  sendEndOfSession: boolean;
  sendUserAccountStatusChanged: boolean;
  sendNewRegisteredUser: boolean;
  sendUnknownUserBadged: boolean;
  sendChargingStationStatusError: boolean;
  sendChargingStationRegistered: boolean;
  sendOcpiPatchStatusError: boolean;
  sendOicpPatchStatusError: boolean;
  sendUserAccountInactivity: boolean;
  sendPreparingSessionNotStarted: boolean;
  sendOfflineChargingStations: boolean;
  sendBillingSynchronizationFailed: boolean;
  sendBillingPeriodicOperationFailed: boolean;
  sendSessionNotStarted: boolean;
  sendCarCatalogSynchronizationFailed: boolean;
  sendComputeAndApplyChargingProfilesFailed: boolean;
  sendEndUserErrorNotification: boolean;
  sendBillingNewInvoice: boolean;
  sendAdminAccountVerificationNotification: boolean;
}

export interface UserSessionContext {
  car?: Car;
  tag?: Tag;
  errorCodes?: StartTransactionErrorCode[];
  smartChargingSessionParameters?: SmartChargingSessionParameters;
}

export interface UserToken {
  id?: string;
  role?: string;
  name?: string;
  email?: string;
  mobile?: string;
  firstName?: string;
  locale?: string;
  language?: string;
  currency?: string;
  billing?: string;
  tagIDs?: string[];
  tenantID: string;
  tenantName?: string;
  tenantSubdomain?: string;
  userHashID?: string;
  tenantHashID?: string;
  scopes?: readonly string[];
  companies?: string[];
  sites?: string[];
  sitesAdmin?: string[];
  sitesOwner?: string[];
  activeComponents?: string[];
}

export interface SiteUser extends TableData {
  user: User;
  siteID: string;
  siteAdmin: boolean;
  siteOwner: boolean;
}

export enum UserButtonAction {
  EDIT_USER = 'edit_user',
  CREATE_USER = 'create_user',
  DELETE_USER = 'delete_user',
  SYNCHRONIZE_BILLING_USER = 'billing_synchronize_user',
  BILLING_FORCE_SYNCHRONIZE_USER = 'billing_force_synchronize_user',
  BILLING_CREATE_PAYMENT_METHOD = 'billing_create_payment_method',
  ASSIGN_SITES_TO_USER = 'assign_sites_to_user',
  EXPORT_USERS = 'export_users',
  IMPORT_USERS = 'import_users',
  NAVIGATE_TO_USER = 'navigate_to_user',
  VIEW_SITES_OF_USER = 'view_sites_of_user',
}

export enum UserStatus {
  PENDING = 'P',
  ACTIVE = 'A',
  DELETED = 'D',
  INACTIVE = 'I',
  BLOCKED = 'B',
  LOCKED = 'L',
  UNKNOWN = 'U',
}

export enum UserRole {
  ADMIN = 'A',
  BASIC = 'B',
  CORPORATE = 'C',
  COMPANY_ADMIN = 'CA',
  DEMO = 'D',
  EMPLOYEE = 'E',
  SUPER_ADMIN = 'S',
  TRANSPORT_ADMIN = 'TA',
  TRANSPORT_MANAGER = 'TM',
  VIEWER = 'V',
  UNKNOWN = 'U',
}

export const UserRequiredImportProperties = ['email', 'firstName', 'name'];

export const UserOptionalImportProperties = ['siteIDs'];
