import { Routes } from '@angular/router';

import { RouteGuardService } from '../../guard/route-guard';
import { Action, Entity } from '../../types/Authorization';
import { CorpsComponent } from './corps.component';

export const CorpRoutes: Routes = [
  {
    path: '', component: CorpsComponent, canActivate: [RouteGuardService], data: {
      auth: {
        entity: Entity.CORP,
        action: Action.LIST,
      },
    },
  },
];
