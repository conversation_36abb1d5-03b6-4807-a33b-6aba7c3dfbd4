<div class="wrapper wrapper-full-page">
  <div class="page-header login-page header-filter" filter-color="black">
    <div class="container">
      <div class="col-md-6 ms-auto me-auto">
        <form class="form" [formGroup]="formGroup" (ngSubmit)="sendOTP(formGroup.value)">
          <div class="card card-login card-hidden">
            <div class="card-header card-header-primary text-center h-25">
              <div class="social-line">
                <img class="big-card-logo mb-2" [src]="tenantLogo" />
              </div>
              <h4 class="card-title text-center">Mobile Number</h4>
            </div>
            <div class="card-body">
              <span class="bmd-form-group">
                <div class="row">
                  <div class="col-md-3">
                    <div class="input-group">
                      <mat-form-field>
                        <mat-select formControlName="countryCode">
                          <mat-option *ngFor="let country of countryCodes" [value]="country.value">
                            {{ country.label }} ({{ country.value }})
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="col-md-9">
                    <div class="input-group">
                      <mat-form-field>
                        <input
                          appAutofocus
                          matInput
                          type="text"
                          placeholder="Mobile Number"
                          [formControl]="mobile"
                          required
                          autocomplete=""
                          [readonly]="false"
                        />
                        <mat-error *ngIf="mobile.errors?.mobile">{{
                          "users.invalid_phone_number" | translate
                        }}</mat-error>
                        <mat-error *ngIf="mobile.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div class="card-footer justify-content-center mb-4" [hidden]="verifyEmailAction">
              <button mat-button type="submit" [disabled]="!formGroup.valid">
                {{ "authentication.verify_email_resend_button" | translate }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
