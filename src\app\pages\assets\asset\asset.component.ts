import { AfterViewInit, Component, Input, OnInit, ViewChild } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { StatusCodes } from 'http-status-codes';
import { WindowService } from 'services/window.service';
import { AbstractTabComponent } from 'shared/component/abstract-tab/abstract-tab.component';
import { AssetsAuthorizations, DialogMode } from 'types/Authorization';

import { CentralServerService } from '../../../services/central-server.service';
import { DialogService } from '../../../services/dialog.service';
import { MessageService } from '../../../services/message.service';
import { SpinnerService } from '../../../services/spinner.service';
import { Asset } from '../../../types/Asset';
import { RestResponse } from '../../../types/GlobalType';
import { Utils } from '../../../utils/Utils';
import { AssetMainComponent } from './main/asset-main.component';
import { AssetConnectionComponent } from './connection/asset-connection.component';

@Component({
  selector: 'app-asset',
  templateUrl: 'asset.component.html',
  styleUrls: ['asset.component.scss']
})
export class AssetComponent extends AbstractTabComponent implements OnInit, AfterViewInit {
  @Input() public currentAssetID!: string;
  @Input() public dialogMode!: DialogMode;
  @Input() public dialogRef!: MatDialogRef<any>;
  @Input() public assetsAuthorizations!: AssetsAuthorizations;
  @Input() public isSmartMeter: boolean = false;

  @ViewChild('assetMainComponent') public assetMainComponent!: AssetMainComponent;
  @ViewChild(AssetConnectionComponent) public assetConnectionComponent!: AssetConnectionComponent;

  public formGroup!: UntypedFormGroup;
  public readOnly = true;
  public asset!: Asset;

  public constructor(
    private centralServerService: CentralServerService,
    private messageService: MessageService,
    private spinnerService: SpinnerService,
    private dialogService: DialogService,
    private translateService: TranslateService,
    protected activatedRoute: ActivatedRoute,
    protected windowService: WindowService,
    private router: Router) {
    super(activatedRoute, windowService, ['main', 'connection'], false);
  }

  public ngOnInit() {
    // Init the form
    this.formGroup = new UntypedFormGroup({});
    // Initialize asset object
    this.asset = this.asset || {} as Asset;
    // Handle Dialog mode
    this.readOnly = this.dialogMode === DialogMode.VIEW;
    Utils.handleDialogMode(this.dialogMode, this.formGroup);
    // Load Asset
    this.loadAsset();
  }


  public loadAsset() {
  if (this.currentAssetID) {
    this.spinnerService.show();
    
    // Use different API call for smart meters
    const loadObservable = this.isSmartMeter 
      ? this.centralServerService.getSmartMeter(this.currentAssetID, false, true)
      : this.centralServerService.getAsset(this.currentAssetID, false, true);
    
    loadObservable.subscribe({
      next: (asset) => {
        this.spinnerService.hide();
        this.asset = asset;
        if (this.readOnly) {
          setTimeout(() => this.formGroup.disable(), 0);
        }
        this.formGroup.updateValueAndValidity();
        this.formGroup.markAsPristine();
        this.formGroup.markAllAsTouched();
      },
      error: (error) => {
        this.spinnerService.hide();
        switch (error.status) {
          case StatusCodes.NOT_FOUND:
            this.messageService.showErrorMessage(
              this.isSmartMeter ? 'smart_meters.smart_meter_not_found' : 'assets.asset_not_found'
            );
            break;
          default:
            Utils.handleHttpError(error, this.router, this.messageService,
              this.centralServerService, 'general.unexpected_error_backend');
        }
      }
    });
  } else {
    // New Asset/Smart Meter logic remains the same
    this.asset = {} as Asset;
    this.formGroup.markAsPristine();
    if (!this.isSmartMeter) {
      this.formGroup.markAllAsTouched();
    }
    if (this.isSmartMeter) {
      setTimeout(() => {
        this.formGroup.updateValueAndValidity();
      }, 100);
    }
  }
}

  public closeDialog(saved: boolean = false) {
    if (this.dialogRef) {
      this.dialogRef.close(saved);
    }
  }

  public close() {
    Utils.checkAndSaveAndCloseDialog(this.formGroup, this.dialogService,
      this.translateService, this.saveAsset.bind(this), this.closeDialog.bind(this));
  }


  public ngAfterViewInit() {
    // Ensure form validation is updated after all child components are initialized
    if (this.isSmartMeter) {
      setTimeout(() => {
        try {
          this.formGroup.updateValueAndValidity();

          // Don't mark all as touched initially to avoid "Invalid Content" errors
          // this.formGroup.markAllAsTouched();

          // For new smart meters, mark as dirty immediately
          if (!this.currentAssetID) {
            //this.formGroup.markAsDirty();
          }

          // Mark as dirty on user input (dynamic)
          // this.formGroup.valueChanges.subscribe(() => {
          //   this.formGroup.markAsDirty();
          // });
             this.formGroup.valueChanges.subscribe(() => {
        if (this.formGroup.valid) {
          this.formGroup.markAsDirty();
        }
      });

        } catch (error) {
          console.warn('Form validation error:', error);
        }
      }, 500);
    }
  }

  public canSave(): boolean {
      if (!this.formGroup || !this.formGroup.valid) {
    return false;
  }
    
    // if (!this.formGroup) {
    //   return false;
    // }

    // For Smart Metersvalidation
    if (this.isSmartMeter) {
      // Check if required Smart Meter fields are filled (name is optional, user can enter their own)
      const meterName = this.formGroup.get('meterName')?.value;
      const meterID = this.formGroup.get('meterID')?.value;
      const publicKey = this.formGroup.get('publicKey')?.value;
      const privateKey = this.formGroup.get('privateKey')?.value;
      const url = this.formGroup.get('url')?.value;

      // Validate URL format if provided
      const isValidUrl = !url || /^https?:\/\/.+/.test(url);

      const hasRequiredFields = meterName && meterID && publicKey && privateKey && url && isValidUrl;
      const isDirty = this.formGroup.dirty;

      console.log('Smart Meter canSave check:', {
        hasRequiredFields,
        isDirty,
        isValidUrl,
        meterName,
        meterID,
        publicKey: !!publicKey,
        privateKey: !!privateKey,
        url
      });

      return hasRequiredFields && isDirty;
    }

    // For regular assets, use standard validation
    return this.formGroup.valid && this.formGroup.dirty;
  }

  public saveAsset(asset: Asset) {
   
    if (this.currentAssetID) {
      this.updateAsset(asset);
    } else {
      this.createAsset(asset);
    }
  }

  private getFormErrors() {
    const errors: any = {};
    Object.keys(this.formGroup.controls).forEach(key => {
      const control = this.formGroup.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }

  private createAsset(asset: Asset) {
  this.spinnerService.show();
  // Set coordinates
  this.assetMainComponent.updateAssetCoordinates(asset);
  // Set the image
  this.assetMainComponent.updateAssetImage(asset);
  // Set smart meter data if applicable
  if (this.assetConnectionComponent) {
    this.assetConnectionComponent.updateSmartMeterData(asset);
  }
  
  // Use different API endpoint for smart meters
  const createObservable = this.isSmartMeter 
    ? this.centralServerService.createSmartMeter(asset)
    : this.centralServerService.createAsset(asset);
  
  // Create
  createObservable.subscribe({
    next: (response) => {
      this.spinnerService.hide();
      if (response.status === RestResponse.SUCCESS) {
        this.messageService.showSuccessMessage(
          this.isSmartMeter ? 'assets.create_success' : 'assets.create_success',
          { assetName: asset.name }
        );
        this.currentAssetID = asset.id;
        this.closeDialog(true);
      } else {
        Utils.handleError(JSON.stringify(response),
          this.messageService, 
          this.isSmartMeter ? 'assets.create_error' : 'assets.create_error'
        );
      }
    },
    error: (error) => {
      this.spinnerService.hide();
      switch (error.status) {
        case StatusCodes.NOT_FOUND:
          this.messageService.showErrorMessage(
            this.isSmartMeter ? 'assets.asset_not_found' : 'assets.asset_not_found'
          );
          break;
        default:
          Utils.handleHttpError(error, this.router, this.messageService,
            this.centralServerService, 
            this.isSmartMeter ? ' assets.create_error' : 'assets.create_error'
          );
      }
    }
  });
}


  

  private updateAsset(asset: Asset) {
  this.spinnerService.show();
   
  // Ensure asset has the correct ID
  if (!asset.id && this.currentAssetID) {
    asset.id = this.currentAssetID;
  }
  
  console.log('Update Asset Debug:', {
    assetId: asset.id,
    currentAssetID: this.currentAssetID,
    isSmartMeter: this.isSmartMeter,
    asset: asset
  });
  
  // Validate that we have an ID for update
  if (!asset.id) {
    this.spinnerService.hide();
    this.messageService.showErrorMessage(
      this.isSmartMeter ? 'assets.asset_not_found' : 'assets.asset_not_found'
    );
    return;
  }
  // Set coordinates
  this.assetMainComponent.updateAssetCoordinates(asset);
  // Set the image
  this.assetMainComponent.updateAssetImage(asset);
  // Set smart meter data if applicable
  if (this.assetConnectionComponent) {
    this.assetConnectionComponent.updateSmartMeterData(asset);
  }
  
  // Use different API endpoint for smart meters
  const updateObservable = this.isSmartMeter 
    ? this.centralServerService.updateSmartMeter(asset)
    : this.centralServerService.updateAsset(asset);

  updateObservable.subscribe({
    next: (response) => {
      this.spinnerService.hide();
      if (response.status === RestResponse.SUCCESS) {
        this.messageService.showSuccessMessage(
          this.isSmartMeter ? 'assets.update_success' : 'assets.update_success',
          { assetName: asset.name }
        );
        this.closeDialog(true);
      } else {
        Utils.handleError(JSON.stringify(response), this.messageService, 
          this.isSmartMeter ? ' assets.update_error' : 'assets.update_error');
      }
    },
    error: (error) => {
      this.spinnerService.hide();
      switch (error.status) {
        case StatusCodes.NOT_FOUND:
          this.messageService.showErrorMessage(
            this.isSmartMeter ? ' assets.asset_not_found' : 'assets.asset_not_found'
          );
          break;
        default:
          Utils.handleHttpError(error, this.router, this.messageService,
            this.centralServerService, 
            this.isSmartMeter ? '   assets.update_error' : 'assets.update_error'
          );
      }
    }
  });
}
}
