import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';
import { AuthorizationDefinitionFieldMetadata } from 'types/Authorization';
import { User, UserToken } from 'types/User';

@Component({
  selector: 'app-verify-wallet',
  templateUrl: 'verify-wallet.component.html',
})
// @Injectable()
export class VerifyWalletComponent implements OnInit, OnChanges {
  // @Input() public formGroup: UntypedFormGroup;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;
  @Output() public tabChanged = new EventEmitter<any>();

  // public application_id = 'iapp_OcnDSUDgP9CwdP';

  public loggedUser: UserToken;
  public user: User;
  public formGroup!: UntypedFormGroup;
  public otp!: AbstractControl;

  public constructor(
    private centralServerService: CentralServerService,
    private messageService: MessageService
  ) {
    this.loggedUser = this.centralServerService.getLoggedUser();
    this.user = this.centralServerService.getUserProfile();
    // this.getUser();
  }

  public async ngOnInit(): Promise<void> {
    // Initialize form groups
    this.formGroup = new UntypedFormGroup({});

    // Init the form
    this.formGroup.addControl(
      'otp',
      new UntypedFormControl('', Validators.compose([Validators.required]))
    );

    // Form
    this.otp = this.formGroup.controls['otp'];
  }

  public ngOnChanges() {
    console.log('ng On Changes...');
  }

  verifyOTP() {
    if (this.formGroup.valid) {
      this.user = this.centralServerService.getUserProfile();

      const params = {
        contact: this.user?.mobile,
        application_id: this.user?.razorpayWallet?.id,
        otp: this.otp.value,
      };
      this.centralServerService.verifyRazorpayWalletOTP(params).subscribe({
        next: (response) => {
          this.messageService.showSuccessMessage('Wallet verified successfully.');
          this.tabChanged.emit(2); // jump to next tab
        },
        error: (error) => {
          this.messageService.showErrorMessage(error?.details?.errorMessage);
        },
      });
    } else {
      this.messageService.showErrorMessage('Please fill out OTP.');
    }
  }

  resendOTP() {
    this.centralServerService.resendRazorpayWalletOTP().subscribe({
      next: (result: any) => {
        this.messageService.showSuccessMessage('OTP resend successfully.');
      },
      error: (error) => {
        this.messageService.showErrorMessage('Error while resending OTP.');
      },
    });
  }

  // public getUser() {
  //   if (this.loggedUser.id) {
  //     this.centralServerService.getUser(this.loggedUser.id).subscribe({
  //       next: (user) => {
  //         this.user = user;
  //       },
  //       error: (error) => {
  //         this.messageService.showErrorMessage('users.user_do_not_exist');
  //       },
  //     });
  //   }
  // }
}
