<div class="main-content vw-90 vh-90 wallet-payment-dialog-size">
  <div class="card card-profile card-testimonial">
    <mat-tab-group
      animationDuration="0ms"
      disableRipple="true"
      class="mat-tab-info"
      [class]="dialogRef ? 'mat-tabs-with-actions' : 'mat-tabs-with-close-action'"
      [(selectedIndex)]="currentTabIndex"
    >
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>account_balance_wallet</mat-icon>
          <span>{{ "razorpay_wallet.tabs.add_balance" | translate }}</span>
        </ng-template>
        <div class="card-body mat-tab-dialog-body-content">
          <app-load-wallet
            #preauthWalletComponent
            [formGroup]="formGroup"
            [metadata]="metadata"
            (tabChanged)="tabChanged($event)"
            (closePopup)="close()"
          ></app-load-wallet>
        </div>
      </mat-tab>
    </mat-tab-group>

    <div [class]="dialogRef ? 'tabs-actions' : 'tabs-actions-embedded'">
      <button mat-icon-button>
        <mat-icon></mat-icon>
      </button>
      <button mat-icon-button (click)="close()" title="{{ 'general.close' | translate }}">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
