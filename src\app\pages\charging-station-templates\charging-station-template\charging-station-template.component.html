<div class="main-content vw-90 vh-90 template-dialog-size">
  <div class="card card-profile card-testimonial">
    <mat-tab-group animationDuration="0ms" disableRipple="true" class="mat-tab-info" [class]="dialogRef && !readOnly ? 'mat-tabs-with-actions' : 'mat-tabs-with-close-action'">
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>assignment</mat-icon>
          <span>{{this.dialogTitle}}</span>
        </ng-template>
        <div class="card-body mat-tab-dialog-body-content">
          <app-charging-station-template-main #chargingStationTemplateMainComponent [formGroup]="formGroup" [chargingStationTemplate]="chargingStationTemplate"></app-charging-station-template-main>
        </div>
      </mat-tab>
    </mat-tab-group>
    <div [class]="dialogRef ? 'tabs-actions' : 'tabs-actions-embedded'">
      <button mat-icon-button *ngIf="!readOnly" (click)="saveTemplate()" title="{{'general.save' | translate}}"
        [disabled]="!formGroup.valid || !formGroup.dirty">
        <mat-icon>save</mat-icon>
      </button>
      <button mat-icon-button *ngIf="dialogRef" (click)="close()" title="{{'general.close' | translate}}">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
