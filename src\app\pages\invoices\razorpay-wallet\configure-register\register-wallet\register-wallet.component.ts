import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import moment from 'moment';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';
import { AuthorizationDefinitionFieldMetadata } from 'types/Authorization';
import { User, UserRole, UserToken } from 'types/User';
import { Constants } from 'utils/Constants';

@Component({
  selector: 'app-register-wallet',
  templateUrl: 'register-wallet.component.html',
})
export class RegisterWalletComponent implements OnInit, OnChanges {
  @Input() public formGroup: UntypedFormGroup;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;
  @Output() public tabChanged = new EventEmitter<any>();
  @Output() public roleChanged = new EventEmitter<UserRole>();
  @Output() public closePopup = new EventEmitter<any>();

  public loggedUser: UserToken;
  public user: User;
  public name!: AbstractControl;
  public email!: AbstractControl;
  public contact!: AbstractControl;
  public identification_id!: AbstractControl;
  public identification_type!: AbstractControl;
  public date_of_birth!: AbstractControl;
  public isLoaded = true;
  public minDate = moment().subtract(18, 'years').format('YYYY-MM-DD');
  public maxDate = moment().subtract(99, 'years').format('YYYY-MM-DD');

  public constructor(
    private centralServerService: CentralServerService,
    private messageService: MessageService,
    private router: Router
  ) {
    this.loggedUser = this.centralServerService.getLoggedUser();
    this.user = this.centralServerService.getUserProfile();
  }

  public ngOnInit(): void {
    // Initialize the form controls
    this.formGroup.addControl(
      'name',
      new UntypedFormControl(
        this.user.name,
        Validators.compose([Validators.required, Validators.maxLength(100)])
      )
    );
    this.formGroup.addControl('email', new UntypedFormControl(this.user.email, Validators.email));
    this.formGroup.addControl(
      'contact',
      new UntypedFormControl(
        this.user.mobile,
        Validators.compose([Validators.required, Validators.pattern('[0-9]+')])
      )
    );
    this.formGroup.addControl(
      'identification_type',
      new UntypedFormControl('', Validators.required)
    );
    this.formGroup.addControl(
      'identification_id',
      new UntypedFormControl(
        '',
        Validators.compose([Validators.required, Validators.pattern('[A-Z0-9]+')])
      )
    );
    this.formGroup.addControl('countryCode', new UntypedFormControl('+91', Validators.required));
    this.formGroup.addControl(
      'date_of_birth',
      new UntypedFormControl(
        '',
        Validators.compose([Validators.required, this.ageRangeValidator(18, 99)])
      )
    );
    this.formGroup.addControl('countryCode', new UntypedFormControl('+91', Validators.required));
    this.formGroup.addControl('date_of_birth', new UntypedFormControl('', Validators.required));

    // Form controls
    this.name = this.formGroup.controls['name'];
    this.email = this.formGroup.controls['email'];
    this.contact = this.formGroup.controls['contact'];
    this.identification_type = this.formGroup.controls['identification_type'];
    this.identification_id = this.formGroup.controls['identification_id'];
    this.date_of_birth = this.formGroup.controls['date_of_birth'];
    this.isLoaded = true;
  }

  public ngOnChanges(): void {
    // Handle changes if needed
  }

  public save(): void {
    if (this.formGroup.valid) {
      const requestData = {
        name: this.name.value,
        email: this.email.value || '',
        contact: this.contact.value,
        // contact: this.user?.mobile,
        identification_id: this.identification_id.value,
        identification_type: this.identification_type.value,
        date_of_birth: this.date_of_birth.value,
      };

      this.centralServerService.createApplication(requestData).subscribe({
        next: (response) => {
          console.log('create Application response:', response);
          // const otp = response.otp;
          if (response.status == 'issued') {
            this.getUser(); // get and set updated data into user-profile varibale as globaly
            this.messageService.showSuccessMessage(
              'Razorpay Wallet registered successfully, Please verify it.'
            );

            this.activateRazorpayWallet(); // activate wallet
            // this.tabChanged.emit(1);
          } else {
            this.messageService.showErrorMessage(response?.errorMessage);
          }
        },
        error: (error) => {
          if (error?.details?.errorMessage) {
            this.messageService.showErrorMessage(error?.details?.errorMessage);
          } else {
            this.messageService.showErrorMessage('An unexpected error occurred. Please try again.');
          }
        },
      });
    } else {
      this.messageService.showErrorMessage('Please fill out all required fields.');
    }
  }

  public capitalizeFirstLetter(event: any): void {
    const input = event.target;
    input.value = input.value.charAt(0).toUpperCase() + input.value.slice(1);
  }

  private ageRangeValidator(min: number, max: number) {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      if (control.value) {
        const dob = new Date(control.value);
        const age = this.calculateAge(dob);
        if (age < min || age > max) {
          return { ageRange: true };
        }
      }
      return null;
    };
  }

  private calculateAge(dob: Date): number {
    const diff = Date.now() - dob.getTime();
    const ageDt = new Date(diff);
    return Math.abs(ageDt.getUTCFullYear() - 1970);
  }

  getUser() {
    if (this.loggedUser.id) {
      this.centralServerService.getUser(this.loggedUser.id).subscribe({
        next: (user) => {
          this.user = user;
          this.centralServerService.setUserProfile(user);
        },
        error: (error) => {
          this.messageService.showErrorMessage('users.user_do_not_exist');
        },
      });
    }
  }

  activateRazorpayWallet() {
    this.centralServerService.activateRazorpayWallet().subscribe({
      next: (response) => {
        console.log('Actiate wallet response:', response);
        if (response.status == 'success') {
          this.getUser(); // get and set updated data into user-profile varibale as globaly
          this.messageService.showSuccessMessage('Razorpay Wallet activated successfully');

          this.tabChanged.emit(1);
        } else {
          this.messageService.showErrorMessage(response?.errorMessage);
        }
      },
      error: (error) => {
        if (error?.details?.errorMessage) {
          this.messageService.showErrorMessage(error?.details?.errorMessage);
        } else {
          this.messageService.showErrorMessage('An unexpected error occurred. Please try again.');
        }
      },
    });
  }
}
