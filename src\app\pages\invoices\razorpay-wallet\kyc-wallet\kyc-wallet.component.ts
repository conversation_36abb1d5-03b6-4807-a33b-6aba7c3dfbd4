import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { WindowService } from 'services/window.service';
import { AuthorizationDefinitionFieldMetadata, DialogMode } from 'types/Authorization';
import { DataResult } from 'types/DataResult';
import { User } from 'types/User';

import { KeyValue } from '../../../../types/GlobalType';
import { Utils } from '../../../../utils/Utils';
import { PancardComponent } from './initiate-kyc/initiate-kyc.component';
import { KYCWalletDialogComponent } from './kyc-wallet.dialog.component';

@Component({
  selector: 'app-kyc-wallet',
  templateUrl: './kyc-wallet.component.html',
  styleUrls: ['./kyc-wallet.component.scss'],
})
export class KYCWalletComponent implements OnInit {
  @Input() public dialogMode!: DialogMode;
  @Input() public inDialog!: boolean;
  @Input() public user!: any;
  @Input() public dialogData!: any;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;

  @ViewChild('pancardComponent') public pancardComponent!: PancardComponent;

  public formGroup!: UntypedFormGroup;

  // Example properties
  public token: string = '';
  public stations: KeyValue[] = [];
  public currentTabIndex = 0; //default tab index is 0

  constructor(
    public dialogRef: MatDialogRef<KYCWalletDialogComponent>,
    protected windowService: WindowService
  ) {}

  public ngOnInit(): void {
    // Initialize form groups
    this.formGroup = new UntypedFormGroup({});

    // Handle dialog mode
    Utils.handleDialogMode(this.dialogMode, this.formGroup);
  }

  public tabChanged(tab: number): void {
    this.currentTabIndex = tab;
  }

  onTabChange(event: any) {
    this.currentTabIndex = event.index;
  }

  public close(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  public save(wallet: any): void {
    // Implement save logic
  }

  public loadDataImpl(): Observable<DataResult<any>> {
    return new Observable((observer) => {
      // Implement data loading logic
    });
  }
}
