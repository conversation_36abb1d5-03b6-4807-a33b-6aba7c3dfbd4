{"general": {"generate": "Generate", "captcha_text_1": "This site is protected by reCAPTCHA and the Google", "captcha_text_2": "Privacy Policy", "captcha_text_3": " and ", "captcha_text_4": "Terms of Service", "captcha_text_5": " apply", "not_authorized": "You are not authorized to perform this action", "created_on": "Created On", "created_by": "Created By", "changed_on": "Changed On", "changed_by": "Changed By", "expired_on": "Expired On", "revoked_on": "Revoked On", "description": "Description", "activate": "Activate", "deactivate": "Deactivate", "upload_in_progress": "Upload in progress, please wait...", "body": "Body", "day": "d", "hour": "h", "minute": "m", "second": "s", "more_records": "More", "click_here": "click here", "selected_records": "Selected", "nbr_of_records": "Records", "connectors": "Connections", "notifications": "Notifications", "dashboard.title": "Dashboard", "dashboard-transport.title": "Transport Dashboard", "general": "Main", "start_time": "Start Time", "available_date": "Available Date", "charging_station_id": "Charging Station ID", "closing_time": "Closing Time", "expire_date": "Expire Date", "app_name": "Operations Manager", "last_app_name": "(C) CERO SMART MOBILITY", "version": "Version", "about_us": "About Us", "unexpected_error_backend": "An unexpected error occurred, check the logs", "select_at_least_one_record": "You must select at least one record in the list", "mandatory_field": "Required field", "no_record_found": "No records have been found", "no_image_found": "No images have been found", "no_connector_available": "No connection available", "error_max_length": "Maximum length is '{{length}}'", "error_min_length": "Minimum length is '{{length}}'", "error_max_value": "Maximum value is '{{value}}'", "error_min_value": "Minimum value is '{{value}}'", "error_url_pattern": "Incorrect URL pattern", "error_number_pattern": "Invalid Number", "invalid_date": "Date invalid", "error_not_a_number": "Not a valid number", "error_duplicate": "Duplicate entry", "id": "Id", "ok": "Ok", "cancel": "Cancel", "actions": "Action(s)", "add": "Add", "select": "Select", "set_filter": "Set Filter", "remove": "Remove", "location": "Location", "address": "Address", "address1": "Address 1", "address2": "Address 2", "postal_code": "Postal Code", "city": "City", "region": "Region", "department": "Department", "country": "Country", "tan_gst": "Tan/Gst", "value": "Value", "latitude": "Latitude", "longitude": "Longitude", "show_place": "View Place", "invalid_value": "Invalid value", "home": "Home", "details": "Details", "create": "Create", "update": "Update", "refresh": "Refresh", "generate_qr": "Generate QR code", "display_qr": "Display QR code", "save": "Save", "delete": "Delete", "export": "Export", "import": "Import", "synchronize": "Synchronize", "force_synchronize": "Force synchronization", "download": "Download", "test_connection": "Test Connection", "upload": "Upload", "change": "Changer", "reload": "Reload", "stop": "Stop", "unlock": "Unlock", "refund": "Refund", "open": "Open", "open_refunding_system": "Jump to your Expenses", "connect": "Connect", "register": "Register", "unregister": "Unregister", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "info": "Info", "now": "Now", "default": "<PERSON><PERSON><PERSON>", "yes": "Yes", "no": "No", "apply": "Apply", "clear": "Clear", "warning": "Warning", "success": "Success", "error": "Error", "drag_drop": "Drag & Drop", "picture": "Picture", "pictures": "Pictures", "change_picture": "Change Picture", "search": "Search", "search_place": "Search Address", "use_place": "Use", "check_place": "Check", "search_date_from": "From", "search_date_until": "To", "search_date_range": "Dates", "search_charging_station": "Charging Station", "search_user": "User", "search_site": "Site", "all": "<All>", "max_lines": "Max Lines", "auto_refresh": "Auto Refresh", "sort_date_desc": "Sort Date Desc.", "search_placeholder": "Search...", "backend_not_running": "Cannot reach to the central server", "error_backend": "Error occurred while contacting the central server", "error_backend1": "Please configure this in billing settings ", "car_catalog_error": "Error occurred while retrieving the car, check the logs", "car_catalogs_error": "Error occurred while retrieving the cars, check the logs", "cars_error": "Error occurred while retrieving the user cars, check the logs", "car_image_error": "Error occurred while retrieving the car images, check the logs", "success_backend": "Connection to the central server has been restored", "success_import": "File imported successfully", "import_unexpected_error": "Error occurred while importing the file, check the logs", "choose_file": "Choose <PERSON>", "user_or_tenant_updated": "You have been logged off because either your profile or your organization's settings have been updated", "invalid_content": "Invalid Content", "open_in_maps": "Localization", "change_pending_title": "Pending Changes", "change_pending_text": "Some changes are pending.", "change_invalid_pending_title": "Pending Changes with invalid value(s)", "change_invalid_pending_text": "Some changes with invalid value(s) are pending.", "save_and_close": "Save & Close", "do_not_save_and_close": "Do Not Save & Close", "set_coordinates": "Set", "reset_filters": "Reset Filters", "browser_not_supported": "This browser is not supported! Use Chrome or Firefox.", "status": "Status", "revoke": "Revoke", "edit": "Edit", "import_instructions": "The file that you are going to import must have the following caracteristics:", "import_instructions_file_type": "File type must be CSV", "import_instructions_header_required": "CSV Header is required", "import_instructions_coma_separator": "Comma separator is used ','", "import_instructions_required_fields": "Required field(s): '{{properties}}'", "import_instructions_optional_fields": "Optional field(s): '{{properties}}'", "invalid_file_error": "The provided file is invalid", "invalid_file_csv_header_error": "At least one of the required header was not provided in the CSV file, operation has been aborted", "import_already_ongoing": "Cannot upload the RFID cards while an import is ongoing", "menu": {"dashboard": "Dashboard", "dashboard-transport": "Transport Dashboard", "charging_stations": "Charging Stations", "transactions": "Sessions", "active": "Active", "history": "History", "analytics": "Analytics", "consumption": "Consumption", "usage": "Usage", "inactivity": "Inactivity", "organisations": "Organizations", "companies": "Companies", "sites": "Sites", "site_areas": "Site Areas", "users": "Users", "transports": "Transports", "pricing": "Pricing", "manufacturers": "Manufacturers", "vehicles": "Vehicles", "logs": "Logs", "tenants": "Organizations", "revenue": "Revenue", "carbon-credit": "Carbon Credit", "wallet-report": "Wallet Report", "components": "Components", "settings": "Settings", "integration_settings": "Integration Settings", "technical_settings": "Technical Settings", "assets": "Assets", "organization": "Organization", "template": "Templates", "cars": "Cars", "invoices": "Invoices", "tags": "RFID Cards", "charging_station_templates": "Charging Station Templates", "corporates": "Accounts", "notifications": "Notifications Center", "payment-settlement": "Payment Settlement"}, "tooltips": {"push_cdr": "Push CDR", "activate": "Activate", "deactivate": "Deactivate", "export": "Export OCPP params", "assign_site_area": "Assign site", "more": "More actions", "smart_charging": "Smart charging", "clear_cache": "Clear cache", "soft_reset": "Reset", "reboot": "Reboot", "more_actions": "More actions", "add": "Add", "auto_refresh": "Automatic refresh", "create": "Create", "delete": "Delete", "edit": "Edit", "edit_chargers": "Edit charging station", "display_chargers": "Display charging stations", "edit_assets": "Edit assets", "display_assets": "Display assets", "assign_site": "Assign to Site", "edit_users": "Edit user", "edit_siteArea": "Edit site areas", "display_siteAreas": "Display site areas", "no_action": "No possible actions", "open": "Open", "open_in_maps": "Open in google maps", "refresh": "Refresh", "generate_qr": "Generate QR code", "refund": "Refund", "register": "Register", "unregister": "Unregister", "remove": "Remove", "revoke": "Revoke", "settings": "Settings", "synchronize": "Synchronize", "start": "Start", "stop": "Stop", "unlock": "Unlock", "view": "View", "send": "Send", "clear": "Clear", "save": "Save", "download": "Download", "test_connection": "Test Connection", "force_available_status": "Force Status to Available", "force_unavailable_status": "Force Status to Unavailable", "display_users": "Display users", "display_sites": "Display sites", "pricing": "Pricing"}}, "issuer": {"title": "Owner", "local": "Current Organization", "foreign": "External Organization"}, "dashboard": {"active_stations_gauge_title": "Charging points", "active_stations_gauge_unit": "Occupied", "occupied_charging_points_gauge_footer": "Number of occupied charging points", "occupied_charging_points_gauge_title": "Occupied", "all_site": "All sites", "co2_footer": "Saved CO2 since 2017 based on an emission of 120g/km", "co2_title": "CO2", "consumption_gauge_footer": "Instant power delivered in kW", "consumption_gauge_title": "Instant Power", "consumption_title": "Instant Power", "gas_footer": "Saved petrol by using a thermal vehicle consuming 8L/100km", "gas_title": "Petrol", "km_footer": "Distance done by an electric vehicle consuming 18kW.h/100km", "km_title": "Distance", "location": "Localization", "metrics": "Current metrics", "realtime_title": "Today's Statistics", "realtime": {"button": {"consumption": "Consumption", "utilization": "Utilization"}}, "statistics_title": "Consumption History", "statistics": {"button": {"day": "Day", "month": "Month", "week": "Week", "year": "Year"}}, "today_duration": "Charging Time", "today_duration_footer": "Today's total time spent in charging", "today_inactivity": "Charging Inactivity", "today_inactivity_footer": "Today's total time spent by vehicles that have finished to charge but stayed connected", "today_energy_delivered": "Energy Consumed", "today_energy_delivered_footer": "Today's total energy consumed", "trends_of_day_avg": "Average", "trends_of_day_duration_avg": "Average", "trends_of_day_duration_max": "Maximum", "trends_of_day_duration_min": "Minimum", "trends_of_day_duration_title": "'{{todayDay}}' trends", "trends_of_day_inactivity_avg": "Average", "trends_of_day_inactivity_max": "Maximum", "trends_of_day_inactivity_min": "Minimum", "trends_of_day_inactivity_title": "'{{todayDay}}' trends", "trends_of_day_max": "Maximum", "trends_of_day_min": "Minimum", "trends_of_day_title": "'{{todayDay}}' trends", "tabs": {"dashboard": "Dashboard"}, "flters": {"dropdown": {"all": "All", "current": "Current", "external": "External"}}, "cards": {"charging_stations": "Charging Stations", "connection_loss": "Faults/Connection Loss", "active_users": "Active Users", "active_sessions": "Active Sessions", "charging_sessions_month": "Charging Sessions (Current Month)", "charging_sessions_year": "Charging Sessions (Current Year)", "revenue_today": "Revenue (Today)", "revenue_month": "Revenue (Current Month)", "revenue_year": "Revenue (Current Year)", "energy_today": "Energy (Today)", "energy_month": "Energy (Current Month)", "energy_year": "Energy (Current Year)", "carbon_credit_today": "Carbon Credit (Today)", "carbon_credit_month": "Carbon Credit (Current Month)", "vehicle_charging_now": "Vehicle Charging Now", "vehicle_charging_history": "Vehicle Charging (History)"}}, "companies": {"no_companies": "No companies found", "company_number": "Company(ies)", "number_of_sites": "Nb Sites", "logo": "Logo", "name": "Name", "company": "Company", "title": "Company", "titles": "Companies", "created_on": "Created On", "created_by": "Created By", "general.value": "Tax Level Value", "taxLevel.value": "TAN/GST", "changed_on": "Changed On", "changed_by": "Changed By", "delete_title": "Delete", "delete_button": "Delete", "delete_confirm": "Do you really want to delete the company '{{companyName}}'?", "delete_success": "The company '{{companyName}}' has been deleted successfully", "delete_error": "Error occurred while deleting the company, check the logs", "changed_by_other_user": "The company '{{companyName}}' has been changed by another user and will be reloaded", "update_success": "The company '{{companyName}}' has been updated successfully", "update_error": "Error occurred while updating the company, check the logs", "company_not_found": "Company has not been found", "create_success": "The company '{{companyName}}' has been created successfully", "create_error": "Error occurred while creating the company, check the logs", "logo_size_error": "The logo size limit exceeded, limit is below '{{maxPictureKb}}' Kb", "select_companies": "Select Company"}, "assets": {"no_assets": "No assets found", "asset_number": "Asset(s)", "logo": "Logo", "name": "Name", "asset_type": "Asset type", "fluctuation_percent": "Fluctuation (%)", "variation_percent": "Variation Threshold (%)", "static_value_watt": "Static Power (W)", "produce": "Produce Energy", "consume": "Consume Energy", "consume_and_produce": "Consume and Produce Energy", "dynamic_asset": "Dynamic Asset", "uses_push_api": "Uses Push API", "instant_power": "Instant Power", "asset_connection": "Asset Connection", "meter_id": "Meter ID", "asset": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "titles": "Assets", "created_on": "Created On", "created_by": "Created By", "changed_on": "Changed On", "changed_by": "Changed By", "delete_title": "Delete", "delete_button": "Delete", "delete_confirm": "Do you really want to delete the asset '{{assetName}}'?", "delete_success": "The asset '{{assetName}}' has been deleted successfully", "delete_error": "Error occurred while deleting the asset, check the logs", "changed_by_other_user": "The asset '{{assetName}}' has been changed by another user and will be reloaded", "update_success": "The asset '{{assetName}}' has been updated successfully", "update_error": "Error occurred while updating the asset, check the logs", "asset_not_found": "Asset has not been found", "asset_settings_error": "Error occurred while getting asset connections, check the logs", "create_success": "The asset '{{assetName}}' has been created successfully", "create_error": "Error occurred while creating the asset, check the logs", "refresh_success": "The asset's consumption or production has been successfully retrieved", "refresh_error": "Error occurred while retrieving the asset's consumption or production'", "consumption_error": "Error occured while retrieving the consumption", "logo_size_error": "The image size limit exceeded, limit is below '{{maxPictureKb}}' Kb", "select_assets": "Select Asset", "consumption_date": "Date", "dialog_tabs": {"connection": "Connection"}, "errors": {"missing_site_area": {"title": "Not Assigned to a Site Area", "description": "The asset needs to be assigned to a site area", "action": "Edit the asset and maintain the site area"}}}, "sites": {"assign_company": "Assign Company", "auto_user_site_assignment": "Automatic user assignment to this site", "public": "This site is public", "public_site": "Public", "auto_assignment": "Auto User Assignment", "user_list": "Users authorized to use charging stations on this site", "user_list_source_title": "Available", "user_list_target_title": "Assigned", "no_sites": "No sites found", "select_sites": "Select Site", "site": "Site", "assigned_users_to_site": "Users assigned to '{{siteName}}'", "unassigned": "Unassigned", "no_connected_chargers": "You are not connected to a charging station", "site_number": "Site(s)", "number_of_site_areas": "Nb Site Areas", "number_of_users": "Nb Users", "users": "Users", "image": "Image", "name": "Name", "title": "Site", "titles": "Sites", "company_invalid": "Company invalid", "created_on": "Created On", "created_by": "Created By", "changed_on": "Changed On", "changed_by": "Changed By", "delete_title": "Delete", "site_not_found": "Site has not been found", "delete_button": "Delete", "delete_confirm": "Do you really want to delete the site '{{siteName}}'?", "delete_success": "The site '{{siteName}}' has been deleted successfully", "delete_error": "Error occurred while deleting the site, check the logs", "remove_users_title": "Remove User(s)", "remove_users_confirm": "Are you sure to remove the selected user(s) from this site?", "remove_users_success": "The user(s) have been removed successfully", "remove_users_error": "Error occurred while removing the user(s) from the site, check the logs", "update_users_success": "The user(s) have been added successfully", "changed_by_other_user": "The site '{{siteName}}' has been changed by another user and will be reloaded", "update_success": "The site '{{siteName}}' has been updated successfully", "update_error": "Error occurred while updating the site, check the logs", "update_users_error": "Error occurred while assigning a user to the site, check the logs", "update_public_site_error": "Site '{{siteName}}' cannot be made private and having public charging stations", "create_success": "The site '{{siteName}}' has been created successfully", "create_error": "Error occurred while creating the site, check the logs", "image_size_error": "The image size limit exceeded, limit is below '{{maxPictureKb}}' Kb", "admin_role": "Site Admin", "owner_role": "Site Owner", "update_set_site_admin_success": "The user '{{userName}}' is now administrator on this site", "update_remove_site_admin_success": "The user '{{userName}}' is no more administrator on this site", "update_site_users_role_error": "Error occurred while trying to update the access right of the user '{{userName}}', check the logs", "update_set_site_owner_success": "The user '{{userName}}' is now owner on this site", "update_remove_site_owner_success": "The user '{{userName}}' is no more owner on this site", "update_site_users_owner_error": "Error occurred while trying to grant the user '{{userName}}' as site owner, check the logs", "export_all_params_title": "Export OCPP parameters", "export_all_params_confirm": "Do you really want to export all the OCPP parameters of all the charging stations belonging to the site '{{siteName}}'?", "export_all_params_error": "Error occurred while trying to export all the OCPP parameters, check the logs", "display_users": "Display users", "private_limitation": "Some features are not available when the site is private"}, "site_areas": {"limits": "Limits", "assign_site": "Assign Site", "smart_charging": "Smart Charging", "export_all_ocpp_params": "Export All OCPP Params", "edit_assets": "Edit assets", "display_assets": "Display assets", "no_site_areas": "No site areas found", "site_areas_number": "Site Area(s)", "show_all_chargers": "Show All Charging Stations", "enable_access_control": "Enable Access Control (users must authenticate with an RFID card)", "enable_smart_charging": "Enable Smart Charging", "access_control": "Access Control", "charger_list_source_title": "Available", "charger_list_target_title": "Assigned", "number_of_charging_stations": "Nb Chargers", "site_area_not_found": "Site Area has not been found", "select_site_areas": "Select Site Area", "unassigned": "Unassigned", "image": "Image", "assigned_chargers": "Charging Stations", "assigned_chargers_to_site_area": "Charging Stations assigned to '{{siteAreaName}}'", "assigned_assets_to_site_area": "Assets assigned to '{{siteAreaName}}'", "name": "Name", "title": "Site Area", "titles": "Site Areas", "parent_site_area": "Parent Site Area", "select_parent_site_area": "Select Parent Site Area", "site_area_hierarchy_error_title": "Site Area Hierarchy Error", "carbon_credit": "Carbon Crdit", "site_area_tree_error": ["An error occurred while modifying the Site Area chain.", "Avoid circular dependencies within the tree.", "For more information please check the logs."], "site_area_tree_error_site": ["An error occurred while modifying the Site Area chain.", "Site Area has children with a different Site.", "Do you want to:", "1 - Update all Site Area chain with the new Site", "2 - Assign the current parent Site Area to all children", "3 - Clear the parent Site Area in all children"], "site_area_tree_error_site_update": "Update (1)", "site_area_tree_error_site_attach": "Assign (2)", "site_area_tree_error_site_clear": "Clear (3)", "site_area_tree_error_smart_charging": ["An error occurred while modifying the Site Area chain.", "Site Area must have the same Smart Charging value as its parent.", "Do you want change the Smart Charging value to the whole Site Area chain?"], "site_area_tree_error_voltage": ["An error occurred while modifying the Site Area chain.", "Site Area must have the same Voltage as its parent."], "site_area_tree_error_number_of_phases": ["An error occurred while modifying the Site Area chain.", "Site Area must have the same Number of Phases as its parent."], "site_area_tree_error_multiple_actions_not_supported": ["An error occurred while modifying the Site Area chain.", "Multiple changes are currently not supported (eg. change of Site and Smart Charging.)", "Perform one change at a time."], "site_invalid": "Site Area invalid", "site_area_does_not_exist": "Site Area does not exist", "max_limit_kw": "Limite <PERSON>", "created_on": "Created On", "created_by": "Created By", "changed_on": "Changed On", "changed_by": "Changed By", "delete_title": "Delete", "delete_button": "Delete", "delete_confirm": "Do you really want to delete the site area '{{siteAreaName}}'?", "delete_success": "The site area '{{siteAreaName}}' has been deleted successfully", "delete_error": "Error occurred while deleting the site area, check the logs", "remove_chargers_title": "Remove Charging Station(s)", "remove_chargers_confirm": "Are you sure to remove the selected charging station(s) from this site area?", "remove_chargers_success": "The charging station(s) have been removed successfully", "remove_chargers_error": "Error occurred while removing the charging station(s) from the site area, check the logs", "update_chargers_success": "The charging station(s) have been added successfully", "remove_assets_title": "<PERSON><PERSON><PERSON>(s)", "remove_assets_confirm": "Are you sure to remove the selected asset(s) from this site area?", "remove_assets_success": "The asset(s) have been removed successfully", "remove_assets_error": "Error occurred while removing the asset(s) from the site area, check the logs", "update_assets_success": "The asset(s) have been added successfully", "changed_by_other_user": "The site area '{{siteAreaName}}' has been changed by another user and will be reloaded", "update_success": "The site area '{{siteAreaName}}' has been updated successfully", "update_error": "Error occurred while updating the site area, check the logs", "update_phase_error": "The site area contains charging station(s) with three phases", "create_success": "The site area '{{siteAreaName}}' has been created successfully", "create_error": "Error occurred while creating the site area, check the logs", "image_size_error": "The image size limit exceeded, limit is below '{{maxPictureKb}}' Kb", "maximum_energy_watts": "Maximum Power (W)", "maximum_energy_amps_per_phase": "Maximum Amperage per phase (A)", "maximum_energy_amps_total": "Maximum total Amperage (A)", "number_of_phases": "Number of phases", "export_all_params_title": "Export OCPP parameters", "export_all_params_confirm": "Do you really want to export all the OCPP parameters of all the charging stations belonging to the site area '{{siteAreaName}}'?", "export_all_params_error": "Error occurred while trying to export all the OCPP parameters, check the logs", "consumption_date": "Date", "single_phased": "Single Phase", "three_phased": "Three Phases", "redirect": "Navigate to Site Area"}, "authentication": {"tfa_title": "MFA Authentication", "tfa_description": "Enable/disable", "mfa": "TOTP", "mfa_card_subtitle": "Enter 6-digit code from your authenticator application", "mfa_card_btn_text": "Verify OTP", "mfa_placeholder": "Auth Type", "mfa_qrcode_expired": "Your authenticator app verification has expired. Please scan the QR code again.", "mfa_totp_invalid": "Enter authenticator code is Invalid", "totp_verification": "OTP verification failed.", "accept": "I accept the", "eula": "Terms-Conditions", "eulaPrivacyPolicy": "Privacy-Policy", "must_accept_eula": "You must accept the End-user License Agreement", "no_space_in_password": "No space is allowed at the beginning and the end", "title": "Sign in to Open CERO SMART MOBILITY", "register": "Register", "signup_link": "New user? Register!", "signin_link": "Already have an account? Sign in!", "password": "Password", "invalid_password": "Enter a valid password", "repeat_password": "Repeat Password", "password_rule": "Password must contain at least 8 characters and at least: one lowercase, one uppercase, one number and one special character", "password_not_equal": "Passwords are not identical", "sign_in": "Sign in", "sign_in_otp": "OTP Login", "sign_up": "Register", "mobile_register": "Mobile Registration", "mobile_number_already_exists": "The mobile number already exists.", "sign_out_short": "SO", "sign_out": "Sign out", "email": "Email", "invalid_email": "Enter a valid email address", "forgot_password": "Forgot password?", "email_already_exists": "Email already exist", "email_does_not_exist": "Email does not exist", "cannot_check_email_in_backend": "Cannot check this email in the backend", "wrong_email_or_password": "Wrong email or password", "technical_user_cannot_login_to_ui": "API user and cannot login using the application", "account_suspended": "Your account is suspended", "account_pending": "Your account is pending, check your email", "super_user_account_pending": "Your account is pending, check your email", "account_locked": "Your account is locked", "account_blocked": "Your account is suspended", "account_inactive": "Your account is inactive", "define_password_title": "Define your Password", "define_password_button": "Save", "define_password_hash_not_valid": "Request no longer valid, request a new password", "define_password_success": "Your password has been updated with success", "define_password_error": "Error occurred when changing your password, check the logs", "reset_password_title": "Reset your Password", "reset_password_button": "Reset", "reset_password_confirm": "Do you really want to reset your password?", "reset_password_success": "If your email is valid, you will receive a link to reinitialize your password", "password_strengh": "Password Strengh", "password_one_maj": "A", "password_one_min": "a", "password_one_special": "@", "password_one_number": "0", "password_one_length": ">= 8", "register_user_success1": "Your account has been created with success!", "register_user_success": "Your account has been created with success! Check your email", "register_super_user_success": "Your account has been created with success! An administrator will check your account and activate it", "register_user_error": "Error occurred while creating your account, check the logs", "verify_email_title": "Account Activation", "verify_email_already_active": "Your account is already active!", "verify_email_success": "Your account has been activated with success!", "verify_email_success_inactive": "Your account has been verified with success, an administrator will check and activate your account.", "verify_email_success_set_password": "Your account has been activated, please set up your password to finalize the registration", "verify_email_token_not_valid": "Your account's activation link is no longer valid", "verify_email_email_not_valid": "This Email does not exist", "verify_email_error": "Error occurred while activating your account, check the logs", "verify_email_resend_button": "Send", "verify_email_resend_confirm": "Your account is not active! Do you want to request the activation link email again?", "verify_email_resend_success": "Your account's activation link has been sent with success", "verify_email_resend_error": "Error occurred while sending your account's activation link, check the logs", "verify_email_proceed_with_activation": "Your account is still inactive! Do you want to resend the activation email?", "verify_email_proceed_with_activation_button": "Proceed", "invalid_captcha_token": "Invalid captcha token", "otp_sent_success": "OTP sent successfully", "otp_sent_error": "Error occurred while sending the OTP", "wrong_otp": "Invalid OTP", "otp_verify_failed": "There was an error while verification, Please try again", "otp_verify_success": "<PERSON><PERSON> Succeeded with OTP", "unregistered_mobile": "Mobile number not registered! Please register and try again", "mercedes_data_usage": "SAP Labs France is accessing your car’s data via Mercedes-Benz for the purpose of providing you a high quality of service during the charging session of your vehicle. SAP Labs France is processing the following data: EV battery level and range of your car. For allowing the processing of your data, you’ll have to give the explicit consent in the following steps. For any further information please note our End-user Agreement Usage of the CERO SMART MOBILITY Software."}, "statistics": {"title": "Statistics", "description": "Statistics of charging sessions", "charger_kw_h": "kW.h", "carbon_credit_tons": "kg", "legend_select_unselect_all": "Unselect/Select All", "graphic_title_consumption_y_axis": "Consumption (kW.h)", "graphic_title_carbon_credit_y_axis": "Consumption (kg)", "graphic_title_usage_y_axis": "Usage (hours)", "graphic_title_inactivity_y_axis": "Inactivity (hours)", "graphic_title_transactions_y_axis": "Number of Sessions", "graphic_title_pricing_y_axis": "Price of Sessions in {{currency}}", "multiple_currencies": "(Multiple Currencies)", "graphic_title_month_x_axis": "Months", "transactions_years": "Year", "total_consumption_year": "Total Consumption {{year}}", "total_usage_year": "Total Usage {{year}}", "consumption_per_cs_month_title": "Charging Stations Consumption per Month (Overall: {{total}} kW.h)", "consumption_per_cs_year_title": "Charging Stations Consumption per Year: {{total}} kW.h", "consumption_per_cs_timeFrame_title": "Charging Stations Consumption in selected time frame (Overall: {{total}} kW.h)", "consumption_per_cs_total_title": "Charging Stations Total Consumption: {{total}} kW.h", "consumption_per_user_month_title": "Users Consumption per Month (Overall: {{total}} kW.h)", "consumption_per_user_year_title": "Users Consumption per Year: {{total}} kW.h", "consumption_per_user_timeFrame_title": "Users Consumption in selected time frame: {{total}} kW.h", "consumption_per_user_total_title": "Users Total Consumption: {{total}} kW.h", "carbon_credit_per_cs_month_title": "Charging Stations Consumption per Month (Overall: {{total}} kg)", "carbon_credit_per_cs_year_title": "Charging Stations Consumption per Year: {{total}} kg", "carbon_credit_per_cs_timeFrame_title": "Charging Stations Consumption in selected time frame (Overall: {{total}} kg)", "carbon_credit_per_cs_total_title": "Charging Stations Total Consumption: {{total}} kg", "carbon_credit_per_user_month_title": "Users Consumption per Month (Overall: {{total}} kg)", "carbon_credit_per_user_year_title": "Users Consumption per Year: {{total}} kg", "carbon_credit_per_user_timeFrame_title": "Users Consumption in selected time frame: {{total}} kg", "carbon_credit_per_user_total_title": "Users Total Consumption: {{total}} kg", "usage_per_cs_month_title": "Charging Stations Usage per Month (Overall: {{total}} hours)", "usage_per_cs_year_title": "Charging Stations Usage per Year: {{total}} hours", "usage_per_cs_timeFrame_title": "Charging Stations Usage in selected time frame: {{total}} hours", "usage_per_cs_total_title": "Charging Stations Total Usage: {{total}} hours", "usage_per_user_month_title": "Users Usage per Month (Overall: {{total}} hours)", "usage_per_user_year_title": "Users Usage per Year: {{total}} hours", "usage_per_user_timeFrame_title": "Users Usage in selected time frame: {{total}} hours", "usage_per_user_total_title": "Users Total Usage: {{total}} hours", "inactivity_per_cs_month_title": "Charging Stations Inactivity per Month (Overall: {{total}} hours)", "inactivity_per_cs_year_title": "Charging Stations Inactivity per Year: {{total}} hours", "inactivity_per_cs_timeFrame_title": "Charging Stations Inactivity in selected time frame: {{total}} hours", "inactivity_per_cs_total_title": "Charging Stations Total Inactivity: {{total}} hours", "inactivity_per_user_month_title": "Users Inactivity per Month (Overall: {{total}} hours)", "inactivity_per_user_year_title": "Users Inactivity per Year: {{total}} hours", "inactivity_per_user_timeFrame_title": "Users Inactivity in selected time frame: {{total}} hours", "inactivity_per_user_total_title": "Users Total Inactivity: {{total}} hours", "transactions_per_cs_month_title": "Charging Stations Sessions per Month (Overall: {{total}})", "transactions_per_cs_year_title": "Charging Stations Sessions per Year: {{total}}", "transactions_per_cs_timeFrame_title": "Charging Stations Sessions in selected time frame: {{total}}", "transactions_per_cs_total_title": "Charging Stations Total Sessions: {{total}}", "transactions_per_user_month_title": "Users Sessions per Month (Overall: {{total}})", "transactions_per_user_year_title": "Users Sessions per Year: {{total}}", "transactions_per_user_timeFrame_title": "Users Sessions in selected time frame: {{total}}", "transactions_per_user_total_title": "Users Total Sessions: {{total}}", "pricing_per_cs_month_title": "Charging Stations Prices per Month (Overall: {{total}})", "pricing_per_cs_year_title": "Charging Stations Prices per Year: {{total}}", "pricing_per_cs_timeFrame_title": "Charging Stations Prices in selected time frame: {{total}}", "pricing_per_cs_total_title": "Charging Stations Total Prices: {{total}}", "pricing_per_user_month_title": "Users Prices per Month (Overall: {{total}})", "pricing_per_user_year_title": "Users Prices per Year: {{total}}", "pricing_per_user_timeFrame_title": "Users Prices in selected time frame: {{total}}", "pricing_per_user_total_title": "Users Total Prices: {{total}}", "hours": "hours", "total": "Total", "others": "Others", "category_label": "Category", "dialog": {"consumption": {"export": {"title": "Export Consumption Data", "confirm": "Do you really want to export all the consumption data matching the filters?"}}, "carbon_credit": {"export": {"title": "Export Carbon Credit Data", "confirm": "Do you really want to export all the Carbon Credit data matching the filters?"}}, "usage": {"export": {"title": "Export Usage Data", "confirm": "Do you really want to export all the usage data matching the filters?"}}, "inactivity": {"export": {"title": "Export Inactivity Data", "confirm": "Do you really want to export all the inactivity data matching the filters?"}}, "transactions": {"export": {"title": "Export Sessions Data", "confirm": "Do you really want to export all the sessions data matching the filters?"}}, "pricing": {"export": {"title": "Export Prices Data", "confirm": "Do you really want to export all the prices data matching the filters?"}}}}, "chargers": {"chargers": "Charging Stations", "token": "Token", "user": "User", "charger_not_found": "The charging station has not been found", "vendor": "<PERSON><PERSON><PERSON>", "model": "Model", "name": "Name", "inactivity": "Inactivity", "heartbeat_title": "Status", "charger_disconnected": "Disconnected", "charger_connected": "Connected", "connectors_title": "Connectors", "consumption_title": "Instant Power", "total_consumption_title": "Consumption", "firmware_version": "Firmware Version", "firmware_status": "Firmware Status", "connector_status": "Status", "ocpp_version": "OCPP Version", "ocpp_protocol": "OCPP Protocol", "ocpp_version_title": "OCPP", "private_url": "Private URL", "nb_connected_phase": "Number of Connected Phase", "current_type": "Current Type", "phase_assignment": "Phase Assignment", "phase_combinations": {"three_phased": {"cs_1_g_1": "RST (L1, L2, L3)", "cs_1_g_2": "STR (L2, L3, L1)", "cs_1_g_3": "TRS (L3, L1, L2)"}, "single_phased": {"cs_1_g_1": "R (L1)", "cs_1_g_2": "S (L2)", "cs_1_g_3": "T (L3)"}}, "cant_charge_in_parallel": "Cannot charge in parallel", "share_power_to_all_connectors": "Share the power with all connectors", "exclude_from_power_limitation": "Cannot limit the power", "update_public_cs_error": "Charging Station cannot be made public on a private site", "public": "This charging station is public", "public_charger": "Public", "exclude_smart_charging": "Exclude from Smart Charging", "force_inactive": "This charging station is inactive", "manual_configuration": "Manual configuration", "master_slave": "Master / Slave", "public_url": "Public URL", "current_ip": "Current IP Route", "select_chargers": "Select Charging Station", "serial_number": "Serial Number", "last_reboot": "Last Reboot", "capabilities": "Capabilities", "ocpp_standard_params": "OCPP Standard Params", "ocpp_vendor_params": "OCPP Advanced Params", "ocpp_advanced_command": "OCPP Advanced Commands", "unassigned": "Unassigned", "unassigned_chargers": "Chargers", "maximum_energy": "Maximum Power (Watts)", "maximum_energy_amps": "Maximum Amperage (Amps)", "efficiency": "Efficiency (%)", "invalid_efficiency": "Efficiency must be a positive number not greater 100", "connector": "Connector", "charge_point": "Charge Point", "connector0": "Charger", "connector_type": "Type", "connector_error_title": "Error", "connector_info_title": "Information", "connector_vendor_error_code_title": "Vendor error", "connector_max_power": "Maximum Power (Watts)", "charger_url": "URL", "charger_param_key": "Key", "charger_param_value": "Value", "title": "Charging Station", "titles": "Charging Stations", "status_occupied": "Occupied", "status_available": "Available", "status_preparing": "Preparing", "status_charging": "Charging", "status_suspendedevse": "Suspended EVSE", "status_suspendedev": "Suspended EV", "status_finishing": "Finishing", "status_reserved": "Reserved", "show_active_only": "Connected", "status_unavailable": "Unavailable", "status_faulted": "Faulted", "status_unknown": "Unknown", "status_error_connector_lock_failure": "Connector Lock Failure", "status_error_ev_communication_error": "EV Communication Error", "status_error_ground_failure": "Ground Failure", "status_error_high_temperature": "High Temperature", "status_error_internal_error": "Internal Error", "status_error_local_list_conflict": "Local List Conflict", "status_error_none": "No Error", "status_error_other_error": "Other Error", "status_error_over_current_failure": "Over Current Failure", "status_error_over_voltage": "Over Voltage", "status_error_power_meter_failure": "Power Meter Failure", "status_error_power_switch_failure": "Power Switch Failure", "status_error_reader_failure": "Reader Failure", "status_error_reset_failure": "Reset Failure", "status_error_under_voltage": "Under Voltage", "status_error_weak_signal": "Weak Signal", "status_error_unknown": "Unknown", "status_firmware_idle": "Idle", "status_firmware_downloading": "Downloading", "status_firmware_downloadfailed": "Download Failed", "status_firmware_installing": "Installing", "status_firmware_installationfailed": "Installation Failed", "status_firmware_downloaded": "Downloaded", "status_firmware_installed": "Installed", "reboot_title": "Reboot Charging Station", "soft_reset_title": "Reset Charging Station", "charger_id_not_found": "The charging station ID '{{chargerID}}' has not been found", "no_transaction_found": "No session found for Charging Station '{{chargerID}}' ", "dialog": {"export": {"title": "Export charging stations", "confirm": "Do you really want to export all the charging stations matching the filters?", "error": "Error occurred while trying to export the charging stations, check the logs"}, "exportConfig": {"title": "Export charging station parameters", "confirm": "Do you really want to export all the parameters matching the filter?"}, "localisation": {"title": "Charging stations localisations"}, "settings": {"fixed_url_for_ocpp": "From OCPP 1.6 on, the URL is fixed by the server", "callback_url_for_ocpp": "Callback URL to send HTTP request to the charging station"}, "enable_manual_configuration": {"title": "Enable manual configuration", "confirm": "If you enable the manual configuration, the charging station will not be adjusted with the automatic configuration template anymore. Do you want to continue?"}, "disable_manual_configuration": {"title": "Disable manual configuration", "confirm": "If you disable the manual configuration, the charging station will be adjusted with the automatic configuration template, rebooted and your manual changes will be lost. Do you want to continue?"}, "manual_configuration_error": {"title": "Manual Configuration/Template error", "confirm": "Manual Configuration has been already disabled for this station. Please make sure it boots correctly and that the template can be applied"}}, "force_available_status_action": "Set Available", "force_unavailable_status_action": "Set Unavailable", "reboot_action": "Reboot", "soft_reset_action": "Soft Reset", "clear_cache_action": "Clear cache", "smart_charging_action": "Charge Limitation", "more_actions_action": "More", "assign_sitearea_action": "Assign", "assign_sitearea_action_tooltip": "Assign site area", "smart_charging": {"slider_power_disabled": "Limit is not supported", "date_not_in_past": "End date must not be in the past", "date_out_of_limit": "The plan must not go beyond 24 hours", "not_supported": "This feature is not supported by this brand of charging station", "smart_charging_enabled_static_limitation": "The static limitation is now managed by the smart charging", "charging_station_inactive": "The charging station is not connected to the server", "smart_charging_enabled_charging_profiles": "The charging plans are now managed by the smart charging", "static_limit": "Static Limitation", "charging_profile_limit": "Charging Plan", "debug_charging_station": "Advanced", "debug_charging_station_title": "Retrieve the Charging Plan from Charging Station '{{chargeBoxID}}'", "reset_button": "Reset", "maximum_energy_limit": "Power Limit of '{{chargeBoxID}}'", "invalid_min_duration": "Minimum duration is '{{minDuration}}' min", "invalid_max_duration": "Maximum duration is '{{maxDuration}}' mins", "power_limit_title": "Limit power", "power_limit_confirm": "Do you really want to limit power of '{{chargeBoxID}}'?", "power_limit_success": "The power of the charging station '{{chargeBoxID}}' has been limited successfully", "power_limit_error": "Error occurred while limiting charging station power, check the logs", "power_limit_has_charging_plan_title": "Existing Charging Plan", "power_limit_has_charging_plan_confim": ["There is a conflict with an existing charging plan!", "Do you want to change the plan and adjust it to the new limit?"], "power_limit_plan_title": "Planner Limit power", "power_limit_plan_confirm": "Do you really want to limit the power of '{{chargeBoxID}}' with this charging plan?", "power_limit_plan_clear": "Do you really want to clear the charging profile of '{{chargeBoxID}}'?", "power_limit_plan_success": "Charging profiles for '{{chargeBoxID}}' have been applied successfully", "power_limit_plan_error": "Error occurred while applying the charging profiles, check the logs", "power_limit_plan_not_accepted": "The profile has not been accepted by the charging station '{{chargeBoxID}}'", "clear_profile_title": "Clear Charging Profiles", "clear_profile_confirm": "Do you really want to clear ALL charging profiles of '{{chargeBoxID}}'?", "clear_profile_success": "Charging profiles of charging station '{{chargeBoxID}}' have been deleted successfully", "clear_profile_error": "Error occurred while clearing charging profiles, check the logs", "clear_profile_not_accepted": "The request has not been accepted by the charging station '{{chargeBoxID}}'.", "clear_profiles_button": "Clear Profiles", "current_limit": "Actual power limit planning", "current_limit_title": "Actual power limit", "limit_title": "Limit", "limit_in_amps": "({{limitInAmps}}A)", "limit_in_watts": "'{{limitInWatts}}kW", "no_current_limit": "No limit planning found", "planning_energy_limit": "Charging Plans", "from_date": "From", "to_date": "To", "profile_type": "Type", "stack_level": "Level", "profile_id": "Profile ID", "duration": "Duration", "duration_with_unit": "Duration (mins)", "start_date": "Start", "end_date": "End", "add_schedule_button": "Add Schedule", "remove_schedule_button": "Remove Schedule", "schedule_start_header": "Starting schedule date", "schedule_index_header": "Schedule number", "schedule_duration_header": "Schedule duration (s)", "schedule_limit_header": "Schedule power limit", "empty_schedule_list_error": "You must create at least one schedule", "connectors_all": "all", "charging_profile_not_found": "Charging Profile has not been found", "profile_types": {"relative": "Relative", "absolute": "Temporary Planning", "recurring_daily": "Daily Planning", "recurring_weekly": "Weekly recurring"}, "retrieve_schedule": "Retrieve Plans", "charging_schedule": "Charging Plans of '{{chargeBoxID}}'", "enable_smart_charging_for_site_area_title": "Enable Smart Charging", "enable_smart_charging_for_site_area_body": ["If you enable the smart charging for this site area, the optimization algorithm will take over the current static limitation and the charging plans.", "Do you want to continue?"], "disable_smart_charging_for_site_area_title": "Disable Smart Charging", "disable_smart_charging_for_site_area_body": ["If you disable the smart charging for this site area, all charging plans will be deleted.", "Do you want to continue?"], "clearing_charging_profiles_not_successful_title": "An error occurred while deleting the charging plans, check the logs", "clearing_charging_profiles_not_successful_body": ["Failed to delete the charging plans of the charging stations belonging to the site area '{{siteAreaName}}'!", "Please check the logs and proceed manually with the deletion of the charging plan(s)."], "trigger_smart_charging_title": "<PERSON>gger Smart Charging", "trigger_smart_charging_confirm": "Do you really want to trigger the smart charging algorithm?", "trigger_smart_charging": "<PERSON>gger Smart Charging", "trigger_smart_charging_success": "The smart charging algorithm has been triggered successfully", "trigger_smart_charging_error": "Error occurred while triggering the smart charging algorithm, check the logs", "minutes": "min(s)", "charging_plans": {"charging_plan_name": "Name", "charging_station_id": "Charging Station", "connector_id": "Connector ID", "current_limit": "Current limit", "kind": "Kind", "purpose": "Purpose", "stack_level": "Stack Level", "site_area": "Site Area", "site_area_limit": "Site Area Limit", "redirect": "Navigate to Charging Plans"}}, "more_actions": {"get_diagnostics_title": "Charger '{{chargeBoxID}}' diagnostics", "get_diagnostics_url": "Upload file URL", "get_diagnostics_download_button": "Download", "get_diagnostics_dialog_title": "Retrieve diagnostics", "get_diagnostics_dialog_confirm": "Do you really want to download the charging station '{{chargeBoxID}}' diagnostics file?", "get_diagnostics_success": "Diagnostics download started successfully", "get_diagnostics_error": "Error occurred while starting the diagnostics download, check the logs"}, "action_error": {"transaction_start_title": "Start session error", "transaction_in_progress": "A session is in progress, unable to start a new session", "no_active_transaction": "No active session", "transaction_start_not_available": "Connector is not available, unable to start a session", "transaction_start_charger_inactive": "Charger is not available, unable to start a session", "transaction_start_not_authorized": "You are not authorized to start a session", "transaction_stop_title": "Stop session error", "transaction_stop_not_authorized": "You are not authorized to stop this session", "transaction_stop_not_available": "Connector is not available, unable to stop a session", "transaction_stop_charger_inactive": "Charger is not available, unable to stop a session", "delete_title": "Delete charging station error", "delete_active_transaction": "Charging station deletion is not allowed when a session is ongoing", "command_title": "Charging station command error", "command_charger_disconnected": "Charging station is not available, no command can be sent", "smart_charging_title": "Smart charging error", "smart_charging_charger_disconnected": "Charging station is not available, no command can be sent", "smart_charging_charger_version": "Smart charging needs at least OCPP version 1.6 on the charging station", "session_details_title": "Session details error", "session_details_not_authorized": "You are not authorized to display the session details", "not_authorized": "You are not authorized to perform this action", "ocpp_parameters_should_not_be_empty": "OCPP Parameter key and value should not be empty", "ocpp_parameters_change_title": "OCPP Parameter Error"}, "reboot_confirm": "Do you really want to reboot the charging station '{{chargeBoxID}}'?", "reboot_success": "The charging station '{{chargeBoxID}}' has been rebooted successfully", "reboot_error": "Error occurred while rebooting the charging station, check the logs", "retrieve_configuration_title": "Retrieve Configuration", "retrieve_configuration_confirm": "Do you really want to retrieve the configuration from the charging station '{{chargeBoxID}}'?", "reboot_required_title": "Reboot Required", "reboot_required_confirm": "Do you want to reboot the charging station '{{chargeBoxID}}'?", "ocpp_params_list_error": "No OCPP parameter to display", "ocpp_params_update_from_template_title": "Update OCPP Params", "ocpp_params_update_from_template_confirm": "Do you really want to update the OCPP parameters of the charging station '{{chargeBoxID}}' with the latest template?", "ocpp_params_update_from_template_success": "The OCPP parameters have been updated successfully from the charging station '{{chargeBoxID}}'", "ocpp_params_update_from_template_error": "Error occurred while updating the OCPP parameters of the charging station, check the logs", "set_configuration_title": "Save Change Configuration", "set_configuration_confirm": "Do you really want to change the param '{{key}}' on charging station '{{chargeBoxID}}'?", "soft_reset_confirm": "Do you really want to reset the charging station '{{chargeBoxID}}'?", "soft_reset_success": "The charging station '{{chargeBoxID}}' has been reseted successfully", "soft_reset_error": "Error occurred while resetting the charging station, check the logs", "clear_cache_title": "<PERSON>ache", "clear_cache_confirm": "Do you really want to clear the cache of the charging station '{{chargeBoxID}}'?", "clear_cache_success": "The cache of the charging station '{{chargeBoxID}}' has been cleared successfully", "clear_cache_error": "Error occurred while clearing the cache of the charging station, check the logs", "force_available_status_title": "Force Status to Available", "force_available_status_confirm": "Do you really want to change the status of all the connectors of the charging station '{{chargeBoxID}}' to Available?", "force_available_status_success": "The status of the connectors of the charging station '{{chargeBoxID}}' has been changed successfully", "force_available_status_error": "Error occurred while changing the status of the connectors of the charging station, check the logs", "force_unavailable_status_title": "Force Status to Unavailable", "force_unavailable_status_confirm": "Do you really want to change the status of all the connectors of the charging station '{{chargeBoxID}}' to Unavailable?", "force_unavailable_status_success": "The status of the connectors of the charging station '{{chargeBoxID}}' has been changed successfully", "force_unavailable_status_error": "Error occurred while changing the status of the connectors of the charging station, check the logs", "retrieve_config_success": "The configuration of the charging station '{{chargeBoxID}}' has been retrieved successfully", "change_config_success": "The charging station '{{chargeBoxID}}' has been saved successfully", "change_config_error": "Cannot change the configuration of the charging station, check the logs", "change_config_phase_error": "Cannot assign three phases charging station to a single phase site area", "charge_point_connectors_error": "Error occurred while saving the charging station. The charge points properties do not match its connectors", "change_params_success": "The parameter '{{paramKey}}' has been updated successfully in the charging station '{{chargeBoxID}}'", "change_params_error": "The parameter '{{paramKey}}' has been rejected by the charging station '{{chargeBoxID}}'", "delete_title": "Delete", "delete_confirm": "Do you really want to delete the charging station '{{chargeBoxID}}'?", "delete_success": "The charging station '{{chargeBoxID}}' has been deleted successfully", "delete_error": "Error occurred while deleting the charging station, check the logs", "properties_title": "Properties", "ocpp_parameters_title": "OCPP Parameters", "firmware_update_title": "Firmware Update", "update_firmware_title": "Update Firmware", "update_firmware_confirm": "Do you really want to update the firmware of the charging station '{{chargeBoxID}}'?", "button_update_firmware": "Update", "update_firmware_error": "Error occurred while trying to send the update firmware command to the charging station, check the logs", "update_firmware_success": "The request to update the firmware of the charging station '{{chargeBoxID}}' has been successfully sent", "save_charger": "Save", "stop_transaction_title": "Stop", "stop_transaction_confirm": "Do you really want to stop the session of the charging station '{{chargeBoxID}}'?", "stop_transaction_success": "The session of the charging station '{{chargeBoxID}}' has been stopped successfully", "stop_transaction_error": "Error occurred while stopping the session, check the logs", "stop_transaction_user_not_allowed": "You are not allowed to stop this session", "stop_transaction_missing_active_tag": "The session cannot be stopped on the charging station '{{chargeBoxID}}', you need to have at least one active RFID card", "start_transaction_title": "Start", "start_transaction_button": "Start", "start_transaction_confirm": "Do you really want to start a new session for user '{{userName}}' on the charging station '{{chargeBoxID}}'?", "start_transaction_success": "The session on the charging station '{{chargeBoxID}}' has been started successfully", "start_transaction_error": "Error occurred while trying to start a new session, check the logs", "start_transaction_missing_active_tag": "The session cannot be started for user '{{userName}}' on the charging station '{{chargeBoxID}}', the user needs to have at least one active RFID card", "start_transaction_user_not_allowed": "You are not allowed to start a session on this charging station", "unlock_connector_title": "Unlock", "unlock_connector_button": "Unlock", "unlock_connector_confirm": "Do you really want to unlock the connector '{{connectorId}}' of the charging station '{{chargeBoxID}}'?", "unlock_connector_success": "The connector '{{connectorId}}' of the charging station '{{chargeBoxID}}' has been unlocked successfully", "unlock_connector_error": "The Charging Station has not been able to unlock the connector", "unlock_connector_not_supported_error": "This Charging Station does not support the unlock of the connector", "connector_type_unknown": "Not set", "connector_type_all": "All Connectors", "connector_type_type2": "Type 2", "connector_type_type1": "Type 1", "connector_type_type1ccs": "Type 1 - Combo CCS", "connector_type_type3c": "Type 3C", "connector_type_domestic": "Domestic", "connector_type_combo": "Type 2 - Combo CCS", "connector_type_chademo": "CHAdeMO", "connector_type_gta": "GB T AC", "connector_type_gtd": "GB T DC", "connector_type_tesla": "Tesla", "direct_current": "Direct Current", "alternating_current": "Alternating Current", "direct_and_alternating_current": "Direct and Alternating Current", "single_phase": "Single phase", "tri_phases": "Three phases", "site_area": "Site Area", "assign_site_area": "Assign Site Area", "change_site_area": "Change", "site": "Site", "invalid_url": "Invalid URL", "invalid_power": "Power value must be a positive number", "invalid_voltage": "Voltage value must be a positive number", "invalid_amperage": "Amperage value must be a positive number", "invalid_amperage_phases": "Amperage must be divisible by the number of phases", "unsaved_title": "Unsaved changes", "unsaved_confirmed": "Do you want to save changes?", "changes_cancelled": "Changes have been cancelled", "unsaved_changes": "You have unsaved changes. Save or cancel them.", "button_retrieve_configuration": "OCPP Retrieve Configuration", "button_force_ocpp_params_update_from_template": "Update OCPP Configuration from Template", "created_on": "Creation date", "start_transaction_details_title": "Start session on charging station '{{chargeBoxID}}'", "start_transaction": "Start session", "power_limit_unit": "Connector Power Unit", "watt": "<PERSON>", "amper": "Ampere", "voltage": "Phase/Neutral Voltage (V)", "amperage": "Total Amperage (A)", "amperagePerPhase": "Amperage per phase (A)", "session_details": "Session", "qr_code_generation_error": "Error occurred while generating QR code", "tabs": {"list": "Charging Stations", "charging_plans": "Charging Plans", "in_error": "In Error", "connection": "Onboard New Station", "evse": "Reservation", "evsee": "Reservation"}, "errors": {"missing_settings": {"title": "Missing Settings", "description": "Required settings are missing", "action": "Maintain missing required settings in the charging station's configuration"}, "connection_broken": {"title": "Connection Lost", "description": "The server can't reach the charging station", "action": "Manual intervention is required. Check your network and/or the charging station's configuration"}, "missing_site_area": {"title": "Not Assigned to a Site Area", "description": "The charging station needs to be assigned to a site area", "action": "Maintain the site area in the charging station's configuration"}, "connector_error": {"title": "Connector Error", "description": "The charging station sent an error code for one of its connectors", "action": "Check the error code details and eventually restart the charging station"}}, "connections": {"registration_tokens_title": "Charging Station Registration Tokens", "registration_token_site_area_name": "Token for Site Area '{{siteAreaName}}'", "registration_token_creation_title": "Create Registration Token", "registration_token_creation_confirm": "Do you really want to create a registration token for charging stations of this site area?", "registration_token_creation_success": "Registration token has been created successfully", "registration_token_creation_error": "Error occurred while creating a registration token, check the logs", "registration_token_site_admin_creation_error": "You must provide a site linked to this registration token", "registration_token_revoke_title": "Revoke Registration Token", "registration_token_revoke_confirm": "Do you really want to revoke the registration token?", "registration_token_revoke_success": "Registration token has been revoked successfully", "registration_token_revoke_error": "Error occurred while revoking a registration token, check the logs", "registration_token_update_title": "Update Registration Token", "registration_token_update_confirm": "Do you really want to update the registration token?", "registration_token_update_success": "Registration token has been updated successfully", "registration_token_update_error": "Error occurred while updating a registration token, check the logs", "registration_token_not_found": "Registration token has not been found", "registration_token_delete_title": "Delete Registration Token", "registration_token_delete_confirm": "Do you really want to delete the registration token?", "registration_token_delete_success": "Registration token has been deleted successfully", "registration_token_delete_error": "Error occurred while deleting a registration token, check the logs", "registration_token_url": "Registration URL", "registration_token_valid": "<PERSON><PERSON>", "registration_token_expired": "Expired", "registration_token_revoked": "Revoked", "registration_token_error": "Error occurred while retrieving the token, check the logs", "token_restrict_site_area": "Restrict access to one Site Area", "url": "OCPP URL", "copy_url_tooltip": "Copy OCPP URL", "url_copied": "OCPP Url has been copied to your clipboard", "generate_connection_url": "Connect a Charging Station", "ocpp_15_soap": "OCPP 1.5 SOAP", "ocpp_16_json": "OCPP 1.6 JSON", "ocpp_16_soap": "OCPP 1.6 SOAP", "ocpp_15_soap_secure": "Secure OCPP 1.5 SOAP", "ocpp_16_json_secure": "Secure OCPP 1.6 JSON", "ocpp_16_soap_secure": "Secure OCPP 1.6 SOAP", "charging_station_shedules_delete_title": "Delete Scheduling", "charging_station_shedules_delete_confirm": "Do you really want to delete the Scheduling?", "charging_station_shedules_delete_success": "Scheduling has been deleted successfully", "charging_station_shedules_delete_error": "Error occurred while deleting a Scheduling, check the logs", "charging_station_shedules_creation_success": "Station schedule has been created successfully", "charging_station_shedules_creation_error": "Error occurred while creating a Station schedule, check the logs", "get_Stations_error": "Error at Stations fetching time", "charging_station_shedules_update_success": "Station schedule has been updated successfully", "charging_station_shedules_update_error": "Error occurred while updating a Station schedule, check the logs", "charging_station_shedules_not_found": "Station schedule has not been found"}}, "logs": {"logging_number": "Log(s)", "debug": "Debug", "info": "Info", "warning": "Warning", "error": "Error", "status": "Status", "level": "Level", "levels": "Levels", "date": "Date", "source": "Source", "host": "Host", "process": "Process", "user": "User", "users": "Users", "on_user": "Action on User", "action": "Action", "actions": "Actions", "type": "Type", "type_security": "Security", "type_regular": "Regular", "message": "Message", "module": "<PERSON><PERSON><PERSON>", "method": "Method", "search_one_minute": "Last Minute", "search_10_minutes": "Last 10 Minutes", "search_30_minutes": "Last 30 Minutes", "search_one_hour": "Last Hour", "search_24_hours": "Last 24 Hours", "search_today": "Today", "search_yesterday": "Yesterday", "search_this_week": "This Week", "search_last_week": "Last Week", "search_this_month": "This Month", "search_last_month": "Last Month", "select_actions": "Select Actions", "redirect": "Navigate to Logs", "no_logs": "No logs matching the filters", "dialog": {"export": {"title": "Export Logs", "confirm": "Do you really want to export all the logs matching the filters?", "error": "Error occurred while trying to export the logs"}}}, "corps": {"name": "Account name", "admin_name": "Admin", "cg": "Charge Group", "gst": "GSTNo", "pt": "PAN_TAN", "created_on": "Created On", "pricing": "Pricing", "corp_already_used": "The RFID card '{{cname}}' already exists", "create_error": "Error occurred while creating the Corporation", "create_success": "The Account named '{{cname}}' has been created successfully", "update_success": "The Account '{{cname}}' has been updated successfully", "update_error": "Error occurred while updating the Account", "corp_not_found": "Account has not been found", "corp_error": "Error occurred while retrieving the Account", "delete_title": "Delete Account", "delete_confirm": "Do you really want to delete the Account '{{name}}'?", "delete_success": "The Account '{{id}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Account"}, "users": {"id": "Id", "tabs": {"list": "Users", "in_error": "In Error", "corps": "Accounts", "tags": "RFID Cards", "billing": "Billing"}, "security": "Security", "import_users": "Import users", "import_users_message": "The import will discard all other previously uploaded users not yet imported in the database, do you want to continue?", "import_users_message_auto_activate": "The import will discard all other previously uploaded users not yet imported in the database and users will be automatically activated, do you want to continue?", "import_users_auto_activation": "Automatically activate new users", "import_users_success": "{{inSuccess}} users have been uploaded successfully and will be processed asynchronously", "import_users_partial": "{{inSuccess}} users have been uploaded successfully and will be processed asynchronously and {{inError}} failed, check the logs", "import_users_error": "{{inError}} users failed to be uploaded", "import_no_users": "No user have been uploaded", "access_mode": "Access Mode", "allow_free_access": "Free access to all charging stations", "user_without_freeAccess": "Paying access", "user_with_freeAccess": "Free access", "number_of_sites": "Nb Sites", "number_of_transactions": "Nb Sessions", "eula_accepted_on": "EULA Accepted On", "edit_profile_short": "EP", "profile": "Profile", "wallet": "Wallet", "razorpay_wallet": "Razorpay Wallet", "virtual_wallet": "Virtual Wallet", "edit_profile": "Edit Profile", "user_number": "User(s)", "technical_user": "API User", "non_technical_user": "Normal User", "role": "Role", "corp": "Account", "roles": "Roles", "role_admin": "Admin", "role_super_admin": "Super Admin", "role_basic": "Basic", "role_demo": "Demo", "role_corp": "Account <PERSON><PERSON>", "role_emp": "Employee", "role_comp_admin": "Company Admin", "company": "Company", "role_company_admin": "Company Admin", "role_transport_admin": "Transport Admin", "role_transport_manager": "Transport Manager", "role_invalid": "Invalid role", "role_mult_all": "All Roles", "role_mult_admin_demo": "Admin or Demo", "user_not_found": "User has not been found", "user_id_not_found": "User '{{userId}}' has not been found", "invalid_phone_number": "Invalid phone number", "missing_tag": "This list must contain at least one RFID card", "invalid_tag_id": "Only numbers and letters are allowed", "invalid_plate_id": "Only capital letters, numbers and '-' are allowed", "locale": "Language", "locale_invalid": "Invalid locale", "locale_desc_en_US": "English", "locale_desc_fr_FR": "French", "locale_desc_es_ES": "Spanish", "locale_desc_de_DE": "German", "locale_desc_pt_PT": "Portuguese", "locale_desc_it_IT": "Italian", "locale_desc_cs_CZ": "Czech", "locale_desc_en_AU": "Australian", "role_unknown": "Unknown", "locale_unknown": "Unknown", "status": "Status", "picture": "Picture", "show_image": "Show Pictures", "sites": "Sites", "assigned_sites_to_user": "Sites assigned to '{{userName}}'", "status_invalid": "Invalid status", "status_active": "Active", "status_blocked": "Suspended", "status_inactive": "Inactive", "status_locked": "Locked", "status_pending": "Pending", "status_unknown": "Unknown", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "plate_id": "Plate", "technical_title": "API User", "technical": "API User (used for backend to backend communications only)", "tags": "RFID Cards", "tag_ids_help": "Enter a list of RFID cards separated by a comma", "inumber": "Account Number", "cost_center": "Cost Center", "phone": "Phone", "mobile": "Mobile", "notifications_active": "Enable the Notifications (Email, Mobile Phone)", "created_on": "Created On", "created_by": "Created By", "changed_on": "Changed On", "changed_by": "Changed By", "save_profile": "Save Profile", "save_address": "Save Address", "save_password": "Save Password", "save_miscs": "Save <PERSON>", "miscs": "Miscs", "email_already_used": "Email already used by another user", "edit": "Edit", "delete": "Delete", "delete_title": "Delete User", "delete_button": "Delete", "delete_confirm": "Do you really want to delete the user '{{userFullName}}'?", "delete_success": "The user '{{userFullName}}' has been deleted successfully", "delete_billing_error": "Error occurred while deleting the user due to the billing system, check the logs", "delete_error": "Error occurred while deleting the user, check the logs", "remove_sites_title": "Remove Site(s)", "remove_sites_confirm": "Are you sure to remove the selected site(s) from this user?", "remove_sites_success": "The site(s) have been removed successfully", "remove_sites_error": "Error occurred while removing the site(s) from the user, check the logs", "export_users_title": "Export Users", "export_users_confirm": "Do you really want to export the User list to a csv format?", "export_users_error": "Error occurred while trying to export the Users", "update_success": "The user '{{userFullName}}' has been updated successfully", "update_sites_success": "The sites have been added successfully", "changed_by_other_user": "The user '{{userFullName}}' has been changed by another user and will be reloaded", "update_error": "Error occurred while updating the user, check the logs", "user_do_not_exist": "User does not exist", "title": "User", "picture_size_error": "The picture size limit exceeded, limit is below '{{maxPictureKb}}' Kb", "create_success": "The user '{{userFullName}}' has been created successfully", "create_error": "Error occurred while creating the user, check the logs", "no_users": "No users found", "select_users": "Select User", "update_set_site_admin_success": "The user is now administrator of the site '{{siteName}}'", "update_remove_site_admin_success": "The user is no more administrator of the site '{{siteName}}'", "update_site_admin_role_error": "Error occurred while trying to update the user right to the site '{{siteName}}', check the logs", "update_set_site_owner_success": "The user is now owner of the site '{{siteName}}'", "update_remove_site_owner_success": "The user is no more owner of the site '{{siteName}}'", "update_site_owner_role_error": "Error occurred while trying to update the user right to the site '{{siteName}}', check the logs", "redirect": "Navigate to User", "display_sites": "Display Sites", "connectors": {"connect": "Connect", "revoke": "Revoke", "created_on": "Created on", "expired_on": "Expires on", "not_connected": "Not connected"}, "invoicing": {"errors": {"no_invoice_found": "No invoice available for your account", "unable_to_get_invoice": "Error occurred while retrieving your invoice, check the logs"}}, "errors": {"inactive_user": {"title": "Inactive Users", "description": "This user is not active.", "action": "Check the status of this user."}, "unassigned_user": {"title": "Not Assigned to a Site", "description": "This user is not assigned to a site.", "action": "Assign this user to at least one site as the organization component is active."}, "inactive_user_account": {"title": "Inactive User Account", "description": "This user has not logged in for at least 6 months.", "action": "Delete or disable this user account."}, "failed_billing_synchro": {"title": "Billing synchronization failed", "description": "The synchronization of this user with the billing system failed.", "action": "Re-synchronize it manually."}}, "export_tags_title": "Export RFID cards", "export_tags_confirm": "Do you really want to export the RFID card list to a CSV format?", "export_tags_error": "Error occurred while trying to export the RFID Cards, check the logs"}, "invoices": {"tabs": {"list": "Invoices"}, "tooltips": {"download": "Download invoice", "pay": "Pay invoice"}, "status": {"paid": "Paid", "unpaid": "Unpaid", "draft": "Draft", "deleted": "Deleted", "uncollectible": "Uncollectible", "void": "Cancelled"}, "number": "Invoice Number", "createdOn": "Invoice Date", "price": "Invoice Price", "amount": "Amount", "tax": "Tax", "taxedAmount": "Taxed Amount", "cannot_retrieve_invoices": "Cannot retrieve the list of invoices", "cannot_download_invoice": "Cannot download invoice", "number_of_items": "Sessions", "pay": "Pay", "user": "User", "failed_download": "Invoice download failed."}, "transfers": {"tabs": {"list": "Transfers"}, "tooltips": {"download": "Download commission invoice", "finalize": "Finalize transfer", "send": "Send transfer"}, "status": {"draft": "Draft", "pending": "Pending", "finalized": "Finalized", "transferred": "Transferred"}, "id": "ID", "accountExternalID": "Connected Account ID", "collected_funds": "Collected Funds", "document_number": "Invoice", "platform_fee_amount": "Collected <PERSON><PERSON>", "platform_fee_tax_inclusive": "Fees (incl. taxes)", "transferAmount": "Transfer", "number_of_items": "Sessions", "transferExternalID": "External ID", "accountOwner": "Business Owner", "cannot_finalize_transfer": "Operation failed - The transfer could not be finalized", "cannot_send_transfer": "Operation failed - The transfer has not been sent to the billing system", "cannot_download_commission_incoice": "Cannot download commission invoice"}, "tags": {"id": "RFID", "not_active": "This RFID card is not active", "issuer": "Organization Owner", "visual_id": "Visual RFID", "visual_id_instruction": "Please fill the ID that is displayed on the RFID card", "status": "Status", "activated": "Active", "deactivated": "Inactive", "sessions": "Sessions", "activate_title": "RFID card activation", "activate_confirm": "Do you really want to activate the RFID card '{{tagID}}'? Once activated, this RFID card will be usable to start sessions.", "deactivate_title": "RFID card deactivation", "deactivate_confirm": "Do you really want to deactivate the RFID card '{{tagID}}'? Once deactivated, this RFID card will no longer be usable to start sessions.", "deactivate_success": "The RFID card '{{tagID}}' has been deactivated successfully", "deactivate_error": "Error occurred while deactivating the RFID card", "activate_success": "The RFID card '{{tagID}}' has been activated successfully", "activate_error": "Error occurred while activating the RFID card", "delete_confirm": "Do you really want to delete the RFID card '{{id}}'?", "delete_title": "Delete RFID card", "delete_success": "The RFID card '{{id}}' has been deleted successfully", "delete_error": "Error occurred while deleting the RFID card", "create_success": "The RFID card '{{tagID}}' has been created successfully", "create_error": "Error occurred while creating the RFID card", "register_success": "The RFID card '{{visualID}}' has been registered successfully", "register_error": "Error occurred while registering the RFID card", "update_success": "The RFID card '{{tagID}}' has been updated successfully", "update_by_visual_id_success": "The RFID card '{{visualID}}' has been updated successfully", "update_error": "Error occurred while updating the RFID card", "tag_error": "Error occurred while retrieving the RFID card", "tag_not_found": "RFID card has not been found", "select_tags": "Select RFID card", "tag_id_already_used": "The RFID card '{{tagID}}' already exists", "tag_visual_id_already_used": "The visualID '{{visualID}}' already exists", "tag_visual_id_does_not_match_tag": "The visualID '{{visualID}}' does not match any RFID card", "tag_inactive": "The RFID card '{{visualID}}' is inactive and cannot be assigned", "redirect": "Navigate to RFID Cards", "title": "RFID card", "default_tag": "Default RFID card", "delete_tags_title": "Delete RFID card(s)", "delete_tags_confirm": "Do you really want to delete '{{quantity}}' RFID card(s)?", "delete_tags_success": "{{inSuccess}} RFID cards have been deleted successfully", "delete_tags_partial": "{{inSuccess}} RFID cards have been deleted successfully and {{inError}} RFID cards have encountered an error, check the logs", "delete_tags_error": "{{inError}} RFID cards failed to be deleted, check the logs", "delete_tags_unexpected_error": "Unexpected error occurred while deleting the RFID cards, check the logs", "delete_no_tag": "No RFID card has been deleted", "import_tags": "Import RFID cards", "import_tags_message": "The import will discard all other previously uploaded RFID cards not yet imported in the database, do you want to continue?", "import_tags_message_auto_activate": "The import will discard all other previously uploaded RFID cards not yet imported in the database, do you want to continue?", "import_tags_success": "{{inSuccess}} RFID cards have been uploaded successfully and will be processed asynchronously", "import_tags_partial": "{{inSuccess}} RFID cards have been uploaded successfully and will be processed asynchronously and {{inError}} failed, check the logs", "import_tags_error": "{{inError}} RFID cards failed to be uploaded", "import_no_tags": "No RFID card have been uploaded", "import_tags_auto_activation": "Automatically activate new tags"}, "tenants": {"title": "Organization", "name": "Name", "shortname": "Short Name", "logo": "Logo", "tenant_management": "Organization Management", "tenant_not_found": "This organization has not been found", "email": "Email", "subdomain": "Subdomain", "iosPackage": "ios Package", "androidPackage": "Android Package", "redirectWebsiteUrl": "Redirect Website URL", "create_success": "The organization '{{name}}' has been created successfully", "create_error": "Error occurred while creating the organization, check the logs", "subdomain_already_used": "The subdomain '{{subdomain}}' is already used", "update_error": "Error occurred while updating the organization, check the logs", "error_subdomain": "Only lowercase and numbers are allowed", "update_success": "The organization '{{name}}' has been updated successfully", "delete_confirm": "Do you really want to delete the organization '{{name}}'?", "delete_title": "Delete Organization", "delete_success": "The organization '{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting the organization, check the logs", "save_error_refund": "Pricing must be active to use refunding", "save_error_roaming": "You cannot activate both the OCPI and OICP roaming components", "save_error_billing": "Pricing must be active to use billing", "save_error_billing_platform": "Billing must be active to use Billing Platform", "save_error_smart_charging": "Organization must be active to use Smart Charging", "save_error_asset": "Organization must be active to use Asset", "save_error_car_connector": "Car Management must be active to use Car Connector", "smart_charging_still_active_for_site_area": ["Smart Charging is still active for certain Site Areas.", "Please deactivate Smart Charging for every Site Area in the tenant."], "logo_size_error": "The logo size limit exceeded, limit is below {{maxPictureKb}} Kb"}, "accounts": {"title": "Connected Accounts", "list": {"select_account": "Select Connected Account", "add_account": "Add Connected Account", "business_owner": "Business Owner", "company_name": "Company Name", "account_status": "Status", "account_id": "Connected Account ID", "billing_account": "Connected Account"}, "message": {"create_success": "Account was added successfully", "create_error": "Error occurred while adding the account, check the logs", "onboard_success": "The onboarding process has been triggered. A mail has been sent to the business owner", "onboard_error": "Error occurred while onboarding the account, check the logs"}, "status": {"account_idle": "Idle", "account_pending": "Pending", "account_active": "Active"}, "onboarding": {"onboard_action": "Send the onboarding email to the business owner", "onboarding_title": "Open CERO SMART MOBILITY - Account Onboarding", "onboarding_message": "You have been invited to onboard your company as an account connected to the Open CERO SMART MOBILITY platform.", "onboarding_message_to_proceed": "Click the link below to complete the onboarding process.", "onboarding_congratulations": "Congratulations", "onboarding_process_completed": "The onboarding of your account has been completed.", "onboarding_button_proceed": "Proceed", "onboarding_navigate_to_dashboard": "Navigate to the dashboard", "onboarding_process_failed": "The activation of your account has failed - contact the support to get help!"}, "platform_fee": {"platform_fee_group_title": "Platform Fee", "platform_flat_fee": "Flat fee per session", "platform_flat_fee_invalid": "Flat fee entered must be a positive number", "platform_fee_percentage": "Fee percentage per session", "platform_fee_percentage_invalid": "Fee percentage must be between 0% to 100%"}}, "settings": {"settings_not_saved_title": "Settings not Saved", "settings_not_saved": "You must save your settings first", "tabs": {"ocpi": "Roaming", "oicp": "Roaming Hubject", "refund": "Refunding", "pricing": "Pricing", "billing": "Billing", "sms": "SMS", "analytics": "Analytics", "smart_charging": "Smart Charging", "asset": "<PERSON><PERSON>", "car_connector": "Car Connector", "taxes": "Taxes", "smtp": "SMTP", "page_setup": "Page Setup"}, "ocpi": {"gireve": {"title": "<PERSON><PERSON><PERSON>"}, "setting_do_not_exist": "OCPI Settings not found", "setting_not_found": "OCPI settings has not been found", "create_success": "OCPI settings have been created successfully", "create_error": "Error occurred while creating the OCPI settings, check the logs", "update_success": "OCPI settings have been updated successfully", "update_error": "Error occurred while updating the OCPI settings, check the logs", "description": ["CERO SMART MOBILITY provides a certified service to connect to an external E-Roaming Platform.", "This feature exposes real-time the charging station infrastructure and availability via the OCPI standard protocol (Open Charge Point Interface).", "CERO SMART MOBILITY is currently using the Gireve Inter-Operability Platform (IOP)."]}, "oicp": {"hubject": {"title": "Hubject"}, "setting_do_not_exist": "OICP Settings not found", "setting_not_found": "OICP settings has not been found", "create_success": "OICP settings have been created successfully", "create_error": "Error occurred while creating the OICP settings, check the logs", "update_success": "OICP settings have been updated successfully", "update_error": "Error occurred while updating the OICP settings, check the logs", "description": ["CERO SMART MOBILITY provides a certified service to connect to an external E-Roaming Platform.", "This feature exposes real-time the charging station infrastructure and availability via the OICP protocol (Open InterCharge Protocol).", "CERO SMART MOBILITY is currently using the Hubject B2B Service Platform (HBS)."]}, "refund": {"description": ["CERO SMART MOBILITY provides a refund service for an external expense system.", "This feature allows the user to push his sessions to an expense system and get reimbursed of the electricity he consumed.", "CERO SMART MOBILITY is currently using SAP Concur. More technical information available at "], "not_found": "Refunding settings not found", "create_success": "Refunding settings have been created successfully", "create_error": "Error occurred while creating the Refunding settings, check the logs", "update_success": "Refunding settings have been updated successfully", "update_error": "Error occurred while updating the Refunding settings, check the logs", "synchronize_dialog_refund_title": "Synchronize Refunded Transactions", "synchronize_dialog_refund_confirm": "Do you really want to synchronize the refunded transactions?", "synchronize_started": "Refunded transactions synchronization has been started", "synchronize_success": "Refunded transactions have been synchronized successfully", "synchronize_error": "Error occurred while synchronizing the refunded transactions, check the logs", "connection_error": "Error in connecting to the refunding system, check your connection in your profile", "concur": {"title": "SAP Concur", "authentication_url": "Authentication Url", "app_url": "Application Url", "api_url": "API Url", "client_id": "Client ID", "client_secret": "Client Secret", "payment_type_id": "Payment Type ID", "expense_type_code": "Expense Type Code", "policy_id": "Policy ID", "report_name": "Report Name", "link_success": "Your account has been successfully linked with Concur", "link_error": "Error occurred while linking your account with <PERSON><PERSON><PERSON>, check the logs", "revoke_success": "Your connection with <PERSON><PERSON><PERSON> has been successfully revoked", "revoke_error": "Error occurred while revoking your connection with <PERSON><PERSON><PERSON>, check the logs"}}, "pricing": {"title": "Pricing", "description": "The Pricing interface enables pricing capabilities for charging stations providers.", "not_found": "Pricing settings not found", "create_success": "Pricing settings have been created successfully", "create_error": "Error occurred while creating the Pricing settings, check the logs", "update_success": "Pricing settings have been updated successfully", "update_error": "Error occurred while updating the Pricing settings, check the logs", "simple_pricing_title": "Built-in Pricing Module", "full_description": "Open CERO SMART MOBILITY provides a pricing module which allows a flexible definition of your company's pricing strategy.", "pricing_currency_changed_title": "Modification of the currency", "pricing_currency_changed_confirm": ["You are about to change the currency. This will force you to reconnect after saving.", "<strong> WARNING ! </strong>Once saved, the currency cannot be modified anymore!", "Do you confirm that the selected value is correct?"], "settings": {"title": "Pricing Settings", "price": "Price (per kW.h)", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "end_date_error": "The date must be greater than the valid-from date", "valid_from": "<PERSON><PERSON>", "valid_to": "<PERSON><PERSON>", "flat_fee": "Flat Fee", "energy": "Energy", "charging_time": "Charging Time", "parking_time": "Parking Time", "create_title": "Create Pricing Definition", "pricing_definition_description": "Description", "pricing_definition_name": "Pricing Definition Name", "pricing_definition_creation_confirm": "Do you really want to create a pricing definition?", "pricing_definition_creation_success": "Pricing definition has been created successfully", "pricing_definition_creation_error": "Error occurred while creating a pricing definition, check the logs", "pricing_definition_site_admin_creation_error": "You must provide a site linked to this pricing definition", "pricing_definition_update_title": "Update Pricing definition", "pricing_definition_update_confirm": "Do you really want to update the pricing definition?", "pricing_definition_update_success": "Pricing definition has been updated successfully", "pricing_definition_update_error": "Error occurred while updating a pricing definition, check the logs", "pricing_definition_not_found": "Pricing definition has not been found", "pricing_definition_delete_title": "Delete Pricing Definition", "pricing_definition_delete_confirm": "Do you really want to delete the pricing definition?", "pricing_definition_delete_success": "Pricing definition has been deleted successfully", "pricing_definition_delete_error": "Error occurred while deleting a pricing definition, check the logs", "pricing_definition_error": "Error occurred while retrieving the pricing definition, check the logs", "pricing_definition_time_range_error": "Start and end time must be different", "pricing_max_energy_error": "Maximum energy must be greater than the minimum energy", "pricing_max_duration_error": "Maximum duration must be greater than the minimum duration", "connector_type": "Connector Type", "connector_power": "Connector Power", "value": "Value", "unit": "Unit", "charging_station": "Charging Station", "pricing_dimensions_title": "Dimensions", "pricing_definition_title": "Pricing Definition", "step_size": "Step Size", "connector_power_unit": "kW", "flat_fee_unit": "{{currency}}/session", "energy_unit": "{{currency}}/kWh", "charging_time_unit": "{{currency}}/hour", "parking_time_unit": "{{currency}}/hour", "flat_fee_formatted_price": "{{price}}/session", "energy_formatted_price": "{{price}}/kWh", "charging_time_formatted_price": "{{price}}/hour", "parking_time_formatted_price": "{{price}}/hour", "energy_step_unit": "Wh", "time_step_unit": "Minutes", "restrictions_title": "Restrictions", "restriction_max_energy": "Max Energy", "restriction_max_energy_unit": "kWh", "restriction_min_energy": "Min Energy", "restriction_min_energy_unit": "kWh", "restriction_max_duration": "Max duration", "restriction_max_duration_unit": "Minutes", "restriction_min_duration": "Min duration", "restriction_min_duration_unit": "Minutes", "restriction_time_range": "Time range", "restriction_start_time": "Start time", "restriction_end_time": "End time", "restriction_days_of_week": "Days of week"}, "billing": {"description": "The billing interface enables billing capabilities for charging station providers.", "deactivated_setting_message": "The billing of the charging sessions is active, the connection settings cannot be changed anymore.", "not_found": "Billing settings not found", "not_properly_set": "Billing settings not properly set", "create_success": "Billing settings have been created successfully", "create_error": "Error occurred while creating the billing settings, check the logs", "update_success": "Billing settings have been updated successfully", "update_error": "Error occurred while updating the billing settings, check the logs", "check_connection": "Check Connection", "connection_success": "Connection to the billing system was tested successfully", "connection_error": "Impossible to connect to the billing system. Check your secret key.", "force_synchronize": "Force Billing Synchronization", "payment_methods_create_title": "Create a payment method", "payment_methods_create_success": "The payment method ending with '{{last4}}' has been created successfully", "payment_methods_create_error": "Error occurred while creating the payment method, check the logs", "payment_methods_create_error_card_declined": "Your card has been declined, please provide a valid payment method", "payment_methods_card_declined": "Card declined", "payment_methods_delete_title": "Delete payment method", "payment_methods_delete_confirm": "Do you really want to delete the payment method ending with '{{last4}}'?", "payment_methods_delete_success": "The payment method ending with '{{last4}}' has been deleted successfully", "payment_methods_delete_error": "Error occurred while deleting the payment method, check the logs", "payment_methods_expired": "Expired", "payment_methods_valid": "<PERSON><PERSON>", "payment_methods_expire_soon": "Expires soon", "payment_methods_expiring_on": "Expires on", "payment_methods_ending_with": "Ending with", "payment_methods_status": "Status", "payment_methods_type": "Type", "payment_methods_brand": "Brand", "payment_methods_card": "Card", "payment_methods_verification_code": "Verification code", "payment_methods_conditions": "For security reasons, we do not store nor process your bank and card details directly. The collected information is sent via a secure channel to our STRIPE partner which provides the billing and payment infrastructure.", "payment_methods_check": "I have read the End-user License Agreement and agree that my bank details will be used for paying future EV Charging Sessions.", "payment_methods_card_number_error": "Invalid card number", "payment_methods_expiration_date_error": "Invalid expiration date", "payment_methods_cvc_error": "Invalid verification code", "transaction_billing_activation": "Billing Activation", "transaction_billing_activation_title": "Activate Billing of Transactions", "transaction_billing_activation_confirm": ["Do you really want to activate the billing of charging sessions? ", "<strong> WARNING ! </strong> Once activated, you will NOT be able to deactivate it !"], "transaction_billing_activation_success": "Billing of charging sessions has been successfully activated", "transaction_billing_activation_error": "Billing of charging sessions cannot be activated", "billing_clear_test_data": "Delete Test Data", "billing_clear_test_data_title": "Test Data Deletion", "billing_clear_test_data_confirm": "Do you really want to delete all billing test data?", "billing_clear_test_data_success": "The test data has been deleted", "billing_clear_test_data_error": "Operation failed. The test data deletion has been aborted", "payment_methods": "Payment Methods", "user": {"force_synchronize_user_dialog_title": "Synchronize Billing User", "force_synchronize_user_dialog_confirm": "Do you really want to synchronize the user '{{userFullName}}' in the billing system?", "force_synchronize_user_success": "The user '{{userFullName}}' has been synchronized successfully", "force_synchronize_user_failure": "Failed to synchronize the user, check the logs"}, "stripe": {"description": "CERO SMART MOBILITY allows to send invoices to the charging station users with Stripe ...", "title": "Stripe", "url": "URL of Stripe Dashboard", "public_key": "Publishable API Key", "invalid_public_key": "Publishable API key is not plausible", "secret_key": "Secret API Key", "invalid_secret_key": "Secret API key is not plausible", "billing_method": "Billing Method", "immediate_billing": "Immediate Billing", "periodic_billing": "Periodic Billing", "tax": "Tax", "platform_fee_tax": "Platform Fee Tax", "no_tax": "No Tax"}, "tap": {"title": "Tap", "url": "URL of Tap Dashboard", "public_key_web": "Publishable API Key Web", "invalid_public_key_web": "Publishable API key Web is not plausible", "public_key": "Publishable API Key", "invalid_public_key": "Publishable API key is not plausible", "secret_key": "Secret API Key", "invalid_secret_key": "Secret API key is not plausible", "billing_method": "Billing Method", "immediate_billing": "Immediate Billing", "periodic_billing": "Periodic Billing", "tax": "Tax", "platform_fee_tax": "Platform Fee Tax", "no_tax": "No Tax"}, "stripe-wallet": {"description": "CERO SMART MOBILITY allows to send invoices to the charging station users with Stripe ...", "title": "Stripe Wallet", "url": "URL of Stripe Dashboard", "public_key": "Publishable API Key", "invalid_public_key": "Publishable API key is not plausible", "secret_key": "Secret API Key", "invalid_secret_key": "Secret API key is not plausible", "billing_method": "Billing Method", "immediate_billing": "Immediate Billing", "periodic_billing": "Periodic Billing", "tax": "Tax", "platform_fee_tax": "Platform Fee Tax", "no_tax": "No Tax"}, "razorpay-wallet": {"description": "CERO SMART MOBILITY allows to send invoices to the charging station users with Razorpay Wallet ...", "title": "Razor Pay Wallet", "url": "URL of Razorpay Wallet Dashboard", "public_key": "Publishable API Key", "invalid_public_key": "Publishable API key is not plausible", "secret_key": "Secret API Key", "invalid_secret_key": "Secret API key is not plausible", "billing_method": "Billing Method", "immediate_billing": "Immediate Billing", "periodic_billing": "Periodic Billing", "tax": "Tax", "platform_fee_tax": "Platform Fee Tax", "no_tax": "No Tax", "api_url": "API Url", "program": "Program"}, "razorpay": {"description": "CERO SMART MOBILITY allows to send invoices to the charging station users with Razorpay ...", "title": "Razor Pay", "url": "URL of Razorpay Dashboard", "public_key": "Publishable API Key", "invalid_public_key": "Publishable API key is not plausible", "secret_key": "Secret API Key", "invalid_secret_key": "Secret API key is not plausible", "billing_method": "Billing Method", "immediate_billing": "Immediate Billing", "periodic_billing": "Periodic Billing", "tax": "Tax", "platform_fee_tax": "Platform Fee Tax", "no_tax": "No Tax"}}, "analytics": {"sac": {"title": "SAP Analytics", "settings": "Settings"}, "description": ["CERO SMART MOBILITY exposes its data to external analytics tools.", "This feature exposes real-time all the session data through a connector to external data analytics tools.", "CERO SMART MOBILITY is currently using SAP Analytics Cloud via an OData connector.", "SAP Analytics Cloud supports the intelligent use of charging data to simplify operational decision making.", "More technical information available at "], "setting_do_not_exist": "Configuration has not been found", "setting_not_found": "SAP Analytics settings have not been found", "create_success": "SAP Analytics Cloud settings have been created successfully", "create_error": "Error occurred while creating the SAP Analytics Cloud settings, check the logs", "update_success": "SAP Analytics Cloud settings have been updated successfully", "update_error": "Error occurred while updating the SAP Analytics Cloud settings, check the logs"}, "smart_charging": {"sap_smart_charging": {"title": "Smart Charging", "settings": "Settings", "user": "User", "password": "Password", "additional_settings": "Additional settings:", "sticky_limitation": "Adjust limit according to current consumption", "limit_buffer_dc": "Buffer for Limit DC (%)", "limit_buffer_ac": "Buffer for Limit AC (%)"}, "description": "CERO SMART MOBILITY can use smart charging algorithm to optimize power distribution and charging schedules based on complex constraints", "setting_do_not_exist": "Configuration has not been found", "setting_not_found": "SAP Smart Charging settings have not been found", "create_success": "SAP Smart Charging settings have been created successfully", "create_error": "Error occurred while creating the SAP Smart Charging settings, check the logs", "update_success": "SAP Smart Charging settings have been updated successfully", "update_error": "Error occurred while updating the SAP Smart Charging settings, check the logs", "connection_success": "Connection to the smart charging service was tested with success", "connection_error": "Impossible to connect to the smart charging service. Check your configuration.", "check_connection": "Check Connection"}, "asset": {"connection": {"title": "Asset connections", "name": "Name", "description": "Description", "type": "Type", "refreshIntervalMins": "Refresh <PERSON> (mins)", "base_url": "URL", "user": "User", "password": "Password", "client_id": "Client ID", "client_secret": "Client Secret", "authentication_url": "Authentication URL", "delete_title": "Delete Asset connection", "delete_confirm": "Do you really want to delete the connection '{{assetConnectionName}}'?"}, "types": {"schneider": "Schneider Building Management System", "greencom": "GreenCom Asset Management System", "iothink": "ioThink Asset Management System", "wit": "WIT Asset Management System", "lacroix": "LACROIX Asset Management System"}, "connection_success": "Connection to the system was tested successfully", "unknown_connection_error": "Unknown connection error", "invalid_grant": "Incorrect User or Password", "connection_failed": "Connection failed", "description": "CERO SMART MOBILITY can manage your asset consumption and use this information with the smart charging algorithm to balance the charging station's consumptions for a given site area.", "setting_do_not_exist": "Configuration has not been found", "setting_not_found": "Asset settings have not been found", "create_success": "Asset settings have been created successfully", "create_error": "Error occurred while creating the Asset settings, check the logs", "update_success": "Asset settings have been updated successfully", "update_error": "Error occurred while updating the Asset settings, check the logs"}, "car_connector": {"connection": {"title": "Car connectors", "name": "Name", "description": "Description", "type": "Type", "authentication_url": "Authentication Url", "api_url": "API Url", "client_id": "Client ID", "client_secret": "Client Secret", "delete_title": "Delete Connector", "delete_confirm": "Do you really want to delete the connector '{{carConnectorConnectionName}}'?"}, "types": {"mercedes": "Mercedes Connector", "tronity": "Tronity Connector", "targa_telematics": "Targa Telematics Connector"}, "mercedes": {"link_success": "Your account has been successfully linked with Mercedes", "link_error": "Error occurred while linking your account with Mercedes, check the logs", "revoke_success": "Your connection with Mercedes has been successfully revoked", "revoke_error": "Error occurred while revoking your connection with Mercedes, check the logs"}, "description": "CERO SMART MOBILITY can get real time data of your cars like the current state of charge and use this information with the smart charging algorithm to optimize charging sessions.", "setting_do_not_exist": "Configuration has not been found", "setting_not_found": "Car connector settings have not been found", "create_success": "Car connector settings have been created successfully", "create_error": "Error occurred while creating the Car connector settings, check the logs", "update_success": "Car connector settings have been updated successfully", "update_error": "Error occurred while updating the Car connector settings, check the logs"}, "taxes": {"title": "Taxes", "name": "Name", "rate": "Rate", "status": "Status", "api_url": "API Url", "taxes_not_found": "Taxes not found", "create_success": "The Tax '{{name}}' has been created successfully", "create_error": "Error occurred while creating the Tax, check the logs", "update_success": "The Tax '{{name}}' has been updated successfully", "update_error": "Error occurred while updating the Tax, check the logs", "delete_title": "Delete Tax", "delete_confirm": "Do you really want to delete the Tax '{{name}}'?", "delete_success": "The Tax '{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Tax, check the logs"}, "information": "Information", "activation_contact_msg": "To enable this feature, please contact your CERO SMART MOBILITY representative.", "car": {"synchronize_car_catalogs": "Synchronize Cars", "synchronize_car_catalogs_dialog_title": "Synchronize Cars", "synchronize_car_catalogs_dialog_confirm": "Do you really want to launch the synchronization of all the electric vehicles?", "assign_user_to_car_dialog_title": "Assign user to car", "assign_user_to_car_dialog_confirm": "This car already exists, do you really want to reuse it?"}}, "technical_settings": {"tabs": {"crypto": "Crypto", "users": "Users", "organization": "Organization"}, "crypto": {"crypto_key": {"title": "Crypto settings", "subtitle": "Crypto Key", "key": "Key", "block_cypher": "Block Cypher", "block_size": "Block Size", "operation_mode": "Operation Mode"}, "setting_do_not_exist": "Crypto Technical Settings not found", "update_success": "Crypto Technical Settings have been updated successfully", "update_error": "Error occurred while updating the Crypto Technical Settings, check the logs", "crypto_check_error": "Crypto check has failed, check your settings", "crypto_key_length_error": "Crypto key length is invalid, check your settings", "crypto_algorithm_error": "Crypto algorithm is not supported, check your settings", "crypto_migration_in_progress_error": "Crypto settings cannot be changed while migration is in progress"}, "user": {"title": "Account <PERSON><PERSON>", "auto_activation": "Automatically activate new user at registration time", "update_success": "User settings have been updated successfully", "update_error": "Error occurred while updating the user settings, check the logs", "setting_do_not_exist": "User settings not found"}}, "payment_cards": {"payment_cards_delete_title": "Delete Payment Cards", "payment_cards_delete_confirm": "Do you really want to delete the Payment Cards?", "payment_cards_delete_success": "Payment Cards has been deleted successfully", "payment_cards_delete_error": "Error occurred while deleting a Payment Cards, check the logs"}, "ocpiendpoints": {"platform_fee": "Platform Fee", "name": "Name", "base_url": "URL", "role": "Role", "country_code": "Country Code", "party_id": "Party ID", "status": "Status", "version": "Version", "local_token": "Local Token", "token": "Remote Token", "new": "New", "registered": "Registered", "unregistered": "Unregistered", "last_patch_job_on": "Last Update Date", "patch_job_last_status": "Last Push Status", "patch_job_status": "Job Status", "patch_job_result": "Success / Total", "patch_job_checkbox": "Activate EVSE Status Update Job", "status_active": "Active", "status_inactive": "Inactive", "generate_new_token": "Generate new token", "test_connection": "Connection Test", "create_success": "The IOP '{{name}}' has been created successfully", "create_error": "Error occurred while creating the IOP, check the logs", "update_success": "The IOP '{{name}}' has been updated successfully", "update_error": "Error occurred while updating the IOP, check the logs", "delete_title": "Delete IOP", "delete_confirm": "Do you really want to delete the IOP '{{name}}'?", "delete_success": "The IOP '{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting the IOP, check the logs", "register_title": "Register IOP", "register_confirm": "Do you want to trigger registration process with the IOP '{{name}}'?", "register_success": "The IOP '{{name}}' has been registered successfully", "register_error": "Error occurred while registring the IOP, check the logs", "unregister_title": "Unregister IOP", "unregister_confirm": "Do you want to trigger unregistration process with the IOP '{{name}}'?", "unregister_success": "The IOP '{{name}}' has been unregistered successfully", "unregister_error": "Error occurred while unregistring the IOP, check the logs", "update_credentials_title": "Update Credentials IOP", "update_credentials_confirm": "Do you want to update the credentials in the IOP '{{name}}'?", "update_credentials_success": "The credentials have been updated successfully in the IOP '{{name}}'", "update_credentials_error": "Error occurred while updating the credentials with the IOP, check the logs", "push_evse_statuses_title": "Send EVSE Statuses", "push_tokens_title": "Send User Tokens", "push_evse_statuses_confirm": "Do you want to send All EVSE statuses to IOP '{{name}}'?", "push_tokens_confirm": "Do you want to send all user tokens to IOP '{{name}}'?", "trigger_jobs_title": "Execute Jobs", "trigger_jobs_confirm": "Do you want to trigger all jobs of IOP '{{name}}'?", "trigger_ocpi_action": "The action has been successfully sent and may take time to be processed", "ocpi_action_in_progress": "You cannot start twice the same action, there is already one running in the background", "pull_locations_title": "Pull Locations", "pull_locations_confirm": "Do you want to retrieve all locations from IOP '{{name}}'?", "pull_locations_success": "The command has been accepted and the location(s) will be pulled asynchronously", "pull_locations_error": "Error occurred while updating the IOP locations, check the logs", "get_sessions_title": "<PERSON><PERSON>", "get_sessions_confirm": "Do you want to retrieve all sessions from IOP '{{name}}'?", "get_sessions_success": "The command has been accepted and the sessions will be pulled asynchronously", "get_sessions_error": "Error occurred while updating the IOP sessions, check the logs", "pull_tokens_title": "<PERSON><PERSON>", "pull_tokens_confirm": "Do you want to retrieve all tokens from IOP '{{name}}'?", "pull_tokens_success": "The command has been accepted and the tokens will be pulled asynchronously", "pull_tokens_error": "Error occurred while updating the IOP tokens, check the logs", "pull_cdrs_title": "Pull Charge Detail Records", "pull_cdrs_confirm": "Do you want to retrieve all charge detail records from IOP '{{name}}'?", "pull_cdrs_success": "The command has been accepted and the charge detail record(s) will be pulled asynchronously", "pull_cdrs_error": "Error occurred while updating the IOP charge detail records, check the logs", "pull_tariffs_title": "Pull Tariffs Detail Records", "pull_tariffs_confirm": "Do you want to retrieve all Tariffs detail records from IOP '{{name}}'?", "pull_tariffs_success": "The command has been accepted and the Tariffs detail record(s) will be pulled asynchronously", "pull_tariffs_error": "Error occurred while updating the IOP Tariffs detail records, check the logs", "check_cdrs_title": "Check Charge Detail Records", "check_cdrs_confirm": "Do you want to check all charge detail records with IOP '{{name}}'?", "check_cdrs_success": "The command has been accepted and the charge detail records will be checked asynchronously", "check_cdrs_error": "Error occurred while checking the IOP charge detail records, check the logs", "check_sessions_title": "Check Sessions", "check_sessions_confirm": "Do you want to check all sessions with I<PERSON> '{{name}}'?", "check_sessions_success": "The command has been accepted and the sessions will be checked asynchronously", "check_sessions_error": "Error occurred while checking the IOP sessions, check the logs", "check_locations_title": "Check Locations", "check_locations_confirm": "Do you want to check all locations with IOP '{{name}}'?", "check_locations_success": "The command has been accepted and the locations will be checked asynchronously", "check_locations_error": "Error occurred while checking the IOP locations, check the logs", "trigger_jobs_error": "Error occurred while executing jobs, check the logs", "success_ping": "Ping successful", "error_ping_401": "<PERSON> failed: Unauthorized", "error_ping_404": "<PERSON> failed: Not Found", "error_ping_412": "Ping failed: Invalid Response", "error_ping": "Ping failure", "error_generate_local_token": "Error occurred while generating new local token, check the logs", "push_evse_statuses_success": "The command has been accepted and the EVSE's statuses will be pushed asynchronously", "push_tokens_success": "The command has been accepted and the token(s) will be pushed asynchronously", "push_evse_statuses_error": "Error occurred while updating EVSE statuses, check the logs", "push_tokens_error": "Error occurred while updating user tokens, check the logs", "start_stop_job": "Enable/Disable background job", "background_job_activated": "Background job activated successfully", "background_job_deactivated": "Background job disabled successfully", "background_job_no_run": "Not Executed", "start_background_job_title": "Start Background Job", "start_background_job_confirm": "Do you want to start the background job for I<PERSON> '{{name}}'?", "stop_background_job_title": "Stop Background Job", "stop_background_job_confirm": "Do you want to stop the background job for <PERSON><PERSON> '{{name}}'?", "total_charge_points": "Charging Stations", "total_tokens": "RFID Cards", "total_locations": "Sites", "total": "Total", "succeeded": "Succeeded", "failed": "Failed"}, "ocpi": {"title": "Roaming OCPI", "type_placeholder": "Type", "roaming_platforms": "Inter Operability Platform", "cpo": "Charging Point Operator", "enable_cpo": "Enable Charging Point Operator", "emsp": "Mobility Service Provider", "enable_emsp": "Enable EMSP", "tariff_id": "Tariff ID", "connector_id": "Connector ID", "remarks": "Remarks", "owner_name": "Owner Name", "description": "Open Charge Point Interface", "details": "Identification", "country_code": "Country code", "party_id": "Party ID", "business_details": "Business Details", "logo": "Image", "sync_all": "Synchronize All", "push_evse_statuses": "<PERSON><PERSON>es", "push_tokens": "<PERSON><PERSON>", "pull_cdrs": "Pull CDRs", "pull_tariffs": "Pull Tariffs", "pull_locations": "Pull Locations", "pull_sessions": "<PERSON><PERSON>", "pull_tokens": "<PERSON><PERSON>", "check_locations": "Check Locations", "check_sessions": "Check Sessions", "check_cdrs": "Check Cdrs", "update_credentials": "Update Credentials", "businessdetails": {"name": "Name of the operator", "website": "Link to the operator website", "logo": {"url": "URL of the full scale image", "thumbnail": "URL of the thumbnail image", "category": "Category of the image", "type": "Image type (gif, jpeg, png, svg,...)", "width": "Width of the full scale image", "height": "Height of the full scale image"}}}, "oicpendpoints": {"name": "Name", "base_url": "URL", "role": "Role", "country_code": "Country Code", "party_id": "Party ID", "status": "Status", "version": "Version", "new": "New", "registered": "Registered", "unregistered": "Unregistered", "last_patch_job_on": "Last Update Date", "patch_job_last_status": "Last Push Status", "patch_job_status": "Job Status", "patch_job_result": "Success / Total", "patch_job_checkbox": "Activate EVSE Status Update Job", "status_active": "Active", "status_inactive": "Inactive", "test_connection": "Connection Test", "create_success": "'{{name}}' has been created successfully", "create_error": "Error occurred while creating the IOP, check the logs", "update_success": "'{{name}}' has been updated successfully", "update_error": "Error occurred while updating the IOP, check the logs", "delete_confirm": "Do you really want to delete the IOP '{{name}}'?", "delete_title": "Delete", "delete_success": "'{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting, check the logs", "register_confirm": "Do you want to trigger registration process with '{{name}}'?", "unregister_confirm": "Do you want to trigger unregistration process with '{{name}}'?", "register_title": "Register", "unregister_title": "Unregister", "register_success": "'{{name}}' has been registered successfully", "unregister_success": "'{{name}}' has been unregistered successfully", "register_error": "Error occurred while registring, check the logs", "unregister_error": "Error occurred while unregistring, check the logs", "push_evse_statuses_title": "Send EVSE Statuses", "push_evses_title": "Send EVSEs", "push_evse_statuses_confirm": "Do you want to send All EVSE statuses to Hubject '{{name}}'?", "push_evses_confirm": "Do you want to send All EVSEs to Hubject '{{name}}'?", "trigger_jobs_title": "Execute Jobs", "trigger_jobs_confirm": "Do you want to trigger all jobs of Hu<PERSON> '{{name}}'?", "trigger_jobs_error": "Error occurred while executing jobs, check the logs", "success_ping": "Ping successful", "error_ping_401": "<PERSON> failed: Unauthorized", "error_ping_404": "<PERSON> failed: Not Found", "error_ping_412": "Ping failed: Invalid Response", "error_ping": "Ping failure", "push_evse_statuses_success": "Successfully updated {{success}} EVSE statuses", "push_evse_statuses_partial": "{{success}} EVSE(s) updated - {{error}} EVSE(s) not updated, check the logs", "push_evses_success": "Successfully updated {{success}} EVSEs", "push_evses_partial": "{{success}} EVSE(s) updated - {{error}} EVSE(s) not updated, check the logs", "push_evse_statuses_error": "Error occurred while updating EVSE statuses, check the logs", "push_evses_error": "Error occurred while updating EVSEs, check the logs", "start_stop_job": "Enable/Disable background job", "background_job_activated": "Background job activated successfully", "background_job_deactivated": "Background job disabled successfully", "background_job_no_run": "Not Executed", "start_background_job_title": "Start Background Job", "start_background_job_confirm": "Do you want to start the background job for '{{name}}'?", "stop_background_job_title": "Stop Background Job", "stop_background_job_confirm": "Do you want to stop the background job for '{{name}}'?", "total_charge_points": "Total Charge Points", "total": "Total", "succeeded": "Succeeded", "failed": "Failed"}, "oicp": {"title": "Roaming Hubject (OICP)", "type_placeholder": "Type", "roaming_platforms": "Roaming Platforms", "cpo": "Charging Point Operator Identification", "emsp": "CERO SMART MOBILITY Service Provider Identification", "description": "Open InterCharge Protocol", "details": "Identification", "country_code": "Country code", "party_id": "Party ID", "business_details": "Business Details", "logo": "Image", "key": "Private Key", "cert": "Certificate", "sync_all": "Synchronize All", "push_evses": "Push EVSE Data", "push_evses_statuses": "Push EVSE Status Data", "businessdetails": {"name": "Name of the operator", "website": "Link to the operator website", "logo": {"url": "URL of the full scale image", "thumbnail": "URL of the thumbnail image", "category": "Category of the image", "type": "Image type (gif, jpeg, png, svg,...)", "width": "Width of the full scale image", "height": "Height of the full scale image"}}}, "organization": {"title": "Organization", "description": "Manage Organization: Companies, Sites and Site Areas", "tabs": {"companies": "Companies", "sites": "Sites", "siteareas": "Site Areas"}, "graph": {"power": "Instant Power (kW)", "no_consumption": "No consumption data available", "asset_consumption_watts": "Asset Consumption (kW)", "asset_production_watts": "Asset Production (kW)", "charging_station_consumption_watts": "Charging Stations Consumption (kW)", "net_consumption_watts": "Net Consumption (kW)", "limit_watts": "Limit (kW)", "asset_consumption_amps": "Asset Consumption (A)", "asset_production_amps": "Asset Production (A)", "charging_station_consumption_amps": "Charging Stations Consumption (A)", "net_consumption_amps": "Net Consumption (A)", "limit_amps": "Limit (A)"}}, "asset": {"title": "<PERSON><PERSON>", "description": "Manage assets (buildings, solar panels, batteries)", "tabs": {"assets": "Assets", "in_error": "In Error"}, "graph": {"power": "Instant Power (kW)", "limit_watts": "<PERSON>rid Limit (kW)", "no_consumption": "No consumption data available"}}, "cars": {"vin": "VIN", "license_plate": "License Plate", "user": "User", "assign_user": "Assign User", "vehicle_model": "Model", "vehicle_make": "Maker", "vehicle_model_version": "Version", "battery_capacity_full": "Battery Capacity", "fast_charge_speed": "Charge Speed", "performance_top_speed": "Top Speed", "range_real": "Real Range", "range_wltp": "WLTP Range", "efficiency_real": "Efficiency", "performance_acceleration": "0-100 km/h", "image": "Image", "drivetrain_propulsion": "Propulsion", "drivetrain_torque": "Couple total", "battery_capacity_useable": "Capacity Useable", "charge_plug": "AC Plug", "drivetrain_power_hp": "Power", "fast_charge_plug": "DC Plug", "charge_plug_location": "Charge Plug Location", "charge_standard_power": "AC Power", "charge_alternative_power": "Alternative AC Power", "charge_option_power": "Option AC Power", "charge_standard_charge_speed": "AC Speed", "charge_standard_charge_time": "AC Time", "fast_charge_power_max": "DC Power", "misc_seats": "Seats", "misc_body": "Car Body", "misc_isofix": "Isofix", "misc_turning_circle": "Turning Circle", "misc_segment": "Segment", "misc_isofix_seats": "Isofix Seats", "miscellaneous": "Miscellaneous", "battery": "Battery", "debug_car": "Advanced", "charge_standard_tables": "AC Converters", "evse_phase_volt": "Voltage", "evse_phase_amp": "Amperage", "evse_phase": "Phase(s)", "evse_phase_ac_standard": "AC Phase(s)", "evse_phase_ac_alternative": "Alternative AC Phase(s)", "evse_phase_ac_option": "Option AC Phase(s)", "charge_phase_volt": "Charge Phase Volt", "charge_phase_amp": "Charge Phase Amp", "charge_phase": "Charge Phase", "charge_power": "Charge Power", "charge_time": "Charge Time", "charge_speed": "Charge Speed", "synchronize_car_catalogs_success": "The car synchronization request has been accepted and will be performed asynchronously", "synchronize_car_catalogs_error": "Cars failed to be synchronized, check the logs", "synchronize_car_catalogs_ongoing": "The synchronization of the cars is already ongoing", "synchronize_car_catalogs_up_to_date": "Cars already up to date", "select_car_maker": "Select Car Maker", "car_makers": "Car Makers", "create_success": "The car '{{carName}}' has been created with success", "update_success": "The car '{{carName}}' has been updated successfully", "delete_success": "The car '{{carName}}' has been deleted successfully", "delete_error": "Error occurred while deleting the car, check the logs", "assign_car_catalog": "Assign car model", "create_error": "Error occurred while creating the car, check the logs", "update_error": "Error occurred while updating the car, check the logs", "car_exist": "Car already exists", "user_not_owner": "Only the owner can update this (default can be changed)", "car_exist_different_car_catalog": "Car already exists with a different car model", "user_already_assigned": "You are already assigned this car to a user", "company_car": "Company Car", "invalid_vin": "Invalid VIN", "assign_users": "Assign Users", "car_owner": "Car Owner", "type": "Type", "default_car": "Default car", "private_car": "Private car", "pool_car": "Pool car", "remove_users_title": "Remove User(s)", "remove_users_confirm": "Are you sure to remove the selected user(s) from this car?", "assign_users_car_partial": "'{{assigned}}' user(s) have been assigned successfully and {{inError}} user(s) have encountered an error, check the logs", "assign_users_car_success": "'{{assigned}}' user(s) have been assigned successfully", "assign_users_car_error": "Error occurred while assigning the users, check the logs", "update_users_car_partial": "'{{assigned}}' user(s) have been updated successfully and {{inError}} user(s) have encountered an error, check the logs", "update_users_car_success": "'{{assigned}}' user(s) have been updated successfully", "update_users_car_error": "Error occurred while updating the users, check the logs", "remove_users_car_partial": "'{{assigned}}' user(s) have been removed successfully and {{inError}} user(s) have encountered an error, check the logs", "remove_users_car_success": "'{{assigned}}' user(s) have been removed successfully", "remove_users_car_error": "Error occurred while removing the users, check the logs", "car_not_found": "Car has not been found", "car_error": "Error occurred while retrieving the car, check the logs", "car_user_error": "Error occurred while retrieving the car's users, check the logs", "assign_converter": "Assign Converter", "converter": "Converter", "delete_title": "Delete Car", "delete_confirm": "Do you really want to delete the car '{{carName}}'?", "users": "Users", "select_car": "Select Car", "car_connector_name": "Connection Name", "car_connector_meter_id": "Connection Meter ID", "unit": {"meters": "m", "seconde": "sec(s)", "secondes": "secs", "minutes": "mins", "newton_metre": "Nm", "horse_power": "hp", "kilowatt_heure": "kWh", "kilometer_per_hour": "km/h", "kilometer": "km", "kilowatt": "kW", "drivetrain_power_hp_unit": "hp"}}, "car": {"title": "Car", "description": "Manage the user's cars", "tabs": {"car_catalogs": "Car Catalogs", "cars": "Car Management"}}, "car_connector": {"title": "Car Connector", "description": "Use APIs to gain real time data of your cars"}, "analytics": {"title": "Analytics", "type_placeholder": "Type", "description": "Extended Analytics Interface", "mainurl": "SAP Analytics Cloud Home Page", "timezone": "Timezone used in SAP Analytics Cloud", "links": "SAP Analytics Cloud Links", "set_link": "Set Link", "delete_title": "Delete link", "delete_confirm": "Do you really want to delete the link '{{linkName}}'?", "link": {"name": "Name", "description": "Description", "role": "User Roles", "url": "URL"}}, "smart_charging": {"title": "Smart Charging", "type_placeholder": "Type", "description": "Smart-Charging of electric vehicles", "optimizerUrl": "Optimizer URL"}, "sms": {"title": "SMS", "type_placeholder": "Type", "description": "SMS of electric vehicles", "username": "Api Id", "password": "Api Password", "Sender_password": "Sender Id", "smsType1": "Text Local", "smsType2": "sms Ala", "smsType3": "SmsGatewayHub", "setting_do_not_exist": "Configuration has not been found", "create_error": "Error occurred while creating the SMS settings, check the logs", "update_success": "SMS settings have been updated successfully", "update_error": "Error occurred while updating the SMS settings, check the logs", "create_success": "SMS settings have been created successfully", "description_blank": "CERO SMART MOBILITY can need of your sms like the current state of charge and see this information with the sms to your enhancement fuctionality."}, "smtp": {"title": "SMTP", "type_placeholder": "Type", "description": "SMTP for mail server", "from": "From", "host": "Host", "port": "Port", "secure": "Secure", "requireTLS": "Require TLS", "user": "User", "password": "Password", "smtpType1": "SMTP", "setting_do_not_exist": "Configuration has not been found", "create_error": "Error occurred while creating the SMTP settings, check the logs", "update_success": "SMTP settings have been updated successfully", "update_error": "Error occurred while updating the SMTP settings, check the logs", "create_success": "SMTP settings have been created successfully", "description_blank": "CERO SMART MOBILITY can need of your SMTP like the current state of charge and see this information with the SMTP to your enhancement fuctionality."}, "page_setup": {"title": "Title", "slug": "Slug", "description": "Description", "type_placeholder": "Enter description here...", "pages_not_found": "Pages not found", "create_success": "The Page '{{title}}' has been created successfully", "create_error": "Error occurred while creating the Page, check the logs", "update_success": "The Page '{{title}}' has been updated successfully", "update_error": "Error occurred while updating the <PERSON>, check the logs", "delete_title": "Delete Page", "delete_confirm": "Do you really want to delete the Page '{{title}}'?", "delete_success": "The Page '{{title}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Page, check the logs"}, "refund": {"title": "Refunding", "type_placeholder": "Type", "description": "Refunding Interface"}, "pricing": {"title": "Pricing", "type_placeholder": "Type", "description": "Pricing Interface", "price_kw_h_label": "Price kW.h", "price_unit_label": "<PERSON><PERSON><PERSON><PERSON>", "update_success": "The price has been updated successfully", "update_error": "Error occurred while updating the price, check the logs"}, "billing": {"title": "Billing", "type_placeholder": "Type", "description": "Billing Interface", "id": "Billing ID", "updated_on": "Synchronized on", "tabs": {"invoices": "Invoices", "in_error": "In Error"}}, "billingPlatform": {"title": "Billing Platform", "description": "Manage several billing accounts"}, "transactions": {"id": "Id", "date_from": "Date", "duration": "Duration", "inactivity": "Inactivity", "user": "User", "badge_id": "RFID", "connector": "Connector", "total_consumption": "Consumption", "current_consumption": "Power", "state": "Status", "price": "Price", "taxedPrice": "Taxed Price", "state_of_charge": "Battery", "date": "Date", "charging_station": "Charging Station", "started_at": "Started At", "end_date": "End Date", "stop_reason": "Reason", "consumption": "Consumption", "transactions_button": "Sessions", "transaction_today": "Today", "transaction_one_week_ago": "1 Week ago", "transaction_one_month_ago": "1 Month ago", "transaction_three_months_ago": "3 Months ago", "transaction_six_months_ago": "6 Months ago", "transaction_one_year_ago": "1 Year ago", "tab_current": "Current", "tab_history": "History", "stopped_by": "Stopped By", "soft_stop": "Stop", "soft_stop_transaction_title": "Stop Session (Soft)", "soft_stop_transaction_confirm": "Do you really want to stop the session (soft) that belongs to '{{userFullName}}' on '{{chargeBoxID}}'?", "soft_stop_transaction_success": "The session has been stopped successfully. You can unlock the connector", "soft_stop_transaction_error": "Error occurred while stopping the session, check the logs", "rebuild_transaction_consumptions_title": "Rebuild Consumptions", "rebuild_transaction_consumptions_confirm": "Do you really want to rebuild all the session's consumptions?", "rebuild_transaction_consumptions_success": "The session's consumptions have been rebuilt successfully", "rebuild_transaction_consumptions_error": "Error occurred while rebuilding the session's consumptions, check the logs", "delete": "Delete", "error_code": "Error", "delete_transaction_title": "Delete Session", "delete_transaction_confirm": "Do you really want to delete the session that belongs to '{{userFullName}}' on '{{chargeBoxID}}'?", "delete_transaction_success": "The session has been deleted successfully", "delete_transaction_error": "Error occurred while deleting the session, check the logs", "delete_transactions_title": "Delete Session(s)", "delete_transactions_confirm": "Do you really want to delete '{{quantity}}' session(s)?", "delete_transactions_success": "{{inSuccess}} sessions have been deleted successfully", "delete_transactions_partial": "{{inSuccess}} sessions have been deleted successfully and {{inError}} sessions have encountered an error, check the logs", "delete_transactions_error": "{{inError}} failed to be deleted, check the logs", "delete_transactions_unexpected_error": "Unexpected error occurred while deleting the sessions, check the logs", "delete_no_transaction": "No session has been deleted", "export_ocpi_cdr_button_title": "Export Cdr", "export_ocpi_cdr_title": "Export Transaction's Cdr", "export_ocpi_cdr_confirm": "Do you really want to export the Cdr of the Session '{{transactionID}}'", "export_ocpi_cdr_error": "Error occurred while exporting the Cdr of the Session '{{transactionID}}'", "inactivity_info": "All Inactivities", "inactivity_warning": "Medium Inactivities", "inactivity_error": "High Inactivities", "refund": "Refund", "refund_transaction_title": "Refund Session", "refund_transaction_confirm": "Do you really want to refund the session of '{{userFullName}}' on '{{chargeBoxID}}'?", "refund_transaction_success": "The session has been refunded successfully", "refund_transaction_error": "Error occurred while refunding the session, check the logs", "refund_undefined": "Not Submitted", "refund_approved": "Approved", "refund_cancelled": "Cancelled", "refund_submitted": "Submitted", "transaction_number": "Session(s)", "total_consumption_kw": "Total Consumption", "transaction_not_found": "Session has not been found", "transaction_id_not_found": "Session ID '{{sessionID}}' has not been found", "load_transactions_error": "Error occurred while retrieving the sessions, check the logs", "load_transaction_error": "Error occurred while retrieving the session, check the logs", "total_duration": "Total Duration", "refund_transactions": "Reimbursed ", "pending_transactions": "Pending ", "count_refunded_reports": "Reports", "hours": "hour(s)", "mins": "min(s)", "hour_short": "h", "no_active_transaction": "No active session", "no_history": "No history during this period", "refundDate": "Transferred On", "reportId": "Report ID", "select_report": "Select Report", "create_invoice_success": "Invoice has been created successfully for this session", "create_invoice_error": "Error occurred while creating an invoice for this session", "redirect": "Navigate to Sessions", "error_start_no_payment_method": "Service unavailable, please go to your profile to provide a payment method.", "error_start_general": "Service temporarily unavailable, please contact the support.", "filter": {"type": {"name": "Type", "refunded": "Refunded", "not_refunded": "Not Refunded"}}, "tabs": {"history": "History", "in_progress": "In Progress", "in_error": "In Error", "refund": "Refunding", "invoices": "Invoices"}, "graph": {"battery": "Battery (%)", "power": "Grid Power (kW)", "power_l1": "Grid Power L1 (kW)", "power_l2": "Grid Power L2 (kW)", "power_l3": "Grid Power L3 (kW)", "amps": "<PERSON><PERSON> (A)", "amps_l1": "Grid Amps L1 (A)", "amps_l2": "Grid Amps L2 (A)", "amps_l3": "Grid Amps L3 (A)", "energy": "Energy Delivered (kW.h)", "energy_amps": "Energy Delivered (A.h)", "reset_zoom": "Reset", "no_consumption": "No consumption data available", "cumulated_amount": "Amount", "amperage": "Amperage (A)", "amperage_dc": "Amperage DC (A)", "amperage_l1": "Amperage L1 (A)", "amperage_l2": "Amperage L2 (A)", "amperage_l3": "Amperage L3 (A)", "voltage": "Voltage (V)", "voltage_dc": "Voltage DC (V)", "voltage_l1": "Voltage L1 (V)", "voltage_l2": "Voltage L2 (V)", "voltage_l3": "Voltage L3 (V)", "plan_watts": "Connector Limit (kW)", "plan_amps": "Connector Limit (A)", "limit_plan_watts": "Plan Limit (kW)", "limit_plan_amps": "Plan Limit (A)", "limit": "Limit", "limit_watts": "<PERSON>rid Limit (kW)", "limit_amps": "<PERSON><PERSON> (A)", "load_all_consumptions": "Show full chart resolution", "unit_kilowatts": "kW", "unit_amperage": "Amp"}, "dialog": {"delete": {"title": "Delete Session", "confirm": "Do you really want to delete the session of '{{user}}'?", "rejected_refunded_msg": "You cannot delete a session that has been already refunded!", "rejected_billed_msg": "You cannot delete a session that has been already billed!"}, "export": {"title": "Export Sessions", "confirm": "Do you really want to export all the sessions matching the filters?", "error": "An error occurred during the export of the sessions, check the logs"}, "soft_stop": {"title": "Stop Session", "confirm": "Do you really want to stop the session of '{{user}}?"}, "refund": {"title": "Refund", "confirm": "Do you really want to transfer '{{quantity}}' session(s) to your refunding system?"}, "roaming": {"title": "Roaming", "confirm": "Do you really want to push the CDR of the session ID '{{sessionID}}' to the roaming platform?"}, "create_invoice": {"title": "Create Invoice", "confirm": "Do you really want to create an invoice to this session?"}, "session": {"tooltips": {"amount": "Amount", "start_date": "Start date", "total_consumption": "Total consumption", "current_consumption": "Current consumption", "state_of_charge": "Battery level", "total_duration": "Total duration", "inactivity": "Inactivity duration"}, "pricing_detail_description": "Description", "pricing_detail_entity_type": "Entity Type", "pricing_detail_entity_id": "Entity ID", "pricing_detail_entity_name": "Entity Name", "pricing_detail_unit_price": "Unit Price", "pricing_detail_consumption": "Consumption", "pricing_detail_duration": "Duration", "pricing_detail_amount": "Amount", "pricing_detail_view_all": "View all"}}, "notification": {"delete": {"success": "The session of '{{user}}' has been deleted successfully", "error": "An error occurred during the deletion of the session, check the logs"}, "roaming": {"success": "The CDR of the session ID '{{sessionID}}' has been pushed successfully to the roaming platform", "error": "An error occurred during the push of the CDR, check the logs", "error_not_from_tenant": "This session does not belong to this tenant", "error_no_ocpi": "This session does not contain OCPI data", "error_cdr_already_pushed": "The CDR of this session has already been pushed"}, "soft_stop": {"success": "The session of '{{user}}' has been stopped successfully.", "error": "An error occurred during session stop, check the logs"}, "refund": {"success": "{{inSuccess}} sessions have been transferred successfully", "partial": "{{inSuccess}} sessions have been transferred successfully and {{inError}} session(s) have encountered an error, check the logs", "forbidden_refund_another_user": "You cannot transfer another user's session", "not_authorized": "You are not authorized to perform this request", "error": "The CERO SMART MOBILITY application couldn't connect to the refunding system. Contact an administrator.", "concur_connection_invalid": "Your refunding system connection is not configured correctly. Contact an administrator.", "tenant_concur_connection_invalid": "The Organization of your refunding system configuration is not complete. Contact an administrator"}}, "errors": {"no_consumption": {"title": "No Consumption", "description": "The session does not contains meter values.", "action": "Check the charging station settings. Ensure that meter values are sent by the charging station."}, "low_consumption": {"title": "Low Consumption", "description": "The session has a consumption < 1 kWh.", "action": "Check the charging station settings. Ensure that meter values are sent by the charging station."}, "average_consumption_greater_than_connector_capacity": {"title": "Consumption Too High", "description": "The average registered charging power is greater than the capacity of the connector.", "action": "Check the charging station settings. Ensure that the connector maximum power is set and valid."}, "negative_inactivity": {"title": "Negative Inactivity", "description": "The inactivity of the session is negative.", "action": "Check the charging station settings. Ensure that meter values are sent in the correct order."}, "long_inactivity": {"title": "High Inactivity", "description": "The inactivity of the session > 24 hours.", "action": "Check the Session"}, "negative_duration": {"title": "Negative Duration", "description": "The duration of the session is negative.", "action": "Check the charging station settings. Ensure that meter values are sent in the correct order."}, "low_duration": {"title": "Low Duration", "description": "The duration of the session < 1 minute.", "action": "Check the charging station settings. Ensure that meter values are sent correctly."}, "missing_price": {"title": "Missing Price", "description": "The session has not been priced.", "action": "Even though the pricing is activated in the organization, the session has not been priced. Contact your site administrator."}, "incorrect_starting_date": {"title": "Incorrect Starting Date", "description": "The starting date value is incorrect or impossible.", "action": "Check the charging station settings. Ensure that the date sent at the start of the session is correct."}, "missing_user": {"title": "Session with no User", "description": "The User is unknown for this Session.", "action": "Delete the Session or disable the access control in the corresponding Site Area."}, "no_billing_data": {"title": "Session with no Billing data", "description": "There is no billing data for this Session.", "action": "Delete the session or create an invoice from it."}}}, "templates": {"title": "Charging Station Template", "tabs": {"charging_station_templates": "Charging Station Templates"}, "invalid_json": "Invalid JSON format", "template_not_found": "Template has not been found", "create_success": "The template '{{template}}' has been created successfully", "create_error": "Error occurred while creating the template, check the logs", "update_success": "The template '{{template}}' has been updated successfully", "update_error": "Error occurred while updating the template, check the logs", "delete_confirm": "Do you really want to delete the template '{{template}}'?", "delete_success": "The template '{{template}}' has been deleted successfully", "delete_error": "Error occurred while deleting the template, check the logs", "delete_title": "Delete Template", "dialog": {"export": {"title": "Export charging station templates", "confirm": "Do you really want to export all the charging station templates matching the filters?", "error": "Error occurred while trying to export the charging station templates, check the logs"}}, "chargePointVendor": "<PERSON><PERSON><PERSON>", "chargePointModel": "Model Filter", "chargeBoxSerialNumber": "Serial Number Filter"}, "geomap": {"dialog_geolocation_title": "Set Geolocation of '{{componentName}}' '{{itemComponentName}}'", "select_geolocation": "Select Geo Location", "search": "Search Place", "max_zoom": "Max Zoom", "min_zoom": "<PERSON>"}, "errors": {"title": "Error", "details": "Details", "description": "Description", "action": "Action"}, "notifications": {"tabs": {"notifications": "Notifications"}, "form": {"title": "Notification Title", "body": "Notification Body"}, "table": {"title": "Title", "body": "Body", "user": "User", "userID": "ID", "timestamp": "Sent Date", "channel": "Channel", "sourceId": "Source", "sourceDescr": "Source Description", "chargeBoxID": "charge Box ID"}, "sent_success": "Notification has been sent successfully", "error_in_sent": "Error in notification send", "admin_only": "Administrators only", "session_started": "Notify me when my charging session has started", "optimal_charge_reached": "Notify me when the optimal battery level has been reached (85%)", "end_of_charge": "Notify me when my electric vehicle is not charging anymore (the electric vehicle is still connected)", "end_of_session": "Notify me when my charging session is finished (the electric vehicle is no longer connected)", "user_account_status_changed": "Notify me when my user's status has been changed by an Administrator (Active, Blocked…)", "new_registered_user": "Notify me when a new user has just registered", "unknown_user_badged": "Notify me when an unknown user has badged on a charging station", "charging_station_status_error": "Notify me when an error occurred on a charging station’s connector, check the logs", "charging_station_registered": "Notify me when a charging station has been registered to the backend or has been restarted", "charging_stations_offline": "Notify me when a charging station is offline", "preparing_session_not_started": "Notify me when a session has not been started (site owner only)", "ocpi_patch_status_error": "Notify me if the transfer of a charging station’s statuses has failed to the roaming platform (OCPI)", "oicp_patch_status_error": "Notify me if the transfer of a charging station’s statuses has failed to the roaming platform (OICP)", "billing_synchronization_failed": "Notify me when a synchronization with billing system has failed", "billing_periodic_operation_failed": "Notify me when a periodic billing operation has failed", "session_not_started": "Notify me if no session has been started after badging", "user_account_inactivity": "Notify me when I haven't logged in to the application for more than 6 months", "car_catalog_synchronization_failed": "Notify me if an error has occurred while synchronization cars, check the logs", "compute_and_apply_charging_profiles_failed": "Notify me when applying the smart charging profiles fails for a site area", "end_user_error_notification": "Notify me when a user reports an error", "billing_new_invoice": "Notify me when a new invoice is available", "admin_account_verification": "Notify me when new account creation needs to be verified"}, "payment_settlement": {"table": {"transactionNo": "Transaction No", "amount": "Amount", "invoiceId": "Invoice ID", "corporateName": "Corporate", "paymentMode": "Payment Mode", "remark": "Remark", "settlementDate": "Settlement Date", "transactionDate": "Transaction Date", "id": "ID"}, "form": {"corporateID": "Corporate", "transactionNo": "Transaction No", "amount": "Amount", "invoiceId": "Invoice ID", "paymentMode": "Payment Mode", "remark": "Remark", "settlementDate": "Settlement Date", "transactionDate": "Transaction Date", "id": "ID"}, "payment_methods": {"online": "Online", "cash": "Cash", "credit_card": "Credit Card", "debit_card": "Debit Card"}, "tabs": {"payment": "Payment Settlement"}, "export": {"title": "Export Settled Payments", "confirm": "Do you really want to export the list to a csv format?", "error": "Error occurred while trying to export"}, "record_not_found": "Payment recored not found", "create_success": "The Payment '{{id}}' has been created successfully", "create_error": "Error occurred while creating the Payment, check the logs", "update_success": "The Payment '{{id}}' has been updated successfully", "update_error": "Error occurred while updating the Payment, check the logs", "delete_title": "Delete Payment", "delete_confirm": "Do you really want to delete the Payment '{{id}}'?", "delete_success": "The Payment '{{id}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Payment, check the logs", "get_corporate_error": "Error at corporate fetching time"}, "evse": {"header_title": "Reservation", "form": {"idTag": "Tag ID", "connectorId": "Connector ID"}, "guns": {"a": "Gun A", "b": "Gun B", "c": "Gun C"}, "table": {"parentIdTag": "Tag ID", "connectorId": "Connector ID", "reservationId": "Reservation ID"}}, "wallet_report": {"search": {"accountName": "Account Name"}, "searchDilog": {"title": "Select Corporate Admin", "CorporateAdmin": "Corporate Admin"}, "table": {"Date": "Date", "SessionId": "SessionId", "TransactionId": "TransactionId", "currency": "currency", "Amount": "Amount", "paymentType": "Debit/Debit", "ClosingBalance": "ClosingBalance"}}, "razorpay_wallet": {"tabs": {"register_wallet": "Register Wallet", "verify_wallet": "<PERSON><PERSON><PERSON>", "preauth_wallet": "Verify PreAuth Token", "add_balance": "Add Balance", "initiate_kyc": "Initiate KYC", "register_beneficiaries": "Register Beneficiaries", "verify_beneficiaries": "Verify Beneficiaries", "withdraw-money": "Withdraw Money", "verify-withdraw": "Veri<PERSON>draw"}}, "virtual_wallet": {"title": "Virtual Wallet", "add_balance": "Add Money", "create_success": "Amount added successfully", "create_error": "Error in amount add", "transactions_not_found": "Transactions not found", "form_field": {"amount": "Amount", "select_user": "Select User", "transactionRef": "Transaction Ref ID", "transactionDate": "Transaction Date", "remarks": "Remarks"}, "errors": {}, "table": {"id": "Transaction ID", "user": "User", "addedBy": "Added By", "amount": "Amount", "select_user": "Select User", "paymentId": "Transaction Ref ID", "transactionDate": "Transaction Date", "remarks": "Remarks"}}}