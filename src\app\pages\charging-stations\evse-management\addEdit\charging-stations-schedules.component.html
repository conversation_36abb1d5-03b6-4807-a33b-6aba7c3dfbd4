<div class="main-content vw-90 vh-90 charging-stations-schedules-dialog-size">
  <div class="card card-profile card-testimonial">
    <mat-tab-group
      animationDuration="0ms"
      disableRipple="true"
      class="mat-tab-info"
      [class]="dialogRef ? 'mat-tabs-with-actions' : 'mat-tabs-with-close-action'"
    >
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>settings_input_component</mat-icon>
          <span class="myclass"
            >{{ "chargers.tabs.evsee" | translate }} - {{ chargingStationID.value }}</span
          >
        </ng-template>
        <div class="card-body">
          <div class="tab-content">
            <div class="tab-pane active" id="profile">
              <!-- <div class="row">
                <div class="col-md-6">
                  <mat-form-field class="form-group">
                    <mat-datetimepicker
                      #picker1
                      type="time"
                      openOnFocus="false"
                      mode="portrait"
                      timeInterval="5"
                      twelvehour="{false}"
                    >
                    </mat-datetimepicker>
                    <mat-datetimepicker-toggle
                      [for]="picker1"
                      matSuffix
                    ></mat-datetimepicker-toggle>
                    <input
                      [formControl]="startTime"
                      matInput
                      placeholder="{{ 'general.start_time' | translate }}"
                      [matDatetimepicker]="picker1"
                      autocomplete="false"
                    />
                  </mat-form-field>
                </div>
                <div class="col-md-6">
                  <mat-form-field class="form-group">
                    <mat-datetimepicker
                      #picker2
                      type="time"
                      openOnFocus="false"
                      mode="portrait"
                      timeInterval="5"
                      twelvehour="{false}"
                    >
                    </mat-datetimepicker>
                    <mat-datetimepicker-toggle
                      [for]="picker2"
                      matSuffix
                    ></mat-datetimepicker-toggle>
                    <input
                      [formControl]="closingTime"
                      matInput
                      placeholder="{{ 'general.closing_time' | translate }}"
                      [matDatetimepicker]="picker2"
                      autocomplete="false"
                    />
                  </mat-form-field>
                </div>
              </div> -->
              <div class="row pb-1">
                <div class="col-md-6">
                  <div class="form-group">
                    <mat-form-field>
                      <mat-select
                        [formControl]="connectorId"
                        placeholder="{{ 'evse.form.connectorId' | translate }}"
                        required
                      >
                        <mat-option [value]="1">{{ "evse.guns.a" | translate }}</mat-option>
                        <mat-option [value]="2">{{ "evse.guns.b" | translate }}</mat-option>
                        <mat-option [value]="3">{{ "evse.guns.c" | translate }}</mat-option>
                      </mat-select>
                      <mat-error *ngIf="connectorId.errors?.required">
                        {{ "general.mandatory_field" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-md-6">
                  <mat-form-field class="form-group">
                    <mat-datetimepicker
                      #picker3
                      type="datetime"
                      openOnFocus="false"
                      mode="portrait"
                      timeInterval="1"
                    >
                    </mat-datetimepicker>
                    <mat-datetimepicker-toggle
                      [for]="picker3"
                      matSuffix
                    ></mat-datetimepicker-toggle>
                    <input
                      [formControl]="expireDate"
                      matInput
                      placeholder="{{ 'general.expired_on' | translate }}"
                      [matDatetimepicker]="picker3"
                      autocomplete="false"
                      required
                    />
                  </mat-form-field>
                </div>
              </div>

              <div class="row pb-1">
                <div class="col-md-6">
                  <div class="form-group">
                    <mat-form-field>
                      <input
                        matInput
                        type="text"
                        readonly="true"
                        placeholder="{{ 'virtual_wallet.form_field.select_user' | translate }}"
                        class="form-field-popup"
                        (click)="assignUser()"
                        [formControl]="user"
                        required
                      />
                      <button
                        mat-button
                        matSuffix
                        mat-icon-button
                        aria-label="Add"
                        (click)="assignUser()"
                        [hidden]="readOnly"
                      >
                        <mat-icon>create</mat-icon>
                      </button>
                      <mat-error *ngIf="userID.errors?.required">
                        {{ "general.mandatory_field" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <mat-form-field>
                      <input
                        matInput
                        type="text"
                        readonly="true"
                        placeholder="{{ 'tags.title' | translate }}"
                        class="form-field-popup"
                        (click)="assignTag()"
                        [formControl]="tag"
                      />
                      <button
                        *ngIf="tag.enabled"
                        mat-button
                        matSuffix
                        mat-icon-button
                        aria-label="Add"
                        (click)="assignTag()"
                      >
                        <mat-icon>create</mat-icon>
                      </button>
                      <mat-error *ngIf="tag.errors?.required">
                        {{ "general.mandatory_field" | translate }}
                      </mat-error>
                      <mat-error *ngIf="tag.errors?.inactive">
                        {{ "tags.not_active" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <div class="row pb-1">
                <div class="col-md-6">
                  <div class="form-group">
                    <mat-form-field>
                      <mat-select
                        [formControl]="chargingStationID"
                        placeholder="{{ 'general.charging_station_id' | translate }}"
                        required
                      >
                        <ng-container *ngFor="let stations of stations">
                          <mat-option [value]="stations.id">
                            {{ stations.id }}
                          </mat-option>
                        </ng-container>
                      </mat-select>
                      <mat-error *ngIf="chargingStationID.errors?.required">
                        {{ "general.charging_station_id" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
    <div [class]="dialogRef ? 'tabs-actions' : 'tabs-actions-embedded'">
      <button
        mat-icon-button
        (click)="save(formGroup.getRawValue())"
        title="{{ 'general.save' | translate }}"
        [disabled]="!formGroup.valid || !formGroup.dirty"
      >
        <mat-icon>save</mat-icon>
      </button>
      <button
        mat-icon-button
        *ngIf="inDialog"
        (click)="close()"
        title="{{ 'general.close' | translate }}"
      >
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
