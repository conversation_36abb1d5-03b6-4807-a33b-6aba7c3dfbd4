import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { WindowService } from 'services/window.service';
import { AuthorizationDefinitionFieldMetadata, DialogMode } from 'types/Authorization';
import { DataResult } from 'types/DataResult';
import { User } from 'types/User';

import { KeyValue } from '../../../../types/GlobalType';
import { Utils } from '../../../../utils/Utils';
import { ConfigureWalletDialogComponent } from './configure-wallet.dialog.component';
import { RegisterWalletComponent } from './register-wallet/register-wallet.component';
import { VerifyWalletComponent } from './verify-wallet/verify-wallet.component';
import { PreauthWalletComponent } from './preauth-wallet/preauth-wallet.component';

@Component({
  selector: 'app-configure-wallet',
  templateUrl: './configure-wallet.component.html',
  styleUrls: ['./configure-wallet.component.scss'],
})
export class ConfigureWalletComponent implements OnInit {
  @Input() public dialogMode!: DialogMode;
  @Input() public inDialog!: boolean;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;

  @ViewChild('registerWalletComponent') public registerWalletComponent!: RegisterWalletComponent;
  @ViewChild('verifyWalletComponent') public verifyWalletComponent!: VerifyWalletComponent;
  @ViewChild('preauthWalletComponent') public preauthWalletComponent!: PreauthWalletComponent;

  public user!: User;
  public formGroup!: UntypedFormGroup;

  // Example properties
  public token: string = '';
  public stations: KeyValue[] = [];
  public currentTabIndex = 0; //default tab index is 0

  constructor(
    public dialogRef: MatDialogRef<ConfigureWalletDialogComponent>,
    protected windowService: WindowService
  ) {}

  public ngOnInit(): void {
    // Initialize form groups
    this.formGroup = new UntypedFormGroup({});

    // Handle dialog mode
    Utils.handleDialogMode(this.dialogMode, this.formGroup);
  }

  public tabChanged(tab: number): void {
    this.currentTabIndex = tab;
  }

  onTabChange(event: any) {
    this.currentTabIndex = event.index;
  }

  public close(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  public save(wallet: any): void {
    // Implement save logic
  }

  public loadDataImpl(): Observable<DataResult<any>> {
    return new Observable((observer) => {
      // Implement data loading logic
    });
  }
}
