<div class="wrapper wrapper-full-page">
  <div class="page-header login-page header-filter" filter-color="black">
    <div class="container">
      <div class="col-lg-6 ms-auto me-auto">
        <form class="form" [formGroup]="formGroup" (ngSubmit)="mobileregister(formGroup.value)">
          <div class="card card-login card-hidden">
            <div class="card-header card-header-primary text-center h-25">
              <div class="social-line">
                <img class="big-card-logo mb-2" [src]="tenantLogo" />
              </div>
              <h4 class="card-title text-center">
                {{ "authentication.mobile_register" | translate }}
              </h4>
            </div>
            <div class="card-body">
              <span class="bmd-form-group">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>account_circle</mat-icon>
                      <mat-form-field class="ms-2">
                        <input
                          appAutofocus
                          id="last-name-field"
                          matInput
                          type="text"
                          placeholder="{{ 'users.name' | translate }}"
                          [formControl]="name"
                          required
                          (input)="firstLetterToUpperCase(name)"
                        />
                        <mat-error *ngIf="name.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                  <div class="input-group-prepend input-group-no-icon">
                    <mat-form-field class="ms-2">
                      <input
                        id="first-name-field"
                        matInput
                        type="text"
                        placeholder="{{ 'users.first_name' | translate }}"
                        [formControl]="firstName"
                        (input)="firstLetterToUpperCase(firstName)"
                        required
                      />
                      <mat-error *ngIf="firstName.errors?.required"
                        >{{ "general.mandatory_field" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="input-group-prepend d-flex align-items-center">
                    <div>
                      <mat-icon>smartphone</mat-icon>
                    </div>
                    <div class="ms-2 col-md-2">
                      <mat-form-field>
                        <mat-select formControlName="countryCode">
                          <mat-option *ngFor="let country of countryCodes" [value]="country.value">
                            {{ country.label }} ({{ country.value }})
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    <div class="ms-2 col-md-9" style="flex: 2">
                      <mat-form-field>
                        <input
                          [formControl]="mobile"
                          matInput
                          placeholder="{{ 'users.mobile' | translate }}"
                          type="text"
                        />
                        <mat-error *ngIf="mobile.errors?.invalidPhone">{{
                          "users.invalid_phone_number" | translate
                        }}</mat-error>
                        <mat-error *ngIf="mobile.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="input-group-prepend text-center mt-3 ms-2">
                    <mat-checkbox [formControl]="acceptEula" required>
                      <span id="eula-checkbox" class="adapt-font-size"
                        >{{ "authentication.accept" | translate }}
                      </span>
                      <a
                        class="auth-link"
                        [routerLink]="['/auth/eula']"
                        [queryParams]="{ slug: 'terms-and-conditions' }"
                        target="_blank"
                      >
                        <span class="adapt-font-size">{{ "authentication.eula" | translate }}</span>
                      </a>
                      and
                      <a
                        class="auth-link"
                        [routerLink]="['/auth/eula']"
                        [queryParams]="{ slug: 'privacy-policy' }"
                        target="_blank"
                      >
                        <span class="adapt-font-size">{{
                          "authentication.eulaPrivacyPolicy" | translate
                        }}</span>
                      </a>
                    </mat-checkbox>
                  </div>
                </div>
              </span>
            </div>
            <div class="card-footer justify-content-center mb-4">
              <button
                id="mobileregister-button"
                mat-button
                type="submit"
                [disabled]="!formGroup.valid"
              >
                {{ "authentication.mobile_register" | translate }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
