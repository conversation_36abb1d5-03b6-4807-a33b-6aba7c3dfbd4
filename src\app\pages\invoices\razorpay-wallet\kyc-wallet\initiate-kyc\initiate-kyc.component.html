<form class="form" [formGroup]="formGroup">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <input
            appAutofocus
            [formControl]="panCardNumber"
            matInput
            placeholder="Enter PAN Card Number"
            required
            type="text"
            maxlength="10"
          />
          <mat-error *ngIf="panCardNumber.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
          <mat-error *ngIf="panCardNumber.errors?.pattern">
            <div [translate]="'general.error_invalid_pan'"></div>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-3" style="margin-top: 50px">
      <button mat-raised-button color="primary" (click)="submitPAN()">Submit</button>
    </div>
  </div>
</form>
