<div class="wrapper wrapper-full-page">
  <div class="page-header login-page header-filter" filter-color="black">
    <div class="container">
      <div class="col-lg-6 ms-auto me-auto">
        <form class="form" [formGroup]="formGroup" (ngSubmit)="register(formGroup.value)">
          <div class="card card-login card-hidden">
            <div class="card-header card-header-primary text-center h-25">
              <div class="social-line">
                <img class="big-card-logo mb-2" [src]="tenantLogo" />
              </div>
              <h4 class="card-title text-center">{{ "authentication.register" | translate }}</h4>
            </div>
            <div class="card-body">
              <span class="bmd-form-group">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>account_circle</mat-icon>
                      <mat-form-field class="ms-2">
                        <input
                          appAutofocus
                          id="last-name-field"
                          matInput
                          type="text"
                          placeholder="{{ 'users.name' | translate }}"
                          [formControl]="name"
                          required
                          (input)="toUpperCase(name)"
                        />
                        <mat-error *ngIf="name.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                  <div class="input-group-prepend input-group-no-icon">
                    <mat-form-field class="ms-2">
                      <input
                        id="first-name-field"
                        matInput
                        type="text"
                        placeholder="{{ 'users.first_name' | translate }}"
                        [formControl]="firstName"
                        (input)="firstLetterToUpperCase(firstName)"
                        required
                      />
                      <mat-error *ngIf="firstName.errors?.required"
                        >{{ "general.mandatory_field" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>email</mat-icon>
                      <mat-form-field class="ms-2">
                        <input
                          id="email-field"
                          matInput
                          type="text"
                          placeholder="{{ 'authentication.email' | translate }}"
                          [formControl]="email"
                          autocomplete="username"
                          required
                        />
                        <mat-error *ngIf="email.errors?.email">{{
                          "authentication.invalid_email" | translate
                        }}</mat-error>
                        <mat-error *ngIf="email.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                  <div class="input-group-prepend d-flex align-items-center">
                    <div>
                      <mat-icon>smartphone</mat-icon>
                    </div>
                    <div class="ms-2 col-md-2">
                      <mat-form-field>
                        <mat-select formControlName="countryCode">
                          <mat-option *ngFor="let country of countryCodes" [value]="country.value">
                            {{ country.label }} ({{ country.value }})
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    <div class="ms-2 col-md-9" style="flex: 2">
                      <mat-form-field>
                        <input
                          [formControl]="mobile"
                          matInput
                          placeholder="{{ 'users.mobile' | translate }}"
                          type="text"
                        />
                        <mat-error *ngIf="mobile.errors?.invalidPhone">{{
                          "users.invalid_phone_number" | translate
                        }}</mat-error>
                        <mat-error *ngIf="mobile.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>lock_outline</mat-icon>
                      <mat-form-field class="ms-2">
                        <input
                          id="password-field"
                          matInput
                          type="password"
                          autocomplete="new-password"
                          placeholder="{{ 'authentication.password' | translate }}"
                          [type]="hidePassword ? 'password' : 'text'"
                          [formControl]="password"
                          required
                        />
                        <mat-icon
                          matSuffix
                          class="icon-clickable"
                          (click)="hidePassword = !hidePassword"
                        >
                          {{ hidePassword ? "visibility" : "visibility_off" }}</mat-icon
                        >
                        <mat-error *ngIf="password.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                        <mat-error *ngIf="password.errors?.noSpace"
                          >{{ "authentication.no_space_in_password" | translate }}
                        </mat-error>
                        <mat-error *ngIf="password.errors?.invalidPassword"
                          >{{ "authentication.password_rule" | translate }}
                        </mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                  <div class="input-group-prepend input-group-no-icon">
                    <mat-form-field class="ms-2">
                      <input
                        id="password-repeat-field"
                        matInput
                        type="password"
                        autocomplete="new-password"
                        placeholder="{{ 'authentication.repeat_password' | translate }}"
                        [type]="hideRepeatPassword ? 'password' : 'text'"
                        [formControl]="repeatPassword"
                        [errorStateMatcher]="parentErrorStateMatcher"
                        required
                      />
                      <mat-icon
                        matSuffix
                        class="icon-clickable"
                        (click)="hideRepeatPassword = !hideRepeatPassword"
                      >
                        {{ hideRepeatPassword ? "visibility" : "visibility_off" }}</mat-icon
                      >
                      <mat-error *ngIf="repeatPassword.errors?.required"
                        >{{ "general.mandatory_field" | translate }}
                      </mat-error>
                      <mat-error
                        *ngIf="!repeatPassword.errors?.required && passwords.errors?.notEqual"
                      >
                        {{ "authentication.password_not_equal" | translate }}</mat-error
                      >
                    </mat-form-field>
                  </div>
                  <div class="input-group-prepend text-center mt-3 ms-2">
                    <mat-checkbox [formControl]="acceptEula" required>
                      <span id="eula-checkbox" class="adapt-font-size"
                        >{{ "authentication.accept" | translate }}
                      </span>
                      <a
                        class="auth-link"
                        [routerLink]="['/auth/eula']"
                        [queryParams]="{ slug: 'terms-and-conditions' }"
                        target="_blank"
                      >
                        <span class="adapt-font-size">{{ "authentication.eula" | translate }}</span>
                      </a>
                      and
                      <a
                        class="auth-link"
                        [routerLink]="['/auth/eula']"
                        [queryParams]="{ slug: 'privacy-policy' }"
                        target="_blank"
                      >
                        <span class="adapt-font-size">{{
                          "authentication.eulaPrivacyPolicy" | translate
                        }}</span>
                      </a>
                    </mat-checkbox>
                  </div>
                </div>
              </span>
            </div>
            <div class="card-footer justify-content-center mb-4">
              <button id="register-button" mat-button type="submit" [disabled]="!formGroup.valid">
                {{ "authentication.register" | translate }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
