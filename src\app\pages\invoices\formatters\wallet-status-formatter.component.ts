import { Component, Input, Pipe, PipeTransform } from '@angular/core';

import { WALLET_STATUS } from '../../../shared/model/wallet-status.model';
import { CellContentTemplateDirective } from '../../../shared/table/cell-content-template/cell-content-template.directive';
import { ChipType } from '../../../types/GlobalType';
import { WalletData, WalletTransferStatus } from '../../../types/Wallet';

@Component({
  selector: 'app-wallet-status-formatter',
  template: `
    <mat-chip-list [selectable]="false">
      <mat-chip [ngClass]="row.paymentStatus | appFormatWalletStatus:'class'" [disabled]="true">
        {{row.paymentStatus | appFormatWalletStatus:'text'}}
      </mat-chip>
    </mat-chip-list>
  `,
})
export class WalletStatusFormatterComponent extends CellContentTemplateDirective {
  @Input() public row!: WalletData;
}

@Pipe({ name: 'appFormatWalletStatus' })
export class AppFormatWalletStatusPipe implements PipeTransform {
  public transform(walletStatus: WalletTransferStatus, type: string): string {
    //console.log('======>', walletStatus);
    if (type === 'class') {
      return this.buildTransferStatusClasses(walletStatus);
    }
    if (type === 'text') {
      return this.buildTransferStatusText(walletStatus);
    }
    return '';
  }

  public buildTransferStatusClasses(status: WalletTransferStatus): string {
    let classNames = 'chip-width-8em ';
    //console.log(status);
    switch (status) {
      case WalletTransferStatus.SUCCESS:
        classNames += ChipType.SUCCESS;
        break;
      case WalletTransferStatus.FAIL:
        classNames += ChipType.DANGER;
        break;
      case WalletTransferStatus.PENGING:
        classNames += ChipType.WARNING;
         break;
      default:
        classNames += ChipType.GREY;
    }
    return classNames;
  }

  public buildTransferStatusText(status): string {
    for (const walletStatus of WALLET_STATUS) {
      if (walletStatus.key === status) {
        return walletStatus.value;
      }
    }
    return 'Unknown';
  }
}
