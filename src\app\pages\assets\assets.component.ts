import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { AuthorizationService } from '../../services/authorization.service';
import { WindowService } from '../../services/window.service';
import { AbstractTabComponent } from '../../shared/component/abstract-tab/abstract-tab.component';

@Component({
  selector: 'app-assets',
  templateUrl: 'assets.component.html',
})
export class AssetsComponent extends AbstractTabComponent {
  public canListAssets: boolean;
  public canListAssetsInError: boolean;
  public canListSmartMeters: boolean;

  public constructor(
    private authorizationService: AuthorizationService,
    activatedRoute: ActivatedRoute,
    windowService: WindowService,
  ) {
    super(activatedRoute, windowService, ['assets', 'inerror', 'smartmeters']);
    this.canListAssets = this.authorizationService.canListAssets();
    this.canListAssetsInError = this.authorizationService.canListAssetsInError();
    this.canListSmartMeters = this.authorizationService.canListAssets(); // For now, use same permission as assets
  }
}
