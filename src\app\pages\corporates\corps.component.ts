import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { AuthorizationService } from '../../services/authorization.service';
import { WindowService } from '../../services/window.service';
import { AbstractTabComponent } from '../../shared/component/abstract-tab/abstract-tab.component';

@Component({
  selector: 'app-corporate',
  templateUrl: 'corps.component.html',
})
export class CorpsComponent extends AbstractTabComponent {
  public canListCorporates: boolean;
  public canListPaymentSettlements: boolean = false;
  private authorizationService: AuthorizationService
  public constructor(
    activatedRoute: ActivatedRoute,
    authorizationService: AuthorizationService,
    windowService: WindowService) {
    super(activatedRoute, windowService, ['all', 'payment-settlement']);
      this.authorizationService = authorizationService;
    this.canListCorporates = authorizationService.canListUsers();
    this.canListPaymentSettlements = this.authorizationService.canListPaymentSettlements();
  }
}
