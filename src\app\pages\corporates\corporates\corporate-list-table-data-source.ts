import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { PricingDefinitionsDialogComponent } from 'shared/pricing-definitions/pricing-definitions.dialog.component';
import {
  TableCreateCorpAction,
  TableCreateCorpActionDef,
} from 'shared/table/actions/corporate/table-create-corporate-action';
import {
  TableDeleteCorpAction,
  TableDeleteCorpActionDef,
} from 'shared/table/actions/corporate/table-delete-corporate-action';
import {
  TableEditCorpAction,
  TableEditCorpActionDef,
} from 'shared/table/actions/corporate/table-edit-corporate-id-action';
import {
  TableViewPricingDefinitionsAction,
  TableViewPricingDefinitionsActionDef,
} from 'shared/table/actions/table-view-pricing-definitions-action';
import { Corporate, CorporateButtonAction } from 'types/Corporate';
import { PricingButtonAction, PricingEntity } from 'types/Pricing';

import { CentralServerService } from '../../../services/central-server.service';
import { ComponentService } from '../../../services/component.service';
import { DialogService } from '../../../services/dialog.service';
import { MessageService } from '../../../services/message.service';
import { SpinnerService } from '../../../services/spinner.service';
import { WindowService } from '../../../services/window.service';
import { AppDatePipe } from '../../../shared/formatters/app-date.pipe';
import { TableAutoRefreshAction } from '../../../shared/table/actions/table-auto-refresh-action';
import { TableMoreAction } from '../../../shared/table/actions/table-more-action';
import { TableRefreshAction } from '../../../shared/table/actions/table-refresh-action';
import { organizations } from '../../../shared/table/filters/issuer-filter';
import { TableDataSource } from '../../../shared/table/table-data-source';
import { CorpAuthorizations } from '../../../types/Authorization';
import { DataResult } from '../../../types/DataResult';
import { TableActionDef, TableColumnDef, TableDef, TableFilterDef } from '../../../types/Table';
import { TenantComponents } from '../../../types/Tenant';
import { Utils } from '../../../utils/Utils';
import { CorpDialogComponent } from '../corp/corp-dialog.component';
import { UserStatusFormatterComponent } from '../formatters/user-status-formatter.component';

@Injectable()
export class CorporateListTableDataSource extends TableDataSource<Corporate> {
  private editAction = new TableEditCorpAction().getActionDef();
  private deleteAction = new TableDeleteCorpAction().getActionDef();
  private createAction = new TableCreateCorpAction().getActionDef();
  private maintainPricingDefinitionsAction = new TableViewPricingDefinitionsAction().getActionDef();
  private corpAuthorizations: CorpAuthorizations;

  public constructor(
    public spinnerService: SpinnerService,
    public translateService: TranslateService,
    private messageService: MessageService,
    private dialogService: DialogService,
    private router: Router,
    private dialog: MatDialog,
    private centralServerService: CentralServerService,
    private componentService: ComponentService,
    private datePipe: AppDatePipe,
    private windowService: WindowService
  ) {
    super(spinnerService, translateService);
    // Init
    this.initDataSource();
    this.initFilters();
  }

  public initFilters() {
    // Tag
    const visualID = this.windowService.getUrlParameterValue('VisualID');
    if (visualID) {
      const tagTableFilter = this.tableFiltersDef.find((filter) => filter.id === 'tag');
      if (tagTableFilter) {
        tagTableFilter.currentValue.push({
          key: visualID,
          value: visualID,
        });
        this.filterChanged(tagTableFilter);
      }
    }
    // Issuer
    const issuer = this.windowService.getUrlParameterValue('Issuer');
    if (issuer) {
      const issuerTableFilter = this.tableFiltersDef.find((filter) => filter.id === 'issuer');
      if (issuerTableFilter) {
        issuerTableFilter.currentValue = [
          organizations.find((organisation) => organisation.key === issuer),
        ];
        this.filterChanged(issuerTableFilter);
      }
    }
  }

  public loadDataImpl(): Observable<DataResult<Corporate>> {
    return new Observable((observer) => {
      // Get the Tenants
      this.centralServerService
        .getCorporates(this.buildFilterValues(), this.getPaging(), this.getSorting())
        .subscribe(
          (users) => {
            // Initialize authorization actions
            this.corpAuthorizations = {
              // Authorization action
              canCreate: Utils.convertToBoolean(users.canCreate),
              canImport: Utils.convertToBoolean(users.canImport),
              canExport: Utils.convertToBoolean(users.canExport),
              // Metadata
              metadata: users.metadata,
            };
            this.createAction.visible = true; //this.usersAuthorizations.canCreate;
            observer.next(users);
            observer.complete();
          },
          (error) => {
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'general.error_backend'
            );
            observer.error(error);
          }
        );
    });
  }

  public buildTableDef(): TableDef {
    return {
      search: {
        enabled: true,
      },
      hasDynamicRowAction: true,
    };
  }

  public buildTableColumnDefs(): TableColumnDef[] {
    return [
      {
        id: 'status',
        name: 'users.status',
        isAngularComponent: true,
        angularComponent: UserStatusFormatterComponent,
        headerClass: 'col-10em text-center',
        class: 'col-10em table-cell-angular-big-component',
        sortable: true,
      },
      {
        id: 'id',
        name: 'general.id',
        headerClass: 'col-15p',
        class: 'text-left col-15p',
      },
      {
        id: 'CorporateName',
        name: 'corps.name',
        headerClass: 'col-15p',
        class: 'text-left col-15p',
        sorted: true,
        direction: 'asc',
        sortable: true,
      },
      {
        id: 'AuthorisedPersonName',
        name: 'corps.admin_name',
        headerClass: 'col-15p',
        class: 'text-left col-15p',
        sortable: true,
      },
      {
        id: 'GSTNo',
        name: 'corps.gst',
        headerClass: 'col-20p',
        class: 'text-left col-20p',
        sortable: true,
      },
      {
        id: 'PAN_TAN',
        name: 'corps.pt',
        headerClass: 'col-15p',
        class: 'col-15p',
        sortable: true,
        visible: this.componentService.isActive(TenantComponents.BILLING),
      },
      {
        id: 'Pricing',
        name: 'corps.pricing',
        headerClass: 'col-15p',
        class: 'col-15p',
        sortable: true,
      },
      {
        id: 'createdOn',
        name: 'corps.created_on',
        headerClass: 'col-15em',
        class: 'col-15em',
        sortable: true,
        formatter: (createdOn: Date) => this.datePipe.transform(createdOn),
      },
    ];
  }

  public buildTableActionsDef(): TableActionDef[] {
    const tableActionsDef = super.buildTableActionsDef();
    return [this.createAction, ...tableActionsDef];
  }

  public buildTableDynamicRowActions(corp: Corporate): TableActionDef[] {
    const rowActions: TableActionDef[] = [];
    const moreActions = new TableMoreAction([]);
    // if (user.issuer) {
    if (true || corp.canUpdate) {
      rowActions.push(this.editAction);
    }
    if (/*corp.isPricingComponentActive*/ true) {
      rowActions.push(this.maintainPricingDefinitionsAction);
    }
    //   if (this.componentService.isActive(TenantComponents.ORGANIZATION)) {
    //     if (user.canAssignUnassignSites) {
    //       rowActions.push(this.assignSitesToUser);
    //     } else if (user.canListUserSites) {
    //       rowActions.push(this.viewSitesOfUser);
    //     }
    //   }
    //   if (this.componentService.isActive(TenantComponents.BILLING)) {
    //     if (user.canSynchronizeBillingUser) {
    //       moreActions.addActionInMoreActions(this.synchronizeBillingUserAction);
    //     }
    //   }
    // }
    // if (user.canListTags) {
    //   moreActions.addActionInMoreActions(this.navigateToTagsAction);
    // }
    // if (user.canListCompletedTransactions) {
    //   moreActions.addActionInMoreActions(this.navigateToTransactionsAction);
    // }
    if (true || corp.canDelete) {
      rowActions.push(this.deleteAction);
    }
    return rowActions;
  }

  public actionTriggered(actionDef: TableActionDef) {
    // Action
    switch (actionDef.id) {
      case CorporateButtonAction.CREATE_CORP:
        if (actionDef.action) {
          (actionDef as TableCreateCorpActionDef).action(
            CorpDialogComponent,
            this.dialog,
            { authorizations: this.corpAuthorizations },
            this.refreshData.bind(this)
          );
        }
        break;
      // case CorporateButtonAction.EXPORT_USERS:
      //   if (actionDef.action) {
      //     (actionDef as TableExportUsersActionDef).action(this.buildFilterValues(), this.dialogService,
      //       this.translateService, this.messageService, this.centralServerService, this.router,
      //       this.spinnerService);
      //   }
      //   break;
      // case UserButtonAction.IMPORT_USERS:
      //   if (actionDef.action) {
      //     (actionDef as TableImportUsersActionDef).action(ImportDialogComponent, this.dialog);
      //   }
      //   break;
    }
  }

  public rowActionTriggered(actionDef: TableActionDef, corp: Corporate) {
    switch (actionDef.id) {
      case CorporateButtonAction.EDIT_CORP:
        if (actionDef.action) {
          (actionDef as TableEditCorpActionDef).action(
            CorpDialogComponent,
            this.dialog,
            { dialogData: corp, authorizations: this.corpAuthorizations },
            this.refreshData.bind(this)
          );
        }
        break;

      case PricingButtonAction.VIEW_PRICING_DEFINITIONS:
        if (actionDef.action) {
          (actionDef as TableViewPricingDefinitionsActionDef).action(
            PricingDefinitionsDialogComponent,
            this.dialog,
            {
              dialogData: {
                id: null,
                context: {
                  entityID: corp.id,
                  entityType: PricingEntity.CORP,
                  entityName: corp.CorporateName,
                },
              },
            },
            this.refreshData.bind(this)
          );
        }
        break;
      case CorporateButtonAction.DELETE_CORP:
        if (actionDef.action) {
          (actionDef as TableDeleteCorpActionDef).action(
            corp,
            this.dialogService,
            this.translateService,
            this.messageService,
            this.centralServerService,
            this.spinnerService,
            this.router,
            this.refreshData.bind(this)
          );
        }
        break;
    }
  }

  public buildTableActionsRightDef(): TableActionDef[] {
    return [
      new TableAutoRefreshAction(false).getActionDef(),
      new TableRefreshAction().getActionDef(),
    ];
  }

  public buildTableFiltersDef(): TableFilterDef[] {
    // this.issuerFilter = new IssuerFilter().getFilterDef();
    // this.userRoleFilter = new UserRoleFilter(this.centralServerService).getFilterDef();
    // this.userStatusFilter = new UserStatusFilter().getFilterDef();
    // this.tagFilter = new TagTableFilter().getFilterDef();
    // this.siteFilter = new SiteTableFilter([this.issuerFilter]).getFilterDef();
    // this.userTechnicalFilter = new UserTechnicalFilter().getFilterDef();
    // this.userFreeAccessFilter = new UserFreeAccessFilter().getFilterDef();
    // const filters: TableFilterDef[] = [
    //   this.issuerFilter,
    //   this.userRoleFilter,
    //   this.userStatusFilter,
    //   this.tagFilter,
    //   this.siteFilter,
    //   this.userTechnicalFilter,
    //   this.userFreeAccessFilter,
    // ];
    const filters: TableFilterDef[] = [];
    return filters;
  }
}
