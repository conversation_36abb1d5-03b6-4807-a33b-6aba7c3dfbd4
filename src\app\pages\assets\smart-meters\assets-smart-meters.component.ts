import { Component } from '@angular/core';
//import { AssetComponent } from '../asset/asset.component';
import { AssetsSmartMetersTableDataSource } from './assets-smart-meters-table-data-source';

@Component({
  selector: 'app-assets-smart-meters',
  template: '<app-table [dataSource]="assetsSmartMetersTableDataSource"></app-table>',
  providers: [AssetsSmartMetersTableDataSource],
})//
export class AssetsSmartMetersComponent {
  // eslint-disable-next-line no-useless-constructor
  public constructor(
    public assetsSmartMetersTableDataSource: AssetsSmartMetersTableDataSource,
  ) {}
}
