import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { StatusCodes } from 'http-status-codes';
import { WindowService } from 'services/window.service';
import { AbstractTabComponent } from 'shared/component/abstract-tab/abstract-tab.component';
import { CorpAuthorizations, DialogMode } from 'types/Authorization';
import { Corporate } from 'types/Corporate';

import { CentralServerService } from '../../../services/central-server.service';
import { DialogService } from '../../../services/dialog.service';
import { MessageService } from '../../../services/message.service';
import { SpinnerService } from '../../../services/spinner.service';
import { Address } from '../../../types/Address';
import { ActionResponse } from '../../../types/DataResult';
import { RestResponse } from '../../../types/GlobalType';
import { HTTPError } from '../../../types/HTTPError';
import { Utils } from '../../../utils/Utils';

@Component({
  selector: 'app-corp',
  templateUrl: 'corp.component.html',
  styleUrls: ['corp.component.scss'],
})
export class CorpComponent extends AbstractTabComponent implements OnInit {
  @Input() public currentCorpID!: string;
  @Input() public dialogRef!: MatDialogRef<any>;
  @Input() public corpAuthorizations!: CorpAuthorizations;
  @Input() public dialogMode!: DialogMode;

  public formGroup!: UntypedFormGroup;
  public readOnly = true;
  public corp!: Corporate;
  public CorporateAddress!: Address;
  public priceCatalog!: any;

  // eslint-disable-next-line no-useless-constructor
  public constructor(
    public spinnerService: SpinnerService,
    private centralServerService: CentralServerService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private dialogService: DialogService,
    protected activatedRoute: ActivatedRoute,
    protected windowService: WindowService,
    private router: Router
  ) {
    super(activatedRoute, windowService, ['main'], false);
  }

  public ngOnInit() {
    // Init the form
    this.formGroup = new UntypedFormGroup({});
    //Get all prices
    this.centralServerService.getEntityPricing(this.currentCorpID).subscribe({
      next: (entityPrices) => {
        this.priceCatalog = entityPrices.result;
      },
      error: (error) => {
        this.priceCatalog = null;
      },
    });
    // Handle Dialog mode
    this.readOnly = this.dialogMode === DialogMode.VIEW;
    Utils.handleDialogMode(this.dialogMode, this.formGroup);
    // Load Corp
    this.loadCorp();
  }

  public onClose() {
    this.closeDialog();
  }

  public loadCorp() {
    if (this.currentCorpID) {
      this.spinnerService.show();
      this.centralServerService.getCorp(this.currentCorpID).subscribe(
        (corp: Corporate) => {
          this.spinnerService.hide();
          this.corp = corp;
          if (this.readOnly) {
            // Async call for letting the sub form groups to init
            setTimeout(() => this.formGroup.disable(), 0);
          }
          // Update form group
          this.formGroup.updateValueAndValidity();
          this.formGroup.markAsPristine();
          this.formGroup.markAllAsTouched();
          if (corp.CorporateAddress) {
            this.CorporateAddress = corp.CorporateAddress;
          }
        },
        (error) => {
          this.spinnerService.hide();
          switch (error.status) {
            case StatusCodes.NOT_FOUND:
              this.messageService.showErrorMessage('corps.corp_not_found');
              break;
            default:
              Utils.handleHttpError(
                error,
                this.router,
                this.messageService,
                this.centralServerService,
                'corps.corp_error'
              );
          }
        }
      );
    }
  }

  public closeDialog(saved: boolean = false) {
    if (this.dialogRef) {
      this.dialogRef.close(saved);
    }
  }

  public close() {
    Utils.checkAndSaveAndCloseDialog(
      this.formGroup,
      this.dialogService,
      this.translateService,
      this.saveCorp.bind(this),
      this.closeDialog.bind(this)
    );
  }

  public saveCorp(corp: Corporate) {
    if (this.currentCorpID) {
      this.updateCorp(corp);
    } else {
      this.createCorp(corp);
    }
  }

  private updateCorp(corp: Corporate) {
    this.spinnerService.show();
    this.centralServerService.updateCorp(this.currentCorpID, corp).subscribe(
      (response: ActionResponse) => {
        this.spinnerService.hide();
        if (response.status === RestResponse.SUCCESS) {
          this.messageService.showSuccessMessage('corps.update_success', {
            cname: corp.CorporateName,
          });
          this.closeDialog(true);
        } else {
          Utils.handleError(JSON.stringify(response), this.messageService, 'corps.update_error');
        }
      },
      (error) => {
        this.spinnerService.hide();
        switch (error.status) {
          case HTTPError.USER_EMAIL_ALREADY_EXIST_ERROR:
            this.messageService.showErrorMessage('corps.corp_already_used', {
              cname: corp.CorporateName,
            });
            break;
          default:
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'corps.update_error'
            );
        }
      }
    );
  }

  private createCorp(corp: Corporate) {
    this.spinnerService.show();
    this.centralServerService.createCorp(corp).subscribe(
      (response: ActionResponse) => {
        this.spinnerService.hide();
        if (response.status === RestResponse.SUCCESS) {
          this.messageService.showSuccessMessage('corps.create_success', {
            cname: corp.CorporateName,
          });
          this.closeDialog(true);
        } else {
          Utils.handleError(JSON.stringify(response), this.messageService, 'corps.create_error');
        }
      },
      (error) => {
        this.spinnerService.hide();
        switch (error.status) {
          case HTTPError.USER_EMAIL_ALREADY_EXIST_ERROR:
            this.messageService.showErrorMessage('corps.corp_already_used', {
              cname: corp.CorporateName,
            });
            break;
          default:
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'corps.create_error'
            );
        }
      }
    );
  }
}
