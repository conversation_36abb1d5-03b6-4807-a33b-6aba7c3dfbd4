<div class="wrapper wrapper-full-page">
  <div class="page-header login-page header-filter" filter-color="black">
    <div class="container">
      <div class="col-lg-6 ms-auto me-auto">
        <form class="form" [formGroup]="formGroup" (ngSubmit)="resetPassword(formGroup.value)">
          <div class="card card-login card-hidden">
            <div class="card-header card-header-primary text-center h-25">
              <div class="social-line">
                <img class="big-card-logo mb-2" [src]="tenantLogo" />
              </div>
              <h4 class="card-title text-center">{{"authentication.define_password_title" | translate}}</h4>
            </div>
            <div class="card-body">
              <span class="bmd-form-group">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>lock_outline</mat-icon>
                      <mat-form-field class="ms-2">
                        <input appAutofocus id="password-field" matInput type="password" autocomplete="new-password"
                          placeholder="{{'authentication.password' | translate}}"
                          [type]="hidePassword ? 'password' : 'text'" [formControl]="password" required>
                        <mat-icon matSuffix class="icon-clickable" (click)="hidePassword = !hidePassword">
                          {{hidePassword ? 'visibility' : 'visibility_off'}}</mat-icon>
                        <mat-error *ngIf="password.errors?.required">{{"general.mandatory_field" | translate}}</mat-error>
                        <mat-error *ngIf="password.errors?.noSpace">{{"authentication.no_space_in_password" | translate}}
                        </mat-error>
                        <mat-error *ngIf="password.errors?.invalidPassword">{{"authentication.password_rule" | translate}}
                        </mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>lock_outline</mat-icon>
                      <mat-form-field class="ms-2">
                        <input id="password-repeat-field" matInput type="password" autocomplete="new-password"
                          placeholder="{{'authentication.repeat_password' | translate}}"
                          [type]="hideRepeatPassword ? 'password' : 'text'" [formControl]="repeatPassword"
                          [errorStateMatcher]="parentErrorStateMatcher" required>
                        <mat-icon matSuffix class="icon-clickable" (click)="hideRepeatPassword = !hideRepeatPassword">
                          {{hideRepeatPassword ? 'visibility' : 'visibility_off'}}</mat-icon>
                        <mat-error *ngIf="repeatPassword.errors?.required">{{"general.mandatory_field" | translate}}
                        </mat-error>
                        <mat-error *ngIf="!repeatPassword.errors?.required && passwords.errors?.notEqual">
                          {{"authentication.password_not_equal" | translate}}</mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                </div>
                <div class="recaptcha-text">
                  {{"general.captcha_text_1" | translate}}
                  <a href="https://policies.google.com/privacy"
                    target="_blank">{{"general.captcha_text_2" | translate}}</a>{{"general.captcha_text_3" | translate}}
                  <a href="https://policies.google.com/terms"
                    target="_blank">{{"general.captcha_text_4" | translate}}</a>{{"general.captcha_text_5" | translate}}
                  .
                </div>
              </span>
            </div>
            <div class="card-footer justify-content-center mb-4">
              <button mat-button type="submit"
                [disabled]="!formGroup.valid">{{"authentication.define_password_button" | translate}}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
