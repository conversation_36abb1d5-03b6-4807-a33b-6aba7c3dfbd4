import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';
import { AuthorizationDefinitionFieldMetadata } from 'types/Authorization';
import { User, UserToken } from 'types/User';

@Component({
  selector: 'app-preauth-wallet',
  templateUrl: 'preauth-wallet.component.html',
})
export class PreauthWalletComponent implements OnInit, OnChanges {
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;
  @Output() public tabChanged = new EventEmitter<any>();

  public loggedUser: UserToken;
  public user: User;
  public token_expire_msg = `Your token is not active, Please activate it.`;
  public preauth_obj: any = {};
  public formGroup!: UntypedFormGroup;
  public otp!: AbstractControl;
  public displayForm = false;

  public constructor(
    private centralServerService: CentralServerService,
    private messageService: MessageService
  ) {
    this.loggedUser = this.centralServerService.getLoggedUser();
    this.getUser();
  }

  public async ngOnInit(): Promise<void> {
    // Initialize form groups
    this.formGroup = new UntypedFormGroup({});
    this.formGroup.addControl(
      'otp',
      new UntypedFormControl('', Validators.compose([Validators.required]))
    );
    this.otp = this.formGroup.controls['otp'];

    // Check pre-auth token status if not already checked
    if (this.user?.razorpayWallet?.account_id) {
      this.getWalletPreAuthTokenStatus();
    }
  }

  public ngOnChanges() {
    console.log('ng On Changes...');
  }

  generatePreAuthOTP() {
    this.centralServerService.sendRazorpayWalletPreAuthTokenOTP().subscribe({
      next: (result: any) => {
        this.preauth_obj = result;
        localStorage.setItem('preauth_obj', JSON.stringify(result)); // Save preauth_obj in local storage
        this.messageService.showSuccessMessage('Pre auth token OTP sent successfully.');
        this.displayForm = true;
      },
      error: (error) => {
        this.messageService.showErrorMessage(error?.details?.errorMessage);
      },
    });
  }

  verifyOTP() {
    if (this.formGroup.valid) {
      const params = {
        preauth_token_id: this.preauth_obj?.id,
        otp: this.otp.value,
      };
      this.centralServerService.verifyRazorpayWalletPreAuthTokenOTP(params).subscribe({
        next: (result) => {
          // Set the token's expiration date to 90 days from now
          const newExpireDate = moment().add(90, 'days').unix(); // Store as UNIX timestamp
          this.preauth_obj.expire_at = newExpireDate;
          localStorage.setItem('preauth_obj', JSON.stringify(this.preauth_obj)); // Update in local storage

          this.messageService.showSuccessMessage(
            'Pre-auth token activated successfully and will expire in 90 days.'
          );
          this.displayForm = false;
          this.getWalletPreAuthTokenStatus(); // update preauth token message to display on screen
          this.tabChanged.emit(3); // Jump to the next tab
        },
        error: (error) => {
          this.messageService.showErrorMessage(error?.details?.errorMessage);
        },
      });
    } else {
      this.messageService.showErrorMessage('Please fill out OTP.');
    }
  }

  resendOTP() {
    this.centralServerService.resendRazorpayWalletPreAuthTokenOTP(this.preauth_obj?.id).subscribe({
      next: (result: any) => {
        this.messageService.showSuccessMessage('Pre auth token OTP resend successfully.');
      },
      error: (error) => {
        this.messageService.showErrorMessage(error?.details?.errorMessage);
      },
    });
  }

  getWalletPreAuthTokenStatus() {
    const storedPreauthObj = localStorage.getItem('preauth_obj');
    if (storedPreauthObj) {
      this.preauth_obj = JSON.parse(storedPreauthObj);
      if (this.preauth_obj?.status === 'active') {
        const expireDate = moment(this.preauth_obj.expire_at, 'X').format('MMMM Do YYYY, h:mm a');
        this.token_expire_msg = `Your token will expire on ${expireDate}`;
      }
      return; // Skip further API call
    }

    this.centralServerService
      .getRazorpayWalletPreAuthTokenStatus(this.user?.razorpayWallet?.account_id)
      .subscribe({
        next: (result: any) => {
          this.preauth_obj = result;
          localStorage.setItem('preauth_obj', JSON.stringify(result)); // Save in local storage

          if (result?.status === 'active') {
            const expireDate = moment(result.expire_at, 'X').format('MMMM Do YYYY, h:mm a');
            this.token_expire_msg = `Your token will expire on ${expireDate}`;
          }
        },
        error: (error) => {
          this.messageService.showErrorMessage('Unable to retrieve token status.');
        },
      });
  }

  public getUser() {
    if (this.loggedUser.id) {
      this.centralServerService.getUser(this.loggedUser.id).subscribe({
        next: (user) => {
          this.user = user;
        },
        error: (error) => {
          this.messageService.showErrorMessage('users.user_do_not_exist');
        },
      });
    }
  }
}
