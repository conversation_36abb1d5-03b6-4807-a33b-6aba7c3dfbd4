import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AssetsAuthorizations } from 'types/Authorization';

import { CentralServerService } from '../../../../services/central-server.service';
import { ComponentService } from '../../../../services/component.service';
import { MessageService } from '../../../../services/message.service';
import { SpinnerService } from '../../../../services/spinner.service';
import { Asset } from '../../../../types/Asset';
import { KeyValue } from '../../../../types/GlobalType';
import { Utils } from '../../../../utils/Utils';

@Component({
  selector: 'app-asset-connection',
  templateUrl: 'asset-connection.component.html',
  styleUrls: ['asset-connection.component.scss'],
})
export class AssetConnectionComponent implements OnInit, OnChanges {
  @Input() public formGroup: UntypedFormGroup;
  @Input() public asset!: Asset;
  @Input() public readOnly: boolean;
  @Input() public assetsAuthorizations!: AssetsAuthorizations;
  @Input() public isSmartMeter: boolean = false;

  public assetConnections!: KeyValue[];
  public initialized = false;

  public dynamicAsset!: AbstractControl;
  public usesPushAPI!: AbstractControl;
  public connectionID!: AbstractControl;
  public meterID!: AbstractControl;

  // Smart Meter specific fields
  public meterName!: AbstractControl;
  public publicKey!: AbstractControl;
  public privateKey!: AbstractControl;
  public url!: AbstractControl;

  // eslint-disable-next-line no-useless-constructor
  public constructor(
    private centralServerService: CentralServerService,
    private componentService: ComponentService,
    private messageService: MessageService,
    private spinnerService: SpinnerService,
    private router: Router
  ) {
  }

  public ngOnInit() {
    // Init the form
    if (!this.formGroup) {
      console.error('FormGroup not provided to AssetConnectionComponent');
      return;
    }

    if (this.isSmartMeter) {
      // Smart Meter specific fields - start with minimal validation to avoid "Invalid Content" errors
      this.formGroup.addControl('meterName', new UntypedFormControl('',
        Validators.compose([
          Validators.maxLength(255),
        ])
      ));
      this.formGroup.addControl('meterID', new UntypedFormControl('',
        Validators.compose([
          Validators.maxLength(200),
        ])
      ));
      this.formGroup.addControl('publicKey', new UntypedFormControl(''));
      this.formGroup.addControl('privateKey', new UntypedFormControl(''));
      this.formGroup.addControl('url', new UntypedFormControl('',
        Validators.compose([
          Validators.pattern('^https?://.+'),
        ])
      ));

      // Form controls for smart meter
      this.meterName = this.formGroup.controls['meterName'];
      this.meterID = this.formGroup.controls['meterID'];
      this.publicKey = this.formGroup.controls['publicKey'];
      this.privateKey = this.formGroup.controls['privateKey'];
      this.url = this.formGroup.controls['url'];

      // Update form group validation
      this.formGroup.updateValueAndValidity();
    } else {
      // Regular asset connection fields
      this.formGroup.addControl('connectionID', new UntypedFormControl('',
        Validators.compose([
          Validators.required,
        ])
      ));
      this.formGroup.addControl('dynamicAsset', new UntypedFormControl(false,
        Validators.compose([
          Validators.required,
        ])
      ));
      this.formGroup.addControl('usesPushAPI', new UntypedFormControl(false));
      this.formGroup.addControl('meterID', new UntypedFormControl('',
        Validators.compose([
          Validators.required,
        ])
      ));

      // Form controls for regular assets
      this.dynamicAsset = this.formGroup.controls['dynamicAsset'];
      this.usesPushAPI = this.formGroup.controls['usesPushAPI'];
      this.connectionID = this.formGroup.controls['connectionID'];
      this.meterID = this.formGroup.controls['meterID'];

      // Disable connection form by default
      this.disableConnectionDetails();
      this.loadAssetConnection();

      // Update form group validation
      this.formGroup.updateValueAndValidity();
    }

    this.initialized = true;
    this.loadAsset();
  }

  public ngOnChanges() {
    this.loadAsset();
  }

  public loadAsset() {
    if (this.initialized && this.asset) {
      if (this.isSmartMeter) {
        // Load smart meter specific data
        if ((this.asset as any).meterName) {
          this.meterName.setValue((this.asset as any).meterName);
        }
        if ((this.asset as any).meterID) {
          this.meterID.setValue((this.asset as any).meterID);
        }
        if ((this.asset as any).publicKey) {
          this.publicKey.setValue((this.asset as any).publicKey);
        }
        if ((this.asset as any).privateKey) {
          this.privateKey.setValue((this.asset as any).privateKey);
        }
        if ((this.asset as any).url) {
          this.url.setValue((this.asset as any).url);
        }
      } else {
        // Load regular asset connection data
        if (this.asset.dynamicAsset) {
          this.dynamicAsset.setValue(this.asset.dynamicAsset);
          this.usesPushAPI.setValue(this.asset.usesPushAPI);
          this.disableConnectionDetails();
        }
        if (this.asset.connectionID) {
          this.connectionID.setValue(this.asset.connectionID);
        }
        if (this.asset.meterID) {
          this.meterID.setValue(this.asset.meterID);
        }
      }
    }
  }

  public disableConnectionDetails() {
    if (Utils.convertToBoolean(this.dynamicAsset.value && !Utils.convertToBoolean(this.usesPushAPI.value))) {
      this.connectionID.enable();
      this.meterID.enable();
    } else {
      this.connectionID.disable();
      this.meterID.disable();
      this.connectionID.reset();
      this.meterID.reset();
    }
    if (!Utils.convertToBoolean(this.dynamicAsset.value)) {
      this.usesPushAPI.reset();
      this.usesPushAPI.disable();
    } else {
      this.usesPushAPI.enable();
    }
  }

  public updateSmartMeterData(asset: Asset) {
    if (this.isSmartMeter) {
      // Set smart meter specific data
      (asset as any).meterName = this.meterName.value;
      (asset as any).meterID = this.meterID.value;
      (asset as any).publicKey = this.publicKey.value;
      (asset as any).privateKey = this.privateKey.value;
      (asset as any).url = this.url.value;
    }
  }

  public markFormAsDirty() {
    if (this.isSmartMeter && this.meterName && this.meterID && this.publicKey && this.privateKey && this.url) {
      try {
        this.meterName.markAsDirty();
        this.meterID.markAsDirty();
        this.publicKey.markAsDirty();
        this.privateKey.markAsDirty();
        this.url.markAsDirty();
        this.formGroup.markAsDirty();
        this.formGroup.updateValueAndValidity();

        // Also mark the form as touched to ensure validation is triggered
        this.meterName.markAsTouched();
        this.meterID.markAsTouched();
        this.publicKey.markAsTouched();
        this.privateKey.markAsTouched();
        this.url.markAsTouched();
      } catch (error) {
        console.warn('Error marking form as dirty:', error);
      }
    }
  }

  public loadAssetConnection() {
    this.spinnerService.show();
    this.componentService.getAssetSettings().subscribe({
      next: (assetSettings) => {
        this.spinnerService.hide();
        if (assetSettings) {
          const connections = [] as KeyValue[];
          for (const connection of assetSettings.asset.connections) {
            connections.push({ key: connection.id, value: connection.name });
          }
          this.assetConnections = connections;
        }
      },
      error: (error) => {
        this.spinnerService.hide();
        Utils.handleHttpError(error, this.router, this.messageService,
          this.centralServerService, 'assets.asset_settings_error');
      }
    });
  }
}
