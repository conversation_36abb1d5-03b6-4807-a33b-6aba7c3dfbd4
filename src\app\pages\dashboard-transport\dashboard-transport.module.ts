import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog, MatDialogActions } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ChartModule } from 'angular2-chartjs';
import { MomentModule } from 'ngx-moment';
import { PricingDefinitionsModule } from 'shared/pricing-definitions/pricing-definitions.module';

import { MaterialModule } from '../../app.module';
import { ComponentModule } from '../../shared/component/component.module';
import { DialogsModule } from '../../shared/dialogs/dialogs.module';
import { CommonDirectivesModule } from '../../shared/directives/directives.module';
import { FormattersModule } from '../../shared/formatters/formatters.module';
import { TableModule } from '../../shared/table/table.module';
import { DashboardTransportComponent } from './dashboard-transport.component';
import { DashboardTransportRoutes } from './dashboard-transport.routing';

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(DashboardTransportRoutes),
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MaterialModule,
    TableModule,
    CommonDirectivesModule,
    DialogsModule,
    MatProgressBarModule,
    FormattersModule,
    ComponentModule,
    ChartModule,
    MomentModule,
    PricingDefinitionsModule,
    // MatDialog,
    // MatDialogActions
  ],
  declarations: [DashboardTransportComponent],
  providers: [],
})
export class DashboardTransportModule {}
