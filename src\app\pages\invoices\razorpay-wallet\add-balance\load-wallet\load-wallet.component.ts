import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
  ElementRef,
  Inject,
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';
import { AuthorizationDefinitionFieldMetadata } from 'types/Authorization';

@Component({
  selector: 'app-load-wallet',
  templateUrl: 'load-wallet.component.html',
})
// @Injectable()
export class LoadWalletComponent implements OnInit, OnChanges {
  // @Input() public formGroup: UntypedFormGroup;
  @Input() public user!: any;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;
  @Output() public tabChanged = new EventEmitter<any>();
  @Output() public closePopup = new EventEmitter<any>();
  // @ViewChild('iframe') iframe: ElementRef;

  public formGroup!: UntypedFormGroup;
  public amount!: AbstractControl;
  public paymentLink: string = '';

  public constructor(
    @Inject(DOCUMENT) private document: Document,
    private centralServerService: CentralServerService,
    private messageService: MessageService,
    private router: Router
  ) {}

  public async ngOnInit(): Promise<void> {
    // Initialize form groups
    this.formGroup = new UntypedFormGroup({});

    // Init the form
    this.formGroup.addControl(
      'amount',
      new UntypedFormControl(
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern(/^\d+$/),
          Validators.min(1),
          Validators.max(100000),
        ])
      )
    );

    // Form
    this.amount = this.formGroup.controls['amount'];
  }

  public ngOnChanges() {
    console.log('ng On Changes...');
  }

  addBalance() {
    if (this.formGroup.valid) {
      const params = {
        amount: this.amount.value,
      };
      this.centralServerService.addRazorpayWalletBalance(params).subscribe({
        next: (response: any) => {
          // console.log('load balance response::::', response);

          // this.messageService.showSuccessMessage('Redirecting on Razorpay payment page.');
          // this.closePopup.emit();

          this.openWindow(response.user_load_url);

          // window.open(
          //   response?.user_load_url
          //   // '_blank' // <- This is what makes it open in a new window.
          // );

          // this.document.location.href = response?.user_load_url;

          // this.paymentLink = response?.user_load_url;

          // $('#paymentIframeDiv').load(response?.user_load_url, function (response, status, xhr) {
          //   if (status == 'error') {
          //     var msg = 'Sorry but there was an error: ';
          //     alert(msg + xhr.status + ' ' + xhr.statusText);
          //   }
          // });

          // this.router.navigate(response?.user_load_url);
          // // this.tabChanged.emit(3); // jump to next tab
        },
        error: (error) => {
          this.messageService.showErrorMessage(error?.details?.errorMessage);
        },
      });
    } else {
      this.messageService.showErrorMessage('Please fill out amount.');
    }
  }

  openWindow(myURL: string, title = 'Load Balance', myWidth = 900, myHeight = 700) {
    var left = (screen.width - myWidth) / 2;

    var top = (screen.height - myHeight) / 4;

    var razorpayWindow = window.open(
      myURL,
      title,
      'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=' +
        myWidth +
        ', height=' +
        myHeight +
        ', top=' +
        top +
        ', left=' +
        left
    );

    let razorpayInterval = setInterval(() => {
      if (razorpayWindow.closed) {
        clearInterval(razorpayInterval);
        this.messageService.showSuccessMessage('Wallet loaded successfully.');
        this.closePopup.emit();
        location.reload();
      }
    }, 1000);
  }
}
