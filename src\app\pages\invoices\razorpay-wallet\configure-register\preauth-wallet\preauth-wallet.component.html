<form class="form" [formGroup]="formGroup" *ngIf="displayForm">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <input
            appAutofocus
            [formControl]="otp"
            matInput
            placeholder="OTP"
            required
            minlength="4"
            maxlength="6"
            type="text"
          />
          <mat-error *ngIf="otp.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-3" style="margin-top: 50px">
      <button mat-raised-button color="primary" (click)="verifyOTP()">Verify PreAuth Token</button>
    </div>
    <div class="col-md-3" style="margin-top: 50px">
      <span
        style="color: #1976d2; text-decoration: underline; cursor: pointer"
        (click)="resendOTP()"
        >Resend</span
      >
    </div>
  </div>
</form>

<div class="row" *ngIf="!displayForm">
  <div *ngIf="token_expire_msg" class="col-md-12" style="margin-top: 50px">
    <span>{{ token_expire_msg }}</span>
  </div>
  <div class="col-md-9" style="margin-top: 50px">
    <button mat-raised-button color="primary" (click)="generatePreAuthOTP()">
      Generate PreAuth Token
    </button>
  </div>
</div>
