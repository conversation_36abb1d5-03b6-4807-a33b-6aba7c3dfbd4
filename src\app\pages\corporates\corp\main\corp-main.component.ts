import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms'; // Import FormGroup
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CorpAuthorizations, DialogMode } from 'types/Authorization';
import { Corporate } from 'types/Corporate';
import { StatusCodes } from 'http-status-codes';
import { ActivatedRoute, Router } from '@angular/router';

import { SpinnerService } from '../../../../services/spinner.service';
import { UsersDialogComponent } from '../../../../shared/dialogs/users/users-dialog.component';
import { Utils } from '../../../../utils/Utils';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';

@Component({
  selector: 'app-corp-main',
  templateUrl: 'corp-main.component.html',
})
export class CorpMainComponent implements OnInit, OnChanges {
  @Input() public formGroup: FormGroup; // Change to FormGroup
  @Input() public corp!: Corporate;
  @Input() public readOnly: boolean;
  @Input() public corpAuthorizations!: CorpAuthorizations;
  @Input() public dialogMode!: DialogMode;
  @Input() public priceCatalog!: any;

  public corpPersonsList!: any;
  public idVisible = true;
  public CorporateName!: AbstractControl;
  public AuthorisedPerson!: AbstractControl;
  public AuthorisedPersonName!: AbstractControl;
  public PAN_TAN!: AbstractControl;
  public GSTNo!: AbstractControl;
  public Pricing!: AbstractControl;
  public Billing!: AbstractControl;
  public CorpAddress!: AbstractControl;
  public readonly DialogMode = DialogMode;

  public constructor(
    public spinnerService: SpinnerService,
    private centralServerService: CentralServerService,
    private messageService: MessageService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  public ngOnInit() {
    // Initialize the form
    this.formGroup.addControl(
      'CorporateName',
      new FormControl('', Validators.compose([Validators.required]))
    );
    this.formGroup.addControl(
      'AuthorisedPerson',
      new FormControl('', Validators.compose([Validators.required]))
    );
    this.formGroup.addControl('AuthorisedPersonName', new FormControl(''));
    this.formGroup.addControl(
      'PAN_TAN',
      new FormControl('', Validators.compose([Validators.required]))
    );
    this.formGroup.addControl(
      'GSTNo',
      new FormControl('', Validators.compose([Validators.required]))
    );
    this.formGroup.addControl(
      'Pricing',
      new FormControl('--', Validators.compose([Validators.required]))
    );
    this.formGroup.addControl(
      'Billing',
      new FormControl('', Validators.compose([Validators.required]))
    );
    // Form
    this.CorporateName = this.formGroup.controls['CorporateName'];
    this.AuthorisedPerson = this.formGroup.controls['AuthorisedPerson'];
    this.AuthorisedPersonName = this.formGroup.controls['AuthorisedPersonName'];
    this.PAN_TAN = this.formGroup.controls['PAN_TAN'];
    this.GSTNo = this.formGroup.controls['GSTNo'];
    this.Pricing = this.formGroup.controls['Pricing'];
    this.Billing = this.formGroup.controls['Billing'];
    //this.CorpAddress = this.formGroup.controls['CorporateAddress']

    this.loadCorp();
    // Load Corp Persons List
    this.getCorpPersonsList();
  }

  public ngOnChanges() {
    this.loadCorp();
  }

  public loadCorp() {
    console.log('corp::::::::::', this.corp);
    if (this.corp) {
      this.CorporateName.setValue(this.corp.CorporateName);
      this.AuthorisedPerson.setValue(this.corp.AuthorisedPerson);
      this.AuthorisedPersonName.setValue(this.corp.AuthorisedPersonName);
      this.Pricing.setValue(this.corp.Pricing);
      this.Billing.setValue(this.corp.Billing);
      this.PAN_TAN.setValue(this.corp.PAN_TAN);
      this.GSTNo.setValue(this.corp.GSTNo);
      //this.CorpAddress.setValue(this.corp.CorporateAddress.address1);
    }
  }

  public getCorpPersonsList() {
    this.spinnerService.show();
    this.centralServerService.getCorpPersonsList().subscribe(
      (corpPersonsList: any) => {
        console.log('corpPersonsList::::', corpPersonsList);
        this.spinnerService.hide();
        this.corpPersonsList = corpPersonsList.result;
      },
      (error) => {
        this.spinnerService.hide();
        switch (error.status) {
          case StatusCodes.NOT_FOUND:
            this.messageService.showErrorMessage('corps.corp_not_found');
            break;
          default:
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'corps.corp_error'
            );
        }
      }
    );
  }

  public assignUser() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'transparent-dialog-container';
    // Set data
    dialogConfig.data = {
      rowMultipleSelection: false,
      staticFilter: {},
      title: 'Select Authorised Person',
    };
    // Open
    this.dialog
      .open(UsersDialogComponent, dialogConfig)
      .afterClosed()
      .subscribe((result) => {
        console.log('selected::::', result);
        if (result.length > 0) {
          // this.PAN_TAN.setValue(Utils.buildUserFullName(result[0].objectRef));
          // this.GSTNo.setValue(result[0].key);
          // this.Billing.enable();
          this.AuthorisedPerson.setValue(result[0].key);
          this.AuthorisedPersonName.setValue(result[0].value);
          this.formGroup.markAsDirty();
        }
      });
  }

  public toUpperCase(control: AbstractControl) {
    control.setValue(control.value.toUpperCase());
  }

  public resetUser() {
    // this.GSTNo.reset();
    // this.PAN_TAN.reset();
    // this.Billing.setValue(false);
    // this.Billing.disable();
    this.AuthorisedPerson.setValue('');
    this.AuthorisedPersonName.setValue('');
    this.formGroup.markAsDirty();
  }
}
