import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, Subscription, interval, skip } from 'rxjs';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';
import { SpinnerService } from 'services/spinner.service';
import { AppCurrencyPipe } from 'shared/formatters/app-currency.pipe';
import { AppUnitPipe } from 'shared/formatters/app-unit.pipe';
import { Constants } from 'utils/Constants';

import { AuthorizationService } from '../../services/authorization.service';
import { WindowService } from '../../services/window.service';
import { AbstractTabComponent } from '../../shared/component/abstract-tab/abstract-tab.component';
import {
  ActionResponse,
  DashboardDataResult,
  SiteAreaDataResult,
  SiteDataResult,
} from '../../types/DataResult';
import { Utils } from '../../utils/Utils';
// import moment from 'moment';
import * as moment from 'moment-timezone';

@Component({
  selector: 'app-dashboard',
  templateUrl: '/dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent extends AbstractTabComponent {
  public chargingStations: any = [];
  public selectedDropdownOption = '';
  availableSites: { id: string; name: string }[] = [];
  availableSiteAreas: { id: string; name: string }[] = [];
  selectedSites: { id: string; name: string }[] = [];
  selectedSiteAreas: { id: string; name: string }[] = [];
  public siteID!: string;

  @ViewChild('mapContainer', { static: false }) gmap: ElementRef;
  map: google.maps.Map;
  markers: google.maps.Marker[] = [];
  lastOpenedInfoWindow: any;
  dashboardData: any = {};
  sitesAuthorizations: {
    canListCompanies: boolean;
    canCreate: boolean;
    projectFields: string[];
    metadata: Record<string, import('types/Authorization').AuthorizationDefinitionFieldMetadata>;
  };
  createAction: any;
  companyFilter: any;
  siteAreasAuthorizations: {
    canCreate: boolean;
    projectFields: string[];
    metadata: Record<string, import('types/Authorization').AuthorizationDefinitionFieldMetadata>;
  };
  startDateTime: any;
  endDateTime: any;

  public constructor(
    private authorizationService: AuthorizationService,
    activatedRoute: ActivatedRoute,
    private centralServerService: CentralServerService,
    windowService: WindowService,
    private spinnerService: SpinnerService,
    private messageService: MessageService,
    private appUnitPipe: AppUnitPipe,
    private appCurrencyPipe: AppCurrencyPipe,
    private router: Router
  ) {
    super(activatedRoute, windowService, ['dashboard']);
  }

  public ngOnInit() {
    this.loadSites();
    this.loadSiteAreas();
    this.loadDashboardData();
    this.loadDashboardChargingStations();
  }

  ngAfterViewInit() {
    this.initGoogleMap();
  }

  initGoogleMap() {
    let lat = 17.385044;
    let lng = 78.486671;
    navigator.geolocation.getCurrentPosition(
      (result) => {
        lat = result.coords.latitude;
        lng = result.coords.longitude;
        let mapProperties = {
          center: new google.maps.LatLng(lat, lng),
          zoom: 5.2,
          mapTypeId: 'roadmap',
          // icon: '../../assets/icon/marker.svg',
          icon: '../../assets/icon/marker.png',
        };
        this.map = new google.maps.Map(this.gmap.nativeElement, mapProperties);
      },
      (error) => {
        let mapProperties = {
          center: new google.maps.LatLng(lat, lng),
          zoom: 5.2,
          mapTypeId: 'roadmap',
          // icon: '../../assets/icon/marker.svg',
          icon: '../../assets/icon/marker.png',
        };
        this.map = new google.maps.Map(this.gmap.nativeElement, mapProperties);
      }
    );
  }

  public applyFilter(item) {
    console.log('item:', item);
    this.loadDashboardData();
    this.loadDashboardChargingStations();
  }
  public applySiteFilter(item) {
    console.log('Selected Site:', item);
    this.selectedSites = item.value;
    this.loadDashboardData();
    this.loadDashboardChargingStations();
  }

  public applySiteAreaFilter(item) {
    console.log('Selected Site Area:', item);
    this.selectedSiteAreas = item.value;
    this.loadDashboardData();
    this.loadDashboardChargingStations();
  }

  public loadDashboardData() {
    let params: any = {};
    params['Issuer'] = true;

    // day
    params['todayStartDateTime'] = params['todayStartDateTime'] = `${moment()
      .startOf('day')
      .tz('Europe/London')
      .format('YYYY-MM-DDTHH:mm:ss')}.000Z`;
    params['todayEndDateTime'] = `${moment()
      .endOf('day')

      .tz('Europe/London')
      .format('YYYY-MM-DDTHH:mm:ss')}.999Z`;

    // month
    params['monthStartDateTime'] = `${moment()
      .startOf('month')
      .tz('Europe/London')
      .format('YYYY-MM-DDTHH:mm:ss')}.000Z`;
    params['monthEndDateTime'] = `${moment()
      .endOf('month')
      .tz('Europe/London')
      .format('YYYY-MM-DDTHH:mm:ss')}.999Z`;

    // year
    params['yearStartDateTime'] = `${moment()
      .startOf('year')
      .tz('Europe/London')
      .format('YYYY-MM-DDTHH:mm:ss')}.000Z`;
    params['yearEndDateTime'] = `${moment()
      .endOf('year')
      .tz('Europe/London')
      .format('YYYY-MM-DDTHH:mm:ss')}.999Z`;

    params['WithCompany'] = true;
    params['WithSite'] = true;
    params['WithSiteArea'] = true;
    params['WithTag'] = true;
    params['WithUser'] = true;
    params['WithCar'] = true;
    params['Statistics'] = 'history';
    params['Limit'] = 50;
    params['SortFields'] = '-timestamp';
    if (this.selectedDropdownOption !== '') {
      params['Issuer'] = this.selectedDropdownOption;
    }
    if (Array.isArray(this.selectedSites) && this.selectedSites.length > 0) {
      params['SiteID'] = this.selectedSites.map((site) => site.id).join('|');
    }
    if (Array.isArray(this.selectedSiteAreas) && this.selectedSiteAreas.length > 0) {
      params['SiteAreaID'] = this.selectedSiteAreas.map((siteArea) => siteArea.id).join('|');
    }
    this.centralServerService.getDashboardData(params).subscribe({
      next: (result: any) => {
        this.dashboardData = result;

        // Define formatter functions for energy and revenue
        const energyFormatter = (energy: number) => this.roundAndTransformEnergy(energy);
        const revenueFormatter = (revenue: number, priceUnit: string) =>
          this.appCurrencyPipe.transform(Math.round(revenue), priceUnit);

        // Apply the formatter functions
        this.dashboardData.energyToday = energyFormatter(this.dashboardData?.energyToday);
        this.dashboardData.energyCurrentMonth = energyFormatter(
          this.dashboardData?.energyCurrentMonth
        );
        this.dashboardData.energyCurrentYear = energyFormatter(
          this.dashboardData?.energyCurrentYear
        );

        const priceUnit = this.dashboardData.priceUnit; // Assuming priceUnit is available
        this.dashboardData.revenueCurrentDay = revenueFormatter(
          this.dashboardData?.revenueCurrentDay,
          priceUnit
        );
        this.dashboardData.revenueCurrentMonth = revenueFormatter(
          this.dashboardData?.revenueCurrentMonth,
          priceUnit
        );
        this.dashboardData.revenueCurrentYear = revenueFormatter(
          this.dashboardData?.revenueCurrentYear,
          priceUnit
        );
      },
      error: (error) => {
        Utils.handleError(
          JSON.stringify(error),
          this.messageService,
          'dashboard.error_in_getting_dashboard_data'
        );
      },
    });
  }

  // Helper method to convert and round energy values
  private roundAndTransformEnergy(energy: number): string {
    const energyInKWh = energy / 1000; // Convert Wh to kWh
    const roundedEnergy = Math.round(energyInKWh); // Round to nearest whole number
    // return this.appUnitPipe.transform(roundedEnergy, 'kWh', 'kWh');
    return `${roundedEnergy} kWh`;
  }

  public loadDashboardChargingStations() {
    this.spinnerService.show();
    let params = {
      WithSite: 'true',
      WithSiteArea: 'true',
      WithUser: 'true',
    };
    if (this.selectedDropdownOption !== '') {
      params['Issuer'] = this.selectedDropdownOption;
    }
    let limit = { limit: 500, skip: 0 };
    this.centralServerService
      .getDashboardChargingStationsToDisplayOverMap(params, limit)
      .subscribe({
        next: (chargingStations) => {
          this.spinnerService.hide();
          this.removeAllMarkers();

          let index = 0;
          let zoom = 5; //10;
          setTimeout(() => {
            for (const location of chargingStations.result) {
              if (!index) {
                // for reset map location as per first charger location
                this.loadMarkersOnMap(location, index, zoom);
              } else {
                this.loadMarkersOnMap(location, index);
              }

              index++;
            }
          }, 5000);
        },
        error: (error) => {
          this.spinnerService.hide();
          Utils.handleError(
            JSON.stringify(error),
            this.messageService,
            'dashboard.error_in_getting_charing_stations'
          );
        },
      });
  }

  removeAllMarkers() {
    this.markers.forEach((marker) => {
      marker.setMap(null);
    });
    this.markers = [];
  }

  // onMapLoad(location: any) {
  //   this.removeAllMarkers();
  //   let index = 0;
  //   let zoom = 5; //10;
  //   this.loadMarkersOnMap(location, index, zoom);
  // }

  loadMarkersOnMap(location: any, index: any, mapZoom?: any) {
    if (mapZoom) {
      let mapProperties = {
        center: new google.maps.LatLng(
          // location?.siteArea?.address?.coordinates[1],
          // location?.siteArea?.address?.coordinates[0]
          location?.coordinates[1],
          location?.coordinates[0]
        ),
        zoom: mapZoom,
        mapTypeId: 'roadmap',
        // icon: '../../assets/icon/marker.svg',
        icon: '../../assets/icon/marker.png',
      };
      this.map = new google.maps.Map(this.gmap.nativeElement, mapProperties);
    }
    let marker = new google.maps.Marker({
      position: new google.maps.LatLng(
        location?.coordinates[1],
        location?.coordinates[0]
        // location?.siteArea?.address?.coordinates[1],
        // location?.siteArea?.address?.coordinates[0]
      ),
      map: this.map,
      // icon: '../../assets/icon/marker.svg',
      icon: '../../assets/icon/marker.png',
      title: location?.siteArea?.name,
    });

    marker['customInfo'] = location;

    const contentString = this.getInfoWindowContent(location);
    const infowindow = new google.maps.InfoWindow({
      content: contentString,
    });
    marker.addListener('click', () => {
      if (this.lastOpenedInfoWindow) {
        this.lastOpenedInfoWindow.close();
      }
      infowindow.open({
        anchor: marker,
        map: this.map,
        shouldFocus: false,
      });
      this.lastOpenedInfoWindow = infowindow;
      google.maps.event.addListenerOnce(infowindow, 'domready', () => {
        document.getElementById('content' + index).addEventListener('click', () => {
          infowindow.close();
        });
      });
    });

    this.markers.push(marker);
  }

  getInfoWindowContent(location: any) {
    let completeAddress = `${location?.siteArea?.address?.address1}
    ${location?.siteArea?.address?.address2}
    ${location?.siteArea?.address?.city}
    ${location?.siteArea?.address?.region}
    ${location?.siteArea?.address?.postalCode}`;
    const contentString = `
          <table style="width:100%">
            <tr>
              <td><strong>Name: </strong></td>
              <td>${location.siteArea?.name}</td>
            </tr>
            <tr>
              <td><strong>Vendor: </strong></td>
              <td>${location?.chargePointVendor}</td>
            </tr>
            <tr>
              <td><strong>Type: </strong></td>
              <td>${location.public ? 'PUBLIC' : 'PRIVATE'}</td>
            </tr>
            <tr>
              <td><strong>City: </strong></td>
              <td>${location?.siteArea?.address?.city}</td>
            </tr>
            <tr>
              <td colspan="2">${completeAddress}</td>
            </tr>
          </table>
          `;
    return contentString;
  }

  public loadSites() {
    const params: any = {
      Issuer: true,
      Limit: 50,
      SortFields: 'name',
    };

    // Make the API call
    this.centralServerService.getSites(params).subscribe({
      next: (data: SiteDataResult) => {
        this.availableSites = data.result;
      },
      error: (error) => {
        Utils.handleError(JSON.stringify(error), this.messageService, 'general.error_get_sites');
      },
    });
  }

  public loadSiteAreas() {
    const params: any = {
      Issuer: true,
      WithSite: true,
      Limit: 50,
      SortFields: 'name',
    };

    // Make the API call
    this.centralServerService.getSiteAreas(params).subscribe({
      next: (data: SiteAreaDataResult) => {
        this.availableSiteAreas = data.result;
      },
      error: (error) => {
        Utils.handleError(
          JSON.stringify(error),
          this.messageService,
          'general.error_get_site_areas'
        );
      },
    });
  }

  goto(url, fragment = null) {
    this.router.navigate([url], { fragment: fragment });
  }
}
