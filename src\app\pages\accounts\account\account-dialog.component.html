
<div class="main-content vw-90 vh-90 billing-account-settings-dialog-size">
  <div class="card card-profile card-testimonial">
    <mat-tab-group
      animationDuration="0ms"
      disableRipple="true"
      class="mat-tab-info"
      [class]="dialogRef ? 'mat-tabs-with-actions' : 'mat-tabs-with-close-action'"
    >
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>business</mat-icon>
          <span>{{ "accounts.list.add_account" | translate }}</span>
        </ng-template>
        <div class="card-body">
          <div class="tab-content">
            <div class="tab-pane active" id="profile">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <mat-form-field>
                      <input
                        matInput
                        type="text"
                        placeholder="{{ 'companies.title' | translate }}"
                        [formControl]="companyName"
                      />
                    </mat-form-field>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <mat-form-field>
                      <input
                        matInput
                        type="text"
                        readonly="true"
                        placeholder="{{ 'users.title' | translate }}"
                        class="form-field-popup"
                        (click)="assignUser()"
                        [formControl]="user"
                      />
                      <button
                        *ngIf="user.enabled && user.value"
                        mat-icon-button
                        matSuffix
                        (click)="resetUser()"
                        aria-label="Clear"
                      >
                        <mat-icon>clear</mat-icon>
                      </button>
                      <mat-error *ngIf="user.errors?.required">
                        {{ "general.mandatory_field" | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>
              <div class="clearfix"></div>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
    <div [class]="dialogRef ? 'tabs-actions' : 'tabs-actions-embedded'">
      <button
        mat-icon-button
        (click)="save(formGroup.value)"
        title="{{ 'general.save' | translate }}"
        [disabled]="!formGroup.valid || !formGroup.dirty"
      >
        <mat-icon>save</mat-icon>
      </button>
      <button mat-icon-button (click)="close()" title="{{ 'general.close' | translate }}">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
