import { AfterViewInit, Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CorpAuthorizations, DialogMode, DialogParamsWithAuth } from 'types/Authorization';
import { Corporate } from 'types/Corporate';

import { Utils } from '../../../utils/Utils';
import { CorpComponent } from './corp.component';

@Component({
  template:
    '<app-corp #appRef [currentCorpID]="corpID" [dialogMode]="dialogMode" [corpAuthorizations]="corpAuthorizations" [dialogRef]="dialogRef"></app-corp>',
})
export class CorpDialogComponent implements AfterViewInit {
  @ViewChild('appRef') public appRef!: CorpComponent;
  public corpID!: string;
  //public tagVisualID!: string;
  // public corpPersonsList!: any;
  public dialogMode!: DialogMode;
  public corpAuthorizations!: CorpAuthorizations;

  public constructor(
    public dialogRef: MatDialogRef<CorpDialogComponent>,
    @Inject(MAT_DIALOG_DATA) dialogParams: DialogParamsWithAuth<Corporate, CorpAuthorizations>
  ) {
    console.log('dialogParams.dialogData:::::', dialogParams.dialogData);
    this.corpID = dialogParams.dialogData?.id;
    this.dialogMode = dialogParams.dialogMode;
    this.corpAuthorizations = dialogParams.authorizations;
    // this.corpPersonsList = dialogParams.corpPersonsList;
  }

  public ngAfterViewInit() {
    // Register key event
    Utils.registerSaveCloseKeyEvents(
      this.dialogRef,
      this.appRef.formGroup,
      this.appRef.saveCorp.bind(this.appRef),
      this.appRef.close.bind(this.appRef)
    );
  }
}
