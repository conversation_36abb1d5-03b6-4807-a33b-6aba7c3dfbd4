<form class="form" [formGroup]="formGroup" *ngIf="isLoaded">
  <div class="row">
    <!-- Name Field -->
    <div class="col-md-12">
      <div class="form-group">
        <mat-form-field>
          <mat-label>{{ "users.name" | translate }}</mat-label>
          <input
            formControlName="name"
            appAutofocus
            id="name-field"
            matInput
            type="text"
            required
            (input)="capitalizeFirstLetter($event)"
          />
          <mat-error *ngIf="name.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
          <mat-error *ngIf="name.errors?.maxlength">
            <div
              [translateParams]="{ length: name.errors?.maxlength?.requiredLength }"
              [translate]="'general.error_max_length'"
            ></div>
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Email Field -->
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <mat-label>{{ "users.email" | translate }}</mat-label>
          <input formControlName="email" matInput type="email" />
        </mat-form-field>
      </div>
    </div>

    <!-- Contact/Mobile Field -->
    <div class="col-md-6" style="flex: 2">
      <div class="form-group">
        <mat-form-field>
          <mat-label>{{ "users.mobile" | translate }}</mat-label>
          <input
            formControlName="contact"
            matInput
            placeholder="{{ 'users.mobile' | translate }}"
            type="text"
          />
          <!-- readonly -->
          <mat-error *ngIf="contact.errors?.pattern">
            {{ "users.invalid_phone_number" | translate }}
          </mat-error>
          <mat-error *ngIf="contact.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Identification Type Field -->
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <mat-label>Identification Type</mat-label>
          <mat-select formControlName="identification_type" required>
            <mat-option value="pan">PAN</mat-option>
            <mat-option value="voter_id">Voter ID</mat-option>
            <mat-option value="passport">Passport</mat-option>
            <mat-option value="driving_licence">Driving Licence</mat-option>
          </mat-select>
          <mat-error *ngIf="identification_type.errors?.required">
            Identification Type is required
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Identification ID Field -->
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <mat-label>Identification ID</mat-label>
          <input
            formControlName="identification_id"
            matInput
            placeholder="Identification ID"
            required
            type="text"
            pattern="[A-Z0-9]+"
          />
          <mat-error *ngIf="identification_id.errors?.required">
            Identification ID is required
          </mat-error>
          <mat-error *ngIf="identification_id.errors?.pattern">
            Identification ID must be a combination of numbers and capital letters
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Date of Birth Field -->
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <mat-label>Date of Birth</mat-label>
          <input
            formControlName="date_of_birth"
            matInput
            placeholder="Date of Birth"
            required
            type="date"
            [min]="maxDate"
            [max]="minDate"
          />
          <mat-error *ngIf="date_of_birth.errors?.required">Date of Birth is required</mat-error>
          <mat-error *ngIf="date_of_birth.errors?.ageRange"
            >Age shuld be between 18 to 99 years</mat-error
          >
        </mat-form-field>
      </div>
    </div>

    <!-- Save Button -->
    <div class="col-md-12">
      <button mat-raised-button color="primary" (click)="save()">Save</button>
    </div>
  </div>
</form>
