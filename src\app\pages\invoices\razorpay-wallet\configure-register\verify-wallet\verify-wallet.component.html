<form class="form" [formGroup]="formGroup">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <input
            appAutofocus
            [formControl]="otp"
            matInput
            placeholder="OTP"
            required
            minlength="4"
            maxlength="6"
            type="text"
          />
          <mat-error *ngIf="otp.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-3" style="margin-top: 50px">
      <button mat-raised-button color="primary" (click)="verifyOTP()">Verify OTP</button>
    </div>

    <div class="col-md-3" style="margin-top: 50px">
      <span
        style="color: #1976d2; text-decoration: underline; cursor: pointer"
        (click)="resendOTP()"
        >Resend OTP</span
      >
    </div>
  </div>
</form>
