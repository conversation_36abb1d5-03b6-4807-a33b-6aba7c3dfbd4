import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { StatusCodes } from 'http-status-codes';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { UserStatus } from 'types/User';

import { CentralServerService } from '../../services/central-server.service';
import { ConfigService } from '../../services/config.service';
import { MessageService } from '../../services/message.service';
import { SpinnerService } from '../../services/spinner.service';
import { WindowService } from '../../services/window.service';
import { VerifyEmailResponse } from '../../types/DataResult';
import { RestResponse } from '../../types/GlobalType';
import { HTTPError } from '../../types/HTTPError';
import { Constants } from '../../utils/Constants';
import { Utils } from '../../utils/Utils';

@Component({
  selector: 'app-authentication-verify-otp',
  templateUrl: 'verify-otp.component.html',
})
export class VerifyOtpComponent implements OnInit, OnDestroy {
  public otp: AbstractControl;
  public mobile: string | null;
  public countryCode: string | null;
  public formGroup: UntypedFormGroup;
  public verifyEmailAction!: boolean;
  public verificationToken: string | null;
  public resetToken: string | null;
  public verificationOTP: number | null;

  public tenantLogo = Constants.NO_IMAGE;

  private messages!: Record<string, string>;

  private siteKey: string;
  private subDomain: string;

  public constructor(
    private centralServerService: CentralServerService,
    private router: Router,
    private route: ActivatedRoute,
    private spinnerService: SpinnerService,
    private messageService: MessageService,
    private windowService: WindowService,
    private translateService: TranslateService,
    private reCaptchaV3Service: ReCaptchaV3Service,
    private configService: ConfigService
  ) {
    // Load the translated messages
    this.translateService.get('authentication', {}).subscribe((messages) => {
      this.messages = messages;
    });
    // Get the Site Key
    this.siteKey = this.configService.getUser().captchaSiteKey;
    // Init Form
    this.formGroup = new UntypedFormGroup({
      otp: new UntypedFormControl(
        '',
        Validators.compose([Validators.required, Validators.minLength(6), Validators.maxLength(6)])
      ),
    });
    // Form
    this.otp = this.formGroup.controls['otp'];
    this.mobile = this.route.snapshot.queryParamMap.get('mobile');
    this.countryCode = this.route.snapshot.queryParamMap.get('countryCode');
    // Keep the sub-domain
    this.subDomain = this.windowService.getSubdomain();
    // Handle Deep Linking
    if (Utils.isInMobileApp(this.subDomain)) {
      // Forward to Mobile App
      const mobileAppURL: string = Utils.buildMobileAppDeepLink(
        `verifyAccount/${this.windowService.getSubdomain()}/${this.verificationOTP}/${
          this.verificationToken
        }/${this.resetToken}`
      );
      // ACHTUNG ! hack for email bug sent 800 times - need to find a
      // window.location.href = mobileAppURL;
    }
    setTimeout(() => {
      const card = document.getElementsByClassName('card')[0];
      // After 700 ms we add the class animated to the login/register card
      card.classList.remove('card-hidden');
    }, 700);
  }

  public ngOnInit() {
    const body = document.getElementsByTagName('body')[0];
    body.classList.add('lock-page');
    body.classList.add('off-canvas-sidebar');
    if (this.subDomain) {
      // Retrieve tenant's logo
      this.centralServerService.getTenantLogoBySubdomain(this.subDomain).subscribe({
        next: (tenantLogo: string) => {
          if (tenantLogo) {
            this.tenantLogo = tenantLogo;
          }
        },
        error: (error) => {
          this.spinnerService.hide();
          switch (error.status) {
            case StatusCodes.NOT_FOUND:
              this.tenantLogo = Constants.NO_IMAGE;
              break;
            default:
              Utils.handleHttpError(
                error,
                this.router,
                this.messageService,
                this.centralServerService,
                'general.unexpected_error_backend'
              );
          }
        },
      });
    } else {
      this.tenantLogo = Constants.MASTER_TENANT_LOGO;
    }
  }

  public ngOnDestroy() {
    const body = document.getElementsByTagName('body')[0];
    body.classList.remove('lock-page');
    body.classList.remove('off-canvas-sidebar');
  }

  public OTPVerify(data: any) {
    // this.reCaptchaV3Service.execute(this.siteKey, 'ActivateAccount', (token) => {
    //   if (token) {
    //     data['captcha'] = token;
    //   } else {
    //     this.messageService.showErrorMessage(this.messages['invalid_captcha_token']);
    //     return;
    //   }
    data['mobile'] = this.mobile;
    data['countryCode'] = this.countryCode;
    data['acceptEula'] = true;
    console.log(data);
    this.spinnerService.show();
    // Resend
    this.centralServerService.verifyOtp(data).subscribe({
      next: (response) => {
        this.spinnerService.hide();
        this.centralServerService.loginSucceeded(response.token);
        this.messageService.showSuccessMessage(this.messages['otp_verify_success']);
        this.router.navigate(['/charging-stations#alls']);
      },
      error: (error) => {
        this.spinnerService.hide();
        switch (error.status) {
          case HTTPError.INVALID_OTP:
            this.messageService.showInfoMessage(this.messages['wrong_otp']);
            break;
          default:
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'authentication.otp_verify_failed'
            );
            break;
        }
      },
    });
    //void this.router.navigate(['/auth/login']);
    //});
  }
}
