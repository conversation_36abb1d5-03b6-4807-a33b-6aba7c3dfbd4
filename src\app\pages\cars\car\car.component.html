<div class="main-content vw-90 vh-90 car-dialog-size">
  <div class="card card-profile card-testimonial">
    <mat-tab-group animationDuration="0ms" disableRipple="true" class="mat-tab-info" [class]="dialogRef ? 'mat-tabs-with-actions' : 'mat-tabs-with-close-action'">
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>portrait</mat-icon>
          <span>{{'car.title' | translate}} - {{formGroup.controls['carCatalog'].value}}</span>
        </ng-template>
        <div class="card-body mat-tab-dialog-body-content">
          <app-car-main #carMainComponent [formGroup]="formGroup" [car]="car" [carsAuthorizations]="carsAuthorizations" [readOnly]="readOnly"></app-car-main>
        </div>
      </mat-tab>
      <mat-tab *ngIf="isCarConnectorComponentActive" >
        <ng-template mat-tab-label>
          <mat-icon>bluetooth_drive</mat-icon>
          <span>{{"car_connector.title"|translate}}</span>
        </ng-template>
        <div class="card-body mat-tab-dialog-body-content">
          <app-car-connector #carConnectorComponent [formGroup]="formGroup" [car]="car" [carsAuthorizations]="carsAuthorizations" [readOnly]="readOnly"></app-car-connector>
        </div>
      </mat-tab>
    </mat-tab-group>
    <div class="tabs-actions">
      <button mat-icon-button *ngIf="!readOnly" (click)="saveCar(formGroup.getRawValue())" title="{{'general.save' | translate}}"
          [disabled]="!formGroup.valid || !formGroup.dirty" >
        <mat-icon>save</mat-icon>
      </button>
      <button mat-icon-button *ngIf="dialogRef" (click)="close()" title="{{'general.close' | translate}}">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
