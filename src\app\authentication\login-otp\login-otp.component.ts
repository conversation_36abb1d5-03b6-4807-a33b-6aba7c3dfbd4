import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { StatusCodes } from 'http-status-codes';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { UserStatus } from 'types/User';

import { CentralServerService } from '../../services/central-server.service';
import { ConfigService } from '../../services/config.service';
import { MessageService } from '../../services/message.service';
import { SpinnerService } from '../../services/spinner.service';
import { WindowService } from '../../services/window.service';
import { RestResponse } from '../../types/GlobalType';
import { HTTPError } from '../../types/HTTPError';
import { Constants } from '../../utils/Constants';
import { Users } from '../../utils/Users';
import { Utils } from '../../utils/Utils';

@Component({
  selector: 'app-authentication-login-otp',
  templateUrl: 'login-otp.component.html',
})
export class LoginOTPComponent implements OnInit, OnDestroy {
  public mobile: AbstractControl;
  public formGroup: UntypedFormGroup;
  public verifyEmailAction!: boolean;
  public verificationToken: string | null;
  public resetToken: string | null;
  public verificationPhno: number | null;
  public country: { label: string; value: string } | null;

  public tenantLogo = Constants.NO_IMAGE;

  private messages!: Record<string, string>;

  private siteKey: string;
  private subDomain: string;
  private countryCode: AbstractControl<string, string>;
  public countryCodes: { label: string; value: string }[];

  public constructor(
    private centralServerService: CentralServerService,
    private router: Router,
    private route: ActivatedRoute,
    private spinnerService: SpinnerService,
    private messageService: MessageService,
    private windowService: WindowService,
    private translateService: TranslateService,
    private reCaptchaV3Service: ReCaptchaV3Service,
    private configService: ConfigService
  ) {
    // Load the translated messages
    this.translateService.get('authentication', {}).subscribe((messages) => {
      this.messages = messages;
    });
    this.countryCodes = Constants.COUNTRY_CODES;
    // Get the Site Key
    this.siteKey = this.configService.getUser().captchaSiteKey;
    // Init Form
    this.formGroup = new UntypedFormGroup({
      countryCode: new UntypedFormControl('+91', Validators.compose([Validators.required])),
      mobile: new UntypedFormControl(
        '',
        Validators.compose([Validators.required, Users.validatePhone])
      ),
    });
    // Form
    this.mobile = this.formGroup.controls['mobile'];
    this.countryCode = this.formGroup.controls['countryCode'];
    // Keep the sub-domain
    this.subDomain = this.windowService.getSubdomain();

    setTimeout(() => {
      const card = document.getElementsByClassName('card')[0];
      // After 700 ms we add the class animated to the login/register card
      card.classList.remove('card-hidden');
    }, 700);
  }

  public ngOnInit() {
    const body = document.getElementsByTagName('body')[0];
    body.classList.add('lock-page');
    body.classList.add('off-canvas-sidebar');

    if (this.subDomain) {
      // Retrieve tenant's logo
      this.centralServerService.getTenantLogoBySubdomain(this.subDomain).subscribe({
        next: (tenantLogo: string) => {
          if (tenantLogo) {
            this.tenantLogo = tenantLogo;
          }
        },
        error: (error) => {
          this.spinnerService.hide();
          switch (error.status) {
            case StatusCodes.NOT_FOUND:
              this.tenantLogo = Constants.NO_IMAGE;
              break;
            default:
              Utils.handleHttpError(
                error,
                this.router,
                this.messageService,
                this.centralServerService,
                'general.unexpected_error_backend'
              );
          }
        },
      });
    } else {
      this.tenantLogo = Constants.MASTER_TENANT_LOGO;
    }
  }

  public ngOnDestroy() {
    const body = document.getElementsByTagName('body')[0];
    body.classList.remove('lock-page');
    body.classList.remove('off-canvas-sidebar');
  }

  public sendOTP(data: any) {
    // this.reCaptchaV3Service.execute(this.siteKey, 'ActivateAccount', (token) => {
    //   if (token) {
    //     data['captcha'] = token;
    //   } else {
    //     this.messageService.showErrorMessage(this.messages['invalid_captcha_token']);
    //     return;
    //   }
    this.spinnerService.show();
    // Resend
    this.centralServerService.loginOtp(data).subscribe({
      next: (response) => {
        this.spinnerService.hide();
        if (response.status && response.status === RestResponse.SUCCESS) {
          this.messageService.showSuccessMessage(this.messages['otp_sent_success']);
          // Go back to login
          void this.router.navigate(['/auth/verify-otp'], {
            queryParams: { mobile: this.mobile.value, countryCode: this.countryCode.value },
          });
        } else {
          Utils.handleError(
            JSON.stringify(response),
            this.messageService,
            this.messages['otp_sent_error']
          );
        }
      },
      error: (error) => {
        this.spinnerService.hide();
        console.log(error);
        switch (error.status) {
          case HTTPError.UNREGISTERED_MOBILE:
            this.messageService.showInfoMessage(this.messages['unregistered_mobile']);
            void this.router.navigate(['/auth/register'], {
              queryParams: { mobile: this.mobile.value },
            });
            break;
          default:
            Utils.handleHttpError(
              error,
              this.router,
              this.messageService,
              this.centralServerService,
              'authentication.otp_sent_error'
            );
            break;
        }
      },
    });
    //});
  }
}
