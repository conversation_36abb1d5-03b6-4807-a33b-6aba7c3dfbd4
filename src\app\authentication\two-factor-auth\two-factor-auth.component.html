<div class="wrapper wrapper-full-page">
  <div class="page-header login-page header-filter" filter-color="black">
    <div class="container">
      <div class="col-md-6 ms-auto me-auto">
        <form class="form" [formGroup]="formGroup" (ngSubmit)="verifyOTP()">
          <div class="card card-login w-75">
            <div class="card-header card-header-primary">
              <h2 class="card-title text-center">{{ "authentication.tfa_title" | translate }}</h2>
            </div>
            <div class="card-body">
              <!-- QR Code Display -->
              <div class="text-center mb-3">
                <img
                  *ngIf="qrcode"
                  [src]="qrcode"
                  alt="QR Code"
                  style="max-width: 100%; height: auto"
                />
                <p>{{ "authentication.mfa_card_subtitle" | translate }}</p>
              </div>
              <!-- OTP Input -->
              <span class="bmd-form-group">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>pin</mat-icon>
                      <mat-form-field class="ms-2">
                        <input
                          appAutofocus
                          matInput
                          type="text"
                          placeholder="OTP"
                          [formControl]="otp"
                          required
                          autocomplete=""
                          [readonly]="false"
                        />
                        <mat-error *ngIf="otp.errors?.otp">{{
                          "users.invalid_phone_number" | translate
                        }}</mat-error>
                        <mat-error *ngIf="otp.errors?.required">{{
                          "general.mandatory_field" | translate
                        }}</mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                </div>
              </span>
              <div class="text-center mt-2">
                <button
                  mat-raised-button
                  color="primary"
                  type="submit"
                  [disabled]="!formGroup.valid"
                >
                {{ "authentication.mfa_card_btn_text" | translate }}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
