{"general": {"generate": "Generate", "captcha_text_1": "Questo sito è protetto da reCAPTCHA e Google", "captcha_text_2": "Politica di Privacy", "captcha_text_3": " e ", "captcha_text_4": "Termini di Servizio", "captcha_text_5": " applica", "not_authorized": "Non sei autorizzato ad eseguire questa azione", "created_on": "Creato Il", "created_by": "<PERSON><PERSON><PERSON>", "changed_on": "Modificato Il", "changed_by": "Modificato Da", "expired_on": "Scaduto Il", "revoked_on": "Revocato Il", "description": "Descrizione", "activate": "<PERSON><PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload_in_progress": "Caricamento in corso, si prega di attendere", "body": "Body", "day": "g", "hour": "h", "minute": "m", "second": "s", "more_records": "<PERSON><PERSON>", "click_here": "clicca qui", "selected_records": "Selezionato", "nbr_of_records": "Record", "connectors": "Prese", "notifications": "Notifiche", "dashboard.title": "Dashboard", "dashboard-transport.title": "Transport Dashboard", "general": "Main", "start_time": "Start Time", "available_date": "Available Date", "charging_station_id": "Charging Station ID", "closing_time": "Closing Time", "expire_date": "Expire Date", "app_name": "Operations Manager", "last_app_name": "(C) CERO SMART MOBILITY", "version": "Versione", "about_us": "A proposito di noi", "unexpected_error_backend": "Si è verificato un errore imprevisto", "select_at_least_one_record": "Devi selezionare almeno un record nella lista", "mandatory_field": "Campo obbligatorio", "no_record_found": "Nessun record trovato", "no_image_found": "<PERSON><PERSON><PERSON> immagine è stata trovata", "no_connector_available": "Nessuna connessione disponibile", "error_max_length": "La lunghezza massima è {{length}}", "error_min_length": "La lunghezza minima è {{length}}", "error_max_value": "Il valore massimo è {{value}}", "error_min_value": "Il valore minimo è {{value}}", "error_url_pattern": "Pattern URL errato", "error_number_pattern": "Numero non valido", "invalid_date": "Data non valida", "error_not_a_number": "Numero non valido", "error_duplicate": "Voce duplicata", "id": "Id", "ok": "Ok", "cancel": "Can<PERSON><PERSON>", "actions": "Azioni", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "Seleziona", "set_filter": "Imposta Filtro", "remove": "<PERSON><PERSON><PERSON><PERSON>", "location": "Posizione", "address": "<PERSON><PERSON><PERSON><PERSON>", "address1": "Indirizzo 1", "address2": "Indirizzo 2", "postal_code": "Codice Postale", "city": "Città", "region": "Regione", "department": "Dipartimento", "country": "<PERSON><PERSON>", "tan_gst": "Tan/Gst", "value": "Value", "latitude": "Latitudine", "longitude": "<PERSON><PERSON><PERSON><PERSON>", "show_place": "Visualizza Luogo", "invalid_value": "Valore non valido", "home": "Home", "details": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON>", "update": "Aggiorna", "refresh": "Refresh", "generate_qr": "Genera il codice QR", "display_qr": "Display QR code", "save": "<PERSON><PERSON>", "delete": "Can<PERSON><PERSON>", "export": "Esporta", "import": "Import", "synchronize": "Sincronizza", "force_synchronize": "Forza sincronizzazione", "download": "Download", "test_connection": "Test Connessione", "upload": "Upload", "change": "Modifica", "reload": "Ricarica", "stop": "Stop", "unlock": "S<PERSON><PERSON>ca", "refund": "<PERSON><PERSON><PERSON><PERSON>", "open": "<PERSON>i", "open_refunding_system": "Vai alle tue Spese", "connect": "<PERSON><PERSON><PERSON>", "register": "Registra", "unregister": "Annulla Registrazione", "close": "<PERSON><PERSON>", "back": "Indietro", "next": "<PERSON><PERSON>", "previous": "Precedente", "info": "Info", "now": "<PERSON>a", "default": "<PERSON><PERSON><PERSON>", "yes": "Si", "no": "No", "apply": "Applica", "clear": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Warning", "success": "Successo", "error": "Errore", "drag_drop": "Trascina & Rilascia", "picture": "<PERSON><PERSON><PERSON><PERSON>", "pictures": "<PERSON><PERSON><PERSON><PERSON>", "change_picture": "Modifica Immagine", "search": "Cerca", "search_place": "Cerca Indirizzo", "use_place": "<PERSON><PERSON>", "check_place": "Controlla", "search_date_from": "Da", "search_date_until": "A", "search_date_range": "Date", "search_charging_station": "Stazione di Ricarica", "search_user": "Utente", "search_site": "<PERSON><PERSON>", "all": "<Tutti>", "max_lines": "Linee Massime", "auto_refresh": "Refresh Automatico", "sort_date_desc": "Ordina Data Decr.", "search_placeholder": "Cerca...", "backend_not_running": "Impossibile raggiungere il server centrale", "error_backend": "Si è verificato un errore contattando il server centrale", "error_backend1": "Please configure this in billing settings ", "car_catalog_error": "Si è verificato un errore durante il recupero del veicolo", "car_catalogs_error": "Si è verificato un errore durante il recupero dei veicoli", "cars_error": "Si è verificato un errore durante il recupero dei veicoli dell'utente", "car_image_error": "Si è verificato un errore durante il recupero delle immagini del veicolo", "success_backend": "La connessione al server centrale è stata ripristinata", "success_import": "File imported successfully", "import_unexpected_error": "Si è verificato un errore durante il recupero dei file, controllare i log", "choose_file": "Scegliere file", "user_or_tenant_updated": "Sei stato disconnesso perchè le impostazioni del tuo profilo o della tua organizzazione sono state aggiornate", "invalid_content": "Contenuto Non Valido", "open_in_maps": "Posizione", "change_pending_title": "Modifiche in Sospeso", "change_pending_text": "Alcune modifiche sono in sospeso.", "change_invalid_pending_title": "Modifiche in Sospeso con valori non validi", "change_invalid_pending_text": "Alcune modifiche con valori invalidi sono in sospeso.", "save_and_close": "Salva & Chiudi", "do_not_save_and_close": "Non Salvare & Chiudi", "set_coordinates": "Imposta", "reset_filters": "Resetta i Filtri", "browser_not_supported": "Questo browser non è supportato! Usa Chrome o Firefox.", "status": "Stato", "revoke": "Revoca", "edit": "Modifica", "import_instructions": "Il file che stai tentando di caricare deve avere le seguenti caratteristiche:", "import_instructions_file_type": "Il tipo di file deve essere CSV", "import_instructions_header_required": "E' richiesta un'intestazione del file CSV", "import_instructions_coma_separator": "La virgola ',' è utilizzata come separatore", "import_instructions_required_fields": "Campo(i) richiesto(i): '{{properties}}'", "import_instructions_optional_fields": "Campo(i) opzionale(i): '{{properties}}'", "invalid_file_error": "Il file non è valido", "invalid_file_csv_header_error": "Almeno uno dei campi obbligatori non è stato aggiunto nel file CSV, l'operazione è stata annullata", "import_already_ongoing": "Impossibile caricare un badge se un caricamento è già in corso", "menu": {"dashboard": "Cruscotto", "dashboard-transport": "Transport Dashboard", "charging_stations": "Stazioni di Ricarica", "transactions": "Sessioni", "active": "Attivo", "pricing": "Pricing", "history": "Storico", "analytics": "<PERSON><PERSON><PERSON>", "consumption": "Consu<PERSON>", "usage": "<PERSON><PERSON>", "inactivity": "Inattività", "organisations": "Organizzazioni", "companies": "Aziende", "sites": "<PERSON><PERSON>", "site_areas": "Aree del <PERSON>o", "users": "<PERSON><PERSON><PERSON>", "transports": "Trasporti", "manufacturers": "Produttori", "vehicles": "<PERSON><PERSON><PERSON><PERSON>", "logs": "Log", "tenants": "Organizzazioni", "revenue": "<PERSON><PERSON>", "carbon-credit": "Carbon Credit", "wallet-report": "Wallet Report", "components": "Componenti", "settings": "Settings", "integration_settings": "Impostazioni di integrazione", "technical_settings": "Impostazioni tecniche", "assets": "Risorse", "organization": "Organizzazione", "template": "Template", "cars": "<PERSON><PERSON><PERSON><PERSON>", "invoices": "Fatture", "tags": "Badges", "charging_station_templates": "Template stazioni di ricarica", "payment-settlement": "Payment Settlement"}, "tooltips": {"push_cdr": "Spingi CDR", "activate": "<PERSON><PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "export": "Esporta parametri OCPP", "assign_site_area": "Assegna sito", "more": "Più azioni", "smart_charging": "Carica Smart", "clear_cache": "Svuota cache", "soft_reset": "Reset", "reboot": "<PERSON><PERSON><PERSON><PERSON>", "more_actions": "Più azioni", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "auto_refresh": "Refresh automatico", "create": "<PERSON><PERSON>", "delete": "Can<PERSON><PERSON>", "edit": "Modifica", "edit_chargers": "Modifica stazione di ricarica", "display_chargers": "Visualizza stazioni di ricarica", "edit_assets": "Modifica risorse", "display_assets": "Visualizza risorse", "assign_site": "Assegna a Sito", "edit_users": "Modifica utente", "edit_siteArea": "Modifica aree del sito", "display_siteAreas": "Visualizza aree del sito", "no_action": "Nessuna azione possibile", "open": "<PERSON>i", "open_in_maps": "Apri in google maps", "refresh": "Refresh", "generate_qr": "Genera il codice QR", "refund": "<PERSON><PERSON><PERSON><PERSON>", "register": "Registrati", "unregister": "Annulla registrazione", "remove": "<PERSON><PERSON><PERSON><PERSON>", "revoke": "Revoca", "settings": "Impostazioni", "synchronize": "Sincronizza", "start": "Avvia", "stop": "Interrompi", "unlock": "S<PERSON><PERSON>ca", "view": "Visualizza", "send": "Invia", "clear": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "download": "Download", "test_connection": "Test Connessione", "force_available_status": "Forza Stato a Disponibile", "force_unavailable_status": "Forza Stato a Non Disponibile", "display_users": "Visualizza utenti", "display_sites": "Visualizza siti", "pricing": "<PERSON>riff<PERSON>"}}, "issuer": {"title": "Proprietario", "local": "Organizzazione Attuale", "foreign": "Organizzazione Esterna"}, "dashboard": {"active_stations_gauge_title": "Punti di Ricarica", "active_stations_gauge_unit": "Occupato", "occupied_charging_points_gauge_footer": "Numero di punti di ricarica occupati", "occupied_charging_points_gauge_title": "Occupato", "all_site": "Tutti i siti", "co2_footer": "CO2 risparmiato dal 2017 basato su un'emissione di 120g/km", "co2_title": "CO2", "consumption_gauge_footer": "Potenza Istantanea erogata in kW", "consumption_gauge_title": "Potenza Istantanea", "consumption_title": "Potenza Istantanea", "gas_footer": "Carburante risparmiato usando un veicolo termico con consumo 8L/100km", "gas_title": "Carburante", "km_footer": "Distanza percorsa da un veicolo elettrico con consumo 18kW.h/100km", "km_title": "Distanza", "location": "Posizione", "metrics": "<PERSON><PERSON><PERSON> at<PERSON>", "realtime_title": "Statistiche di oggi", "realtime": {"button": {"consumption": "Consu<PERSON>", "utilization": "Utilizzazione"}}, "statistics_title": "Storico Consumi", "statistics": {"button": {"day": "<PERSON><PERSON><PERSON>", "month": "Mese", "week": "Set<PERSON><PERSON>", "year": "<PERSON><PERSON>"}}, "today_duration": "Tempo di Ricarica", "today_duration_footer": "Tempo totale di oggi speso per la ricarica", "today_inactivity": "Inattività di Carica", "today_inactivity_footer": "Tempo totale di oggi trascorso dai veicoli che hanno terminato la ricarica ma sono rimasti connessi", "today_energy_delivered": "Energia Consumata", "today_energy_delivered_footer": "Energia totale consumata oggi", "trends_of_day_avg": "Media", "trends_of_day_duration_avg": "Media", "trends_of_day_duration_max": "<PERSON><PERSON>", "trends_of_day_duration_min": "Minimo", "trends_of_day_duration_title": "{{todayDay}} trend", "trends_of_day_inactivity_avg": "Media", "trends_of_day_inactivity_max": "<PERSON><PERSON>", "trends_of_day_inactivity_min": "Minimo", "trends_of_day_inactivity_title": "{{todayDay}} trend", "trends_of_day_max": "<PERSON><PERSON>", "trends_of_day_min": "Minimo", "trends_of_day_title": "{{todayDay}} trend", "tabs": {"dashboard": "Dashboard"}, "flters": {"dropdown": {"all": "All", "current": "Current", "external": "External"}}, "cards": {"charging_stations": "Charging Stations", "connection_loss": "Faults/Connection Loss", "active_users": "Active Users", "active_sessions": "Active Sessions", "charging_sessions_month": "Charging Sessions (Current Month)", "charging_sessions_year": "Charging Sessions (Current Year)", "revenue_today": "Revenue (Today)", "revenue_month": "Revenue (Current Month)", "revenue_year": "Revenue (Current Year)", "energy_today": "Energy (Today)", "energy_month": "Energy (Current Month)", "energy_year": "Energy (Current Year)", "carbon_credit_today": "Carbon Credit (Today)", "carbon_credit_month": "Carbon Credit (Current Month)", "vehicle_charging_now": "Vehicle Charging Now", "vehicle_charging_history": "Vehicle Charging (History)"}}, "companies": {"no_companies": "Nessuna azienda trovata", "company_number": "Azienda(e)", "number_of_sites": "Numero di Siti", "logo": "Logo", "name": "Nome", "company": "Azienda", "title": "Azienda", "titles": "Aziende", "created_on": "Creato Il", "general.value": "Tax Level Value", "taxLevel.value": "TAN/GST", "created_by": "<PERSON><PERSON><PERSON>", "changed_on": "Modificato Il", "changed_by": "Modificato Da", "delete_title": "Can<PERSON><PERSON>", "delete_button": "Can<PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare l'azienda '{{companyName}}'?", "delete_success": "L'azienda '{{companyName}}' è stata cancellata correttamente", "delete_error": "Si è verificato un errore durante la cancellazione dell'azienda", "changed_by_other_user": "L'azienda '{{companyName}}' è stata modificata da un altro utente e verrà ricaricata", "update_success": "L'azienda '{{companyName}}' è stata aggiornata correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento dell'azienda", "company_not_found": "L'azienda non è stata trovata", "create_success": "L'azienda '{{companyName}}' è stata creata correttamente", "create_error": "Si è verificato un errore durante la creazione dell'azienda", "logo_size_error": "Il limite di dimensione del logo è stato superato, il limite è inferiore a {{maxPictureKb}} Kb", "select_companies": "Seleziona l'azienda"}, "assets": {"no_assets": "Nessuna risorsa trovata", "asset_number": "R<PERSON>orsa(e)", "logo": "Logo", "name": "Nome", "asset_type": "Tipo risorsa", "fluctuation_percent": "Fluttuazione a breve termine (%)", "variation_percent": "Variation Threshold (%)", "static_value_watt": "Potenza Statica (W)", "produce": "Produce Energia", "consume": "Consuma Energia", "consume_and_produce": "Consuma e Produce Energia", "dynamic_asset": "Risorsa <PERSON>", "uses_push_api": "Uses Push API", "instant_power": "Potenza Istantanea", "asset_connection": "Connessione Risorsa", "meter_id": "ID Contatore", "asset": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": "Risorse", "created_on": "Creata Il", "created_by": "<PERSON><PERSON><PERSON>", "changed_on": "Modificata Il", "changed_by": "Modificata Da", "delete_title": "Can<PERSON><PERSON>", "delete_button": "Can<PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare la risorsa '{{assetName}}'?", "delete_success": "La risorsa '{{assetName}}' è stata cancellata correttamente", "delete_error": "Si è verificato un errore durante la cancellazione della risorsa", "changed_by_other_user": "La risorsa '{{assetName}}' è stata modificata da un altro utente e verrà ricaricata", "update_success": "La risorsa '{{assetName}}' è stata aggiornata correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento della risorsa", "asset_not_found": "La risorsa non è stata trovata", "asset_settings_error": "Si è verificato un errore durante il recupero delle connessioni della risorsa", "create_success": "La risorsa '{{assetName}}' è stata creata correttamente", "create_error": "Si è verificato un errore durante la creazione della risorsa", "refresh_success": "Il consumo o la produzione della risorsa sono stati recuperati con successo", "refresh_error": "Si è verificato un errore durante il recupero del consumo o la produzione della risorsa, si prega di verificare i logs", "consumption_error": "Si è verificato un errore durante il recupero del consumo", "logo_size_error": "Il limite di dimensione dell'immagine è stato superato, il limite è inferiore a {{maxPictureKb}} Kb", "select_assets": "Seleziona Risorsa", "consumption_date": "Data", "dialog_tabs": {"connection": "Connessione"}, "errors": {"missing_site_area": {"title": "Non assegnata ad un'Area del Sito", "description": "La risorsa deve essere assegnata ad un'area del sito", "action": "Modifica la risorsa e imposta l'area del sito"}}}, "sites": {"assign_company": "Assegna Azienda", "auto_user_site_assignment": "Assegnazione automatica dell'utente a questo sito", "public": "Questo sito è pubblico", "public_site": "Pubblico", "auto_assignment": "Assegnazione Automatica Utente", "user_list": "Utenti autorizzati ad usare la stazione di ricarica su questo sito", "user_list_source_title": "Disponibile", "user_list_target_title": "<PERSON><PERSON><PERSON><PERSON>", "no_sites": "<PERSON><PERSON><PERSON> sito trovato", "select_sites": "Seleziona Sito", "site": "<PERSON><PERSON>", "assigned_users_to_site": "Utenti assegnati a {{siteName}}", "unassigned": "Non Assegnato", "no_connected_chargers": "Non sei connesso a questa stazione di ricarica", "site_number": "<PERSON><PERSON>(i)", "number_of_site_areas": "Numero di Aree del Sito", "number_of_users": "Numero Utenti", "users": "<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "title": "<PERSON><PERSON>", "titles": "<PERSON><PERSON>", "company_invalid": "Azienda non valida", "created_on": "Creato Il", "created_by": "<PERSON><PERSON><PERSON>", "changed_on": "Modificato Il", "changed_by": "Modificato Da", "delete_title": "Can<PERSON><PERSON>", "site_not_found": "Il Sito non è stato trovato", "delete_button": "Can<PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare il sito '{{siteName}}'?", "delete_success": "Il sito '{{siteName}}' è stato cancellato correttamente", "delete_error": "Si è verificato un errore durante la cancellazione del sito", "remove_users_title": "Rimuovi Utente(i)", "remove_users_confirm": "Sei sicuro di voler rimuovere gli(lo) utenti(e) selezionati(o) da questo sito?", "remove_users_success": "Gli utenti sono stati rimossi correttamente", "remove_users_error": "Si è verificato un errore durante la rimozione degli utenti dal sito", "update_users_success": "Gli utenti sono stati aggiunti correttamente", "changed_by_other_user": "Il sito '{{siteName}}' è stato modificato da un altro utente e verrà ricaricato", "update_success": "Il sito '{{siteName}}' è stato aggiornato correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento del sito", "update_users_error": "Si è verificato un errore durante l'assegnazione di un utente al sito", "update_public_site_error": "Il sito '{{siteName}}' non può essere reso privato ed avere stazioni di ricarica pubbliche", "create_success": "Il sito '{{siteName}}' è stato creato correttamente", "create_error": "Si è verificato un errore durante la creazione del sito", "image_size_error": "Il limite di dimensione dell'immagine è stato superato, il limite è inferiore a {{maxPictureKb}} Kb", "admin_role": "Amministratore <PERSON>", "owner_role": "Proprietario del Sito", "update_set_site_admin_success": "L'utente '{{userName}}' è ora amministratore di questo sito", "update_remove_site_admin_success": "L'utente '{{userName}}' non è più amministratore di questo sito", "update_site_users_role_error": "Si è verificato un errore durante l'aggiornamento dei diritti di accesso dell'utente '{{userName}}'", "update_set_site_owner_success": "L'utente '{{userName}}' è ora proprietario in questo sito", "update_remove_site_owner_success": "L'utente '{{userName}}' non è più proprietario in questo sito", "update_site_users_owner_error": "Si è verificato un errore durante il tentativo di concedere all'utente '{{userName}}' lo stato di proprietario del sito", "export_all_params_title": "Esporta parametri OCPP", "export_all_params_confirm": "Vuoi veramente esportare tutti i parametri OCPP di tutte le stazioni di ricarica appartenenti al sito '{{siteName}}' ?", "export_all_params_error": "Si è verificato un errore durante l'esportazione di tutti i parametri OCPP", "display_users": "<PERSON><PERSON> utenti", "private_limitation": "Alcune funzionalità non sono disponibili quando il sito é in modalità privata"}, "site_areas": {"limits": "Limiti", "assign_site": "<PERSON><PERSON><PERSON>", "smart_charging": "Carica Smart", "export_all_ocpp_params": "Esporta Tutti i Parametri OCPP", "edit_assets": "Modifica risorse", "display_assets": "Visualizza risorse", "no_site_areas": "Nessuna area del sito trovata", "site_areas_number": "Areae Del Sito(i)", "show_all_chargers": "Visualizza Tutte le Stazioni di Ricarica", "enable_access_control": "Abilita Controllo Accessi (gli utenti devono autenticarsi con un badge)", "enable_smart_charging": "Abilita Ricarica Smart", "access_control": "Controllo Accessi", "charger_list_source_title": "Disponibile", "charger_list_target_title": "<PERSON><PERSON><PERSON><PERSON>", "number_of_charging_stations": "Numero Caricatori", "site_area_not_found": "L'Area del Sito non è stata trovata", "select_site_areas": "Seleziona Area Sito", "unassigned": "Non Assegnato", "image": "<PERSON><PERSON><PERSON><PERSON>", "assigned_chargers": "Stazioni di Ricarica", "assigned_chargers_to_site_area": "Stazioni di Ricarica assegnate a {{siteAreaName}}", "assigned_assets_to_site_area": "Risorse assegnate a {{siteAreaName}}", "name": "Nome", "title": "Area Sito", "titles": "<PERSON><PERSON>", "parent_site_area": "Area del sito principale", "select_parent_site_area": "Selezionare la zona del sito principale", "site_area_hierarchy_error_title": "Errore nella gerarchia delle zone", "carbon_credit": "Carbon Crdit", "site_area_tree_error": ["Si é verificato un errore nella modifica della gerarchia delle zone.", "Evitare le dipendenze circolari.", "Per più informazioni si prega di controllare i logs."], "site_area_tree_error_site": ["Si é verificato un errore nella modifica della gerarchia delle zone.", "Le sotto-zone non hanno lo stesso sito che quella principale", "Vorresti:", "1 - Aggiornare tutte le zone della gerarchia con il nuovo sito", "2 - Assegnare l'area del sito principale a tutte le sotto-zone", "3 - Cancellare l'area del sito principale dalle sotto-zone"], "site_area_tree_error_site_update": "Aggiornare (1)", "site_area_tree_error_site_attach": "Asse<PERSON><PERSON> (2)", "site_area_tree_error_site_clear": "Cancellare (3)", "site_area_tree_error_smart_charging": ["Si é verificato un errore nella modifica della gerarchia delle zone.", "Una zona deve avere la stessa Ricarica Smart che la principale.", "Vuoi cambiare il valore della Ricarica Smart per tutta la gerarchia delle zone?"], "site_area_tree_error_voltage": ["Si é verificato un errore nella modifica della gerarchia delle zone.", "Una zona deve avere lo stesso voltaggio che la principale"], "site_area_tree_error_number_of_phases": ["Si é verificato un errore nella modifica della gerarchia delle zone.", "Una zona deve avere lo stesso numero di fasi che la principale"], "site_area_tree_error_multiple_actions_not_supported": ["Si é verificato un errore nella modifica della gerarchia delle zone.", "Non sono attualmente supportate più modifiche contemporaneamente (esempio: cambiamento di zona e Ricarica Smart.)", "Si prega di fare una modifica alla volta."], "site_invalid": "Area Sito non Valida", "site_area_does_not_exist": "L'Area del Sito non esiste", "max_limit_kw": "<PERSON>ite <PERSON>", "created_on": "Creato Il", "created_by": "<PERSON><PERSON><PERSON>", "changed_on": "Modificato Il", "changed_by": "Modificato Da", "delete_title": "Can<PERSON><PERSON>", "delete_button": "Can<PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare l'area del sito '{{siteAreaName}}'?", "delete_success": "L'area del sito '{{siteAreaName}}' è stata cancellata correttamente", "delete_error": "Si è verificato un errore durante la cancellazione dell'area del sito", "remove_chargers_title": "Rimuovi Stazione(i) di Ricarica", "remove_chargers_confirm": "Sei sicuro di voler rimuovere la(e) stazione(i) di ricarica selezionata(e) da questa area del sito?", "remove_chargers_success": "La(e) stazione(i) di ricarica è stata rimossa correttamente", "remove_chargers_error": "Si è verificato un errore durante la rimozione della(e) stazione(i) di ricarica dall'area del sito", "update_chargers_success": "La(e) stazione(i) di ricarica è stata aggiunta correttamente", "remove_assets_title": "<PERSON><PERSON><PERSON><PERSON>(e)", "remove_assets_confirm": "Sei sicuro di voler rimuovere la(e) risorsa(e) selezionata(e) da quest'area del sito?", "remove_assets_success": "La(e) risorsa(e) è stata rimossa correttamente", "remove_assets_error": "Si è verificato un errore durante la rimozione della(e) risorsa(e) dall'area del sito", "update_assets_success": "Le risorse sono state aggiunte correttamente", "changed_by_other_user": "L'area del sito '{{siteAreaName}}' è stata modificata da un altro utente e verrà ricaricata", "update_success": "L'area del sito '{{siteAreaName}}' è stata aggiornata correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento dell'area del sito", "update_phase_error": "L'area del sito contiene delle stazioni di ricarica trifase", "create_success": "L'area del sito '{{siteAreaName}}' è stata creata correttamente", "create_error": "Si è verificato un errore durante la creazione dell'area del sito", "image_size_error": "Il limite di dimensione dell'immagine è stato superato, il limite è inferiore a {{maxPictureKb}} Kb", "maximum_energy_watts": "Potenza Massima (W)", "maximum_energy_amps_per_phase": "<PERSON><PERSON> amperaggio per fase (A)", "maximum_energy_amps_total": "Totale amperaggio massimo (A)", "number_of_phases": "Numero di fasi", "export_all_params_title": "Esporta parametri OCCP", "export_all_params_confirm": "Vuoi veramente esportare tutti i parametri OCPP di tutte le stazioni di ricarica appartenenti all'area del sito '{{siteAreaName}}' ?", "export_all_params_error": "Si è verificato un errore durante l'esportazione di tutti i parametri OCPP", "consumption_date": "Data", "single_phased": "Monofase", "three_phased": "Trifase", "redirect": "Vai all'Area del Sito"}, "authentication": {"accept": "Accetto l'", "eula": "Accordo di Licenza con l'Utente Finale", "must_accept_eula": "Devi accettare l'Accordo di Licenza con l'Utente Finale", "no_space_in_password": "Nessuno spazio è consentito all'inizo e alla fine", "title": "Accedi a Open e-Mobility", "register": "Registrati", "signup_link": "Nuovo utente? Registrati!", "signin_link": "Hai già un account? Accedi!", "password": "Password", "invalid_password": "Inserisci una password valida", "repeat_password": "Ripeti la Password", "password_rule": "La password deve contentere almeno 8 caratteri e almeno: uno minuscolo, uno maiuscolo, un numero e un carattere speciale", "password_not_equal": "Le password non sono identiche", "sign_in": "Accedi", "sign_up": "Registrati", "mobile_register": "Mobile Registration", "mobile_number_already_exists": "The mobile number already exists.", "sign_out_short": "EX", "sign_out": "<PERSON><PERSON><PERSON>", "email": "Email", "invalid_email": "Inserisci un indirizzo email valido", "forgot_password": "Password dimenticata?", "email_already_exists": "L'email esiste già", "email_does_not_exist": "L'email non esiste", "cannot_check_email_in_backend": "Non posso verificare questa email nel backend", "wrong_email_or_password": "<PERSON>ail o <PERSON> sbagliata", "technical_user_cannot_login_to_ui": "Un utilizzatore tecnico (API user) non può connettersi usando l'applicazione", "account_suspended": "Il tuo account è sospeso", "account_pending": "Il tuo account è in sospeso, controlla la tua email", "super_user_account_pending": "Il tuo account è in sospeso, controlla la tua email", "account_locked": "Il tuo account è bloccato", "account_blocked": "Il tuo account è sospeso", "account_inactive": "Il tuo account è inattivo", "define_password_title": "Definisci la tua Password", "define_password_button": "<PERSON><PERSON>", "define_password_hash_not_valid": "<PERSON><PERSON> non più valida, richiedi una nuova password", "define_password_success": "La tua password è stata aggiornata correttamente", "define_password_error": "Si è verificato un errore durante la modifica della password", "reset_password_title": "Resetta la tua Password", "reset_password_button": "Resetta", "reset_password_confirm": "Vuoi veramente resettare la tua password?", "reset_password_success": "Se la tua mail è valida, riceverai un link per reinizializzare la tua password", "password_strengh": "Sicurezza Password", "password_one_maj": "A", "password_one_min": "a", "password_one_special": "@", "password_one_number": "0", "password_one_length": ">= 8", "register_user_success": "Il tuo account è stato creato con successo! Controlla la tua email", "register_super_user_success": "Il tuo account è stato creato con successo! Un amministratore verificherà il tuo account e lo attiverà", "register_user_error": "Si è verificato un errore durante la creazione del tuo account", "verify_email_title": "Attivazione Account", "verify_email_already_active": "Il tuo account è già attivo!", "verify_email_success": "Il tuo account è stato attivato correttamente!", "verify_email_success_inactive": "Il tuo account è stato verificato con successo, un amministratore controllerà e attiverà il tuo account.", "verify_email_success_set_password": "Il tuo account è stato attivato, imposta la password per finalizzare la registrazione", "verify_email_token_not_valid": "Il link di attivazione del tuo account non è più valido", "verify_email_email_not_valid": "Questa Email non esiste", "verify_email_error": "Si è verificato un errore durante l'attivazione del tuo account", "verify_email_resend_button": "Invia", "verify_email_resend_confirm": "Il tuo account non è attivo! Vuoi richiedere nuovamente l'email con il link di attivazione?", "verify_email_resend_success": "Il link di attivazione del tuo account è stato inviato correttamente", "verify_email_resend_error": "Si è verificato un errore durante l'invio del link di attivazione dell'account", "verify_email_proceed_with_activation": "Il tuo account è ancora inattivo! Vuoi inviare nuovamente l'email di attivazione?", "verify_email_proceed_with_activation_button": "<PERSON><PERSON><PERSON>", "invalid_captcha_token": "Token captcha non valido", "mercedes_data_usage": "Sap Labs France accede ai dati del tuo veicolo via Mercedes-Benz per fornire un migliore servizio di qualità durante la ricarica del veicolo stesso. SAP Labs France utilizz i dati seguenti: livello di carica della batteria e autonomia del veicolo durante la ricarica. Per permettere l'accesso a tali dati, un consenso deve essere esplicitamente fornito attraverso i prossimi passaggi. Per tutte altre informazioni, si prega di consultare le nostre condizioni d'utilizzazione."}, "statistics": {"title": "Statistiche", "description": "Statistiche della sessione di carica", "charger_kw_h": "kW.h", "carbon_credit_tons": "kg", "legend_select_unselect_all": "Deseleziona/Seleziona Tutto", "graphic_title_consumption_y_axis": "Consumo (kW.h)", "graphic_title_carbon_credit_y_axis": "Consumption (kg)", "graphic_title_usage_y_axis": "Utilizzo (ore)", "graphic_title_inactivity_y_axis": "Inattività (ore)", "graphic_title_transactions_y_axis": "Numero di Sessioni", "graphic_title_pricing_y_axis": "Prezzo delle Sessioni in {{currency}}", "multiple_currencies": "(<PERSON><PERSON>)", "graphic_title_month_x_axis": "Me<PERSON>", "transactions_years": "<PERSON><PERSON>", "total_consumption_year": "Consumo Totale {{year}}", "total_usage_year": "<PERSON><PERSON><PERSON><PERSON> {{year}}", "consumption_per_cs_month_title": "Consumo Mensile Stazioni di Ricarica (Complessivo: {{total}} kW.h)", "consumption_per_cs_year_title": "Consumo Annuale Stazioni di Ricarica: {{total}} kW.h", "consumption_per_cs_timeFrame_title": "Consumo Stazioni di Ricarica nel periodo selezionato (Complessivo: {{total}} kW.h)", "consumption_per_cs_total_title": "Consumo Totale Stazioni di Ricarica: {{total}} kW.h", "consumption_per_user_month_title": "<PERSON><PERSON><PERSON> (Complessivo: {{total}} kW.h)", "consumption_per_user_year_title": "Consumo Annuale Utenti: {{total}} kW.h", "consumption_per_user_timeFrame_title": "Consumo Utenti nel periodo selezionato: {{total}} kW.h", "consumption_per_user_total_title": "Consumo Totale Utenti: {{total}} kW.h", "carbon_credit_per_cs_month_title": "Charging Stations Consumption per Month (Overall: {{total}} kg)", "carbon_credit_per_cs_year_title": "Charging Stations Consumption per Year: {{total}} kg", "carbon_credit_per_cs_timeFrame_title": "Charging Stations Consumption in selected time frame (Overall: {{total}} kg)", "carbon_credit_per_cs_total_title": "Charging Stations Total Consumption: {{total}} kg", "carbon_credit_per_user_month_title": "Users Consumption per Month (Overall: {{total}} kg)", "carbon_credit_per_user_year_title": "Users Consumption per Year: {{total}} kg", "carbon_credit_per_user_timeFrame_title": "Users Consumption in selected time frame: {{total}} kg", "carbon_credit_per_user_total_title": "Users Total Consumption: {{total}} kg", "usage_per_cs_month_title": "Utilizzo Mensile Stazioni di Ricarica (Complessivo: {{total}} ore)", "usage_per_cs_year_title": "Utilizzo Annuale Stazioni di Ricarica: {{total}} ore", "usage_per_cs_timeFrame_title": "Utilizzo Stazioni di Ricarica nel periodo selezionato: {{total}} ore", "usage_per_cs_total_title": "Utilizzo Totale Stazioni di Ricarica: {{total}} ore", "usage_per_user_month_title": "<PERSON><PERSON><PERSON><PERSON> (Complessivo: {{total}} ore)", "usage_per_user_year_title": "Utilizzo Annuale Utenti: {{total}} ore", "usage_per_user_timeFrame_title": "<PERSON><PERSON><PERSON><PERSON> nel periodo selezionato: {{total}} ore", "usage_per_user_total_title": "Utilizzo Totale Utenti: {{total}} ore", "inactivity_per_cs_month_title": "Inattività Mensile Stazioni di Ricarica (Complessiva: {{total}} ore)", "inactivity_per_cs_year_title": "Inattività Annuale Stazioni di Ricarica: {{total}} ore", "inactivity_per_cs_timeFrame_title": "Inattività Stazioni di Ricarica nel periodo selezionato: {{total}} ore", "inactivity_per_cs_total_title": "Inattività Totale Stazioni di Ricarica: {{total}} ore", "inactivity_per_user_month_title": "Inattività Mensile Utenti (Complessiva: {{total}} ore)", "inactivity_per_user_year_title": "Inattività Annuale Utenti: {{total}} ore", "inactivity_per_user_timeFrame_title": "Inattività Utenti nel periodo selezionato: {{total}} ore", "inactivity_per_user_total_title": "Inattività Totale Utenti: {{total}} ore", "transactions_per_cs_month_title": "Sessioni Mensili Stazioni di Ricarica (Complessive: {{total}})", "transactions_per_cs_year_title": "Sessioni Annuali Stazioni di Ricarica: {{total}}", "transactions_per_cs_timeFrame_title": "Sessioni Stazioni di Ricarica nel periodo selezionato: {{total}}", "transactions_per_cs_total_title": "Sessioni Totali Stazioni di Ricarica: {{total}}", "transactions_per_user_month_title": "Sessioni Utente Mensili (Complessive: {{total}})", "transactions_per_user_year_title": "Sessioni Utente Annuali: {{total}}", "transactions_per_user_timeFrame_title": "Sessioni Utente nel periodo selezionato: {{total}}", "transactions_per_user_total_title": "Sessioni Utente Totali: {{total}}", "pricing_per_cs_month_title": "Prezzi Mensili Stazioni di Ricarica (Overall: {{total}})", "pricing_per_cs_year_title": "Prezzi Annuali Stazioni di Ricarica: {{total}}", "pricing_per_cs_timeFrame_title": "Prezzi Stazioni di Ricarica nel periodo selezionato: {{total}}", "pricing_per_cs_total_title": "<PERSON>zzi Totali Stazioni di Ricarica: {{total}}", "pricing_per_user_month_title": "<PERSON>zzi Utente Mensili (Complessivi: {{total}})", "pricing_per_user_year_title": "<PERSON>zzi Utente Annuali: {{total}}", "pricing_per_user_timeFrame_title": "Prezzi Utente nel periodo selezionato: {{total}}", "pricing_per_user_total_title": "<PERSON>zzi Utente Totali: {{total}}", "hours": "ore", "total": "Totale", "others": "<PERSON><PERSON>", "category_label": "Categoria", "dialog": {"consumption": {"export": {"title": "Esporta Da<PERSON>", "confirm": "Vuoi veramente esportare tutti i dati di consumo corrispondenti ai filtri?"}}, "carbon_credit": {"export": {"title": "Export Carbon Credit Data", "confirm": "Do you really want to export all the Carbon Credit data matching the filters?"}}, "usage": {"export": {"title": "Esporta Dati <PERSON>", "confirm": "Vuoi veramente esportare tutti i dati di utilizzo corrispondenti ai filtri?"}}, "inactivity": {"export": {"title": "Esporta Dati Inattività", "confirm": "Vuoi veramente esportare tutti i dati di inattività corrispondenti ai filtri?"}}, "transactions": {"export": {"title": "Esporta Dati Sessioni", "confirm": "Vuoi veramente esportare tutti i dati delle sessioni corrispondenti ai filtri?"}}, "pricing": {"export": {"title": "Esporta <PERSON>", "confirm": "Vuoi veramente esportare tutti i dati dei prezzi corrispondenti ai filtri?"}}}}, "chargers": {"chargers": "Stazioni di Ricarica", "token": "Token", "user": "Utente", "charger_not_found": "La stazione di ricarica non è stata trovata", "vendor": "Fornitore", "model": "<PERSON><PERSON>", "name": "Nome", "inactivity": "Inattività", "heartbeat_title": "Stato", "charger_disconnected": "Disconnesso", "charger_connected": "<PERSON><PERSON><PERSON>", "connectors_title": "Prese", "consumption_title": "Potenza Istantanea", "total_consumption_title": "Consu<PERSON>", "firmware_version": "Versione del Firmware", "firmware_status": "Stato del Firmware", "connector_status": "Stato", "ocpp_version": "Versione OCPP", "ocpp_protocol": "Protocollo OCPP", "ocpp_version_title": "OCPP", "private_url": "URL Privato", "nb_connected_phase": "Numero di Fasi Connesse", "current_type": "<PERSON><PERSON><PERSON>", "phase_assignment": "Assegnazione Fase", "phase_combinations": {"three_phased": {"cs_1_g_1": "RST (L1, L2, L3)", "cs_1_g_2": "STR (L2, L3, L1)", "cs_1_g_3": "TRS (L3, L1, L2)"}, "single_phased": {"cs_1_g_1": "R (L1)", "cs_1_g_2": "S (L2)", "cs_1_g_3": "T (L3)"}}, "cant_charge_in_parallel": "Impossibile caricare in parallelo", "share_power_to_all_connectors": "Condividi la potenza con tutte le prese", "exclude_from_power_limitation": "Impossibile limitare la potenza", "update_public_cs_error": "La stazione di ricarica non può essere resa pubblica su un sito privato", "public": "La stazione di ricarica è pubblica", "public_charger": "Pubblica", "exclude_smart_charging": "<PERSON><PERSON><PERSON><PERSON><PERSON> Car<PERSON>", "force_inactive": "Questa stazione di ricarica è inattiva", "manual_configuration": "Manual configuration", "master_slave": "Master / Slave", "public_url": "URL Pubblico", "current_ip": "Percorso IP Corrente", "select_chargers": "Seleziona la Stazione di Ricarica", "serial_number": "Numero Seriale", "last_reboot": "<PERSON><PERSON><PERSON>", "capabilities": "Funzionalità", "ocpp_standard_params": "Parametri OCPP Standard", "ocpp_vendor_params": "Parametri OCPP Avanzati", "ocpp_advanced_command": "Comandi OCPP Avanzati", "unassigned": "Non assegnato", "unassigned_chargers": "Caricatori", "maximum_energy": "<PERSON><PERSON><PERSON> (Watt)", "maximum_energy_amps": "<PERSON><PERSON><PERSON><PERSON> (Amp)", "efficiency": "Efficienza (%)", "invalid_efficiency": "L'efficienza deve essere un numero positivo non superiore a 100", "connector": "Presa", "charge_point": "Punto di Ricarica", "connector0": "Caricatore", "connector_type": "Tipo", "connector_error_title": "Errore", "connector_info_title": "Informazione", "connector_vendor_error_code_title": "Errore fornitore", "connector_max_power": "<PERSON><PERSON><PERSON> (Watt)", "charger_url": "URL", "charger_param_key": "Chiave", "charger_param_value": "Valore", "title": "Stazione di Ricarica", "titles": "Stazioni di Ricarica", "status_occupied": "Occupata", "status_available": "Disponibile", "status_preparing": "In Preparazione", "status_charging": "In carica", "status_suspendedevse": "EVSE Sospeso", "status_suspendedev": "EV Sospeso", "status_finishing": "Finendo", "status_reserved": "Riservato", "show_active_only": "<PERSON><PERSON><PERSON>", "status_unavailable": "Non disponibile", "status_faulted": "Difettosa", "status_unknown": "Sconosciuta", "status_error_connector_lock_failure": "Errore di Blocco Presa", "status_error_ev_communication_error": "Errore di Comunicazione EV", "status_error_ground_failure": "Mancanza Terra", "status_error_high_temperature": "Temperatura Alta", "status_error_internal_error": "Errore Interno", "status_error_local_list_conflict": "Conflitto Lista Locale", "status_error_none": "<PERSON><PERSON><PERSON> errore", "status_error_other_error": "<PERSON><PERSON> errore", "status_error_over_current_failure": "Errore da Sovracorrente", "status_error_over_voltage": "Sovratensione", "status_error_power_meter_failure": "<PERSON><PERSON><PERSON> Misuratore Po<PERSON>", "status_error_power_switch_failure": "Guasto Interruttore Alimentazione", "status_error_reader_failure": "<PERSON><PERSON><PERSON><PERSON>", "status_error_reset_failure": "Guasto Reset", "status_error_under_voltage": "Sotto tensione", "status_error_weak_signal": "<PERSON><PERSON><PERSON>", "status_error_unknown": "Sconosciuta", "status_firmware_idle": "Inattivo", "status_firmware_downloading": "In Scaricamento", "status_firmware_downloadfailed": "Scaricamento Fallito", "status_firmware_installing": "Installazione in corso", "status_firmware_installationfailed": "Installazione Fallita", "status_firmware_downloaded": "Scaricato", "status_firmware_installed": "Installato", "reboot_title": "Riavvio Stazione di Ricarica", "soft_reset_title": "Reset Stazione di Ricarica", "charger_id_not_found": "L'ID della stazione di ricarica '{{chargerID}}' non è stato trovato", "no_transaction_found": "Nessuna sessione trovata per la Stazione di Ricarica '{{chargerID}}' ", "dialog": {"export": {"title": "Esporta le stazioni di ricarica", "confirm": "Vuoi veramente esportare tutte le stazioni di ricarica corrispondenti ai filtri?", "error": "Si è verificato un errore durante l'esportazione delle stazioni di ricarica"}, "exportConfig": {"title": "Esporta i parametri della stazione di ricarica", "confirm": "Vuoi veramente esportare tutti i parametri corrispondenti ai filtri?"}, "localisation": {"title": "Localizzazione delle stazioni di ricarica"}, "settings": {"fixed_url_for_ocpp": "Da OCPP 1.6 in poi, l'URL è fissato dal server", "callback_url_for_ocpp": "URL callback per inviare la richiesta HTTP alla stazione di ricarica"}, "enable_manual_configuration": {"title": "Abilitare la configurazione manuale", "confirm": "Se la configurazione manuale é abilitata, la stazione di ricarica non sarà più modificata via la configurazione automatica. Desideri procedere?"}, "disable_manual_configuration": {"title": "Disabilitare la configurazione manuale", "confirm": "Se la configurazione manuale é disabilitata, la stazione di ricarica sarà modificata via la configurazione automatica, riavviata e le modifiche fatte manualmente saranno perdute. Desideri procedere?"}, "manual_configuration_error": {"title": "Configurazione Manuale/ Errore template", "confirm": "La configurazione manuale é già disabilitata per questa stazione. Si prega di verificare che la stazione si avvii correttamente é che il template automatico sia applicato."}}, "force_available_status_action": "Imposta come Disponibile", "force_unavailable_status_action": "Imposta come Non Disponibile", "reboot_action": "<PERSON><PERSON><PERSON><PERSON>", "soft_reset_action": "<PERSON><PERSON><PERSON><PERSON>", "clear_cache_action": "<PERSON><PERSON><PERSON><PERSON> cache", "smart_charging_action": "Limitazione di Carica", "more_actions_action": "<PERSON><PERSON>", "assign_sitearea_action": "Assegna", "assign_sitearea_action_tooltip": "Assegna area del sito", "smart_charging": {"slider_power_disabled": "Limite non supportato", "date_not_in_past": "La data di fine non può essere nel passato", "date_out_of_limit": "Il piano non può andare oltre le 24 ore", "not_supported": "Questa funzionalità non è supportata da questa marca di stazioni di ricarica", "smart_charging_enabled_static_limitation": "La limitazione statica è ora gestita dalla ricarica intelligente", "charging_station_inactive": "La stazione di ricarica non è connessa al server", "smart_charging_enabled_charging_profiles": "I piani di ricarica sono ora gestiti dalla ricarica intelligente", "static_limit": "Limitazione Statica", "charging_profile_limit": "Piano di Ricarica", "debug_charging_station": "<PERSON><PERSON><PERSON>", "debug_charging_station_title": "Recupera il Piano di Ricarica dalla Stazione di Ricarica '{{chargeBoxID}}'", "reset_button": "Reset", "maximum_energy_limit": "Limite di Potenza di '{{chargeBoxID}}'", "invalid_min_duration": "La durata minima è di {{minDuration}} minuti", "invalid_max_duration": "La durata massima è di {{maxDuration}} minuti", "power_limit_title": "Limite di Potenza", "power_limit_confirm": "Vuoi veramente limitare la potenza di '{{chargeBoxID}}'?", "power_limit_success": "La potenza della stazione di ricarica '{{chargeBoxID}}' è stata limitata con successo", "power_limit_error": "Si è verificato un errore durante la limitazione della potenza della stazione di ricarica", "power_limit_has_charging_plan_title": "Piano di Ricarica Esistente", "power_limit_has_charging_plan_confim": ["C'è un conflitto con un piano di ricarica esistente!", "Vuoi veramente modificare il piano ed adattarlo al nuovo limite?"], "power_limit_plan_title": "Pianificatore Limite Potenza", "power_limit_plan_confirm": "Vuoi veramente limitare la potenza di '{{chargeBoxID}}' con questo piano di ricarica?", "power_limit_plan_clear": "Vuoi veramente cancellare il profilo di ricarica di '{{chargeBoxID}}'?", "power_limit_plan_success": "I profili di ricarica di '{{chargeBoxID}}' sono stati applicati con successo", "power_limit_plan_error": "Si è verificato un errore durante l'applicazione dei profili di ricarica", "power_limit_plan_not_accepted": "Il profilo non è stato accettato dalla stazione di ricarica '{{chargeBoxID}}'", "clear_profile_title": "Cancella Profili di Ricarica", "clear_profile_confirm": "Vuoi veramente cancellare TUTTI i profili di ricarica di '{{chargeBoxID}}'?", "clear_profile_success": "I profili di ricarica della stazione di ricarica '{{chargeBoxID}}' sono stati cancellati con successo", "clear_profile_error": "Si sono verificati degli errori durante la cancellazione deli profili di ricarica", "clear_profile_not_accepted": "La richiesta non è stata accettata dalla stazione di ricarica '{{chargeBoxID}}'.", "clear_profiles_button": "Cancella Profili", "current_limit": "Piano di limitazione potenza corrente", "current_limit_title": "Limite di potenza corrente", "limit_title": "Limite", "limit_in_amps": "({{limitInAmps}}A)", "limit_in_watts": "{{limitInWatts}}kW", "no_current_limit": "<PERSON>essun piano di limite trovato", "planning_energy_limit": "Piani di Ricarica", "from_date": "Da", "to_date": "A", "profile_type": "Tipo", "stack_level": "<PERSON><PERSON>", "profile_id": "ID Profilo", "duration": "<PERSON><PERSON>", "duration_with_unit": "<PERSON><PERSON> (min)", "start_date": "<PERSON><PERSON><PERSON>", "end_date": "Fine", "add_schedule_button": "Aggiungi pianificazione", "remove_schedule_button": "Rimuovi Pianificazione", "schedule_start_header": "Data di inizio pianificazione", "schedule_index_header": "Numero pianificazione", "schedule_duration_header": "Durata pianificazione (s)", "schedule_limit_header": "Pianificazione limite potenza", "empty_schedule_list_error": "Devi creare almeno una pianificazione", "connectors_all": "<PERSON><PERSON>", "charging_profile_not_found": "Profilo di ricarica non trovato", "profile_types": {"relative": "Relativo", "absolute": "Pianificazione Temporanea", "recurring_daily": "Pianificazione Giornaliera", "recurring_weekly": "Ricorrenza Settimanale"}, "retrieve_schedule": "<PERSON><PERSON><PERSON>", "charging_schedule": "Piani di ricarica di '{{chargeBoxID}}'", "enable_smart_charging_for_site_area_title": "Abilita Ricarica Smart", "enable_smart_charging_for_site_area_body": ["Se attivi la ricarica smart per quest'area del sito, l'algoritmo di ottimizzazione sostituirà l'attuale limitazione statica e i piani di ricarica.", "Vuoi continuare?"], "disable_smart_charging_for_site_area_title": "Disabilita Ricarica Smart", "disable_smart_charging_for_site_area_body": ["Se disattivi la ricarica smart per quest'area del sito, tutti i piani di ricarica saranno cancellati.", "Vuoi continuare?"], "clearing_charging_profiles_not_successful_title": "Si è verificato un errore durante la cancellazione dei piani di ricarica", "clearing_charging_profiles_not_successful_body": ["Impossibile cancellare i piani di ricarica delle stazioni di ricarica appartenenti all'area del sito '{{siteAreaName}}'!", "Controlla i log e procedi manualmente con la cancellazione del(i) piano(i) di ricarica."], "trigger_smart_charging_title": "Attiva Ricarica Smart", "trigger_smart_charging_confirm": "Vuoi veramente attivare l'algoritmo di ricarica smart?", "trigger_smart_charging": "Attiva Ricarica Smart", "trigger_smart_charging_success": "L'algoritmo di ricarica smart è stato avviato correttamente", "trigger_smart_charging_error": "Si è verificato un errore durante l'avvio dell'algoritmo di ricarica smart, controlla i log", "minutes": "min(s)", "charging_plans": {"charging_plan_name": "Nome", "charging_station_id": "Stazione di Ricarica", "connector_id": "ID Presa", "current_limit": "<PERSON>ite corrente", "kind": "Tipo", "purpose": "Scopo", "stack_level": "<PERSON><PERSON>", "site_area": "Area Sito", "site_area_limit": "Limite Area Sito", "redirect": "Vai ai Piani di Ricarica"}}, "more_actions": {"get_diagnostics_title": "Diagnostica caricatore '{{chargeBoxID}}'", "get_diagnostics_url": "Carica l'URL del file", "get_diagnostics_download_button": "Download", "get_diagnostics_dialog_title": "Recupera Diagnostica", "get_diagnostics_dialog_confirm": "Vuoi veramente scaricare il file di diagnostica della stazione di ricarica '{{chargeBoxID}}'?", "get_diagnostics_success": "Il download della diagnostica è stato avviato correttamente", "get_diagnostics_error": "Si è verificato un errore durante l'avvio del download della diagnostica"}, "action_error": {"transaction_start_title": "Errore di avvio sessione", "transaction_in_progress": "Una sessione è in corso, non posso avviare una nuova sessione", "no_active_transaction": "Nessuna sessione attiva", "transaction_start_not_available": "La presa non è disponibile, non posso avviare una sessione", "transaction_start_charger_inactive": "Caricatore non disponibile, non posso avviare una sessione", "transaction_start_not_authorized": "Non sei autorizzato ad avviare una sessione", "transaction_stop_title": "Errore interruzione sessioneS", "transaction_stop_not_authorized": "Non sei autorizzato a interrompere questa sessione", "transaction_stop_not_available": "La presa non è disponibile, impossibile interrompere una sessione", "transaction_stop_charger_inactive": "Il caricatore non è disponibile, impossibile interrompere una sessione", "delete_title": "Errore Cancellazione Stazione di Ricarica", "delete_active_transaction": "La cancellazione di una stazione di ricarica non è consentita quando una sessione è in corso", "command_title": "Errore Comando Stazione di Ricarica", "command_charger_disconnected": "La stazione di ricarica non è disponibile, impossibile inviare un comando", "smart_charging_title": "Errore Carica Smart", "smart_charging_charger_disconnected": "La stazione di ricarica non è disponibile, impossibile inviare un comando", "smart_charging_charger_version": "La ricarica smart necessita almeno della versione OCCP 1.6 sulla stazione di ricarica", "session_details_title": "<PERSON><PERSON>re <PERSON>li <PERSON>", "session_details_not_authorized": "Non sei autorizzato a visualizzare i dettagli della sessione", "not_authorized": "Non sei autorizzato ad eseguire questa azione", "ocpp_parameters_should_not_be_empty": "Chiave e valore del parametro OCPP non devono essere vuoti", "ocpp_parameters_change_title": "Errore Parametro OCPP"}, "reboot_confirm": "Vuoi veramente riavviare la stazione di ricarica '{{chargeBoxID}}'?", "reboot_success": "La stazione di ricarica '{{chargeBoxID}}' è stata riavviata correttamente", "reboot_error": "Si è verificato un errore durante il riavvio della stazione di ricarica", "retrieve_configuration_title": "Ottieni Configurazione", "retrieve_configuration_confirm": "Vuoi veramente recuperare la configurazione dalla stazione di ricarica '{{chargeBoxID}}'?", "reboot_required_title": "<PERSON><PERSON><PERSON><PERSON>", "reboot_required_confirm": "Vuoi veramente riavviare la stazione di ricarica '{{chargeBoxID}}'?", "ocpp_params_list_error": "Nessun parametro OCPP da visualizzare", "ocpp_params_update_from_template_title": "Aggiurna parametri OCPP", "ocpp_params_update_from_template_confirm": "Vuoi veramente aggiornare i parametri OCPP della stazione di ricarica '{{chargeBoxID}}' con l'ultimo template?", "ocpp_params_update_from_template_success": "I parametri OCPP sono stati aggiornati correttamente dalla stazione di ricarica '{{chargeBoxID}}'", "ocpp_params_update_from_template_error": "Si è verificato un errore durante l'aggiornamento dei parametri OCPP della stazione di ricarica", "set_configuration_title": "Salva Modifica Configurazione", "set_configuration_confirm": "Vuoi veramente modificare il parametro '{{key}}' sulla stazione di ricarica '{{chargeBoxID}}'?", "soft_reset_confirm": "Vuoi veramente resettare la stazione di ricarica '{{chargeBoxID}}'?", "soft_reset_success": "La stazione di ricarica '{{chargeBoxID}}' è stata resettata correttamente", "soft_reset_error": "Si è verificato un errore durante il reset della stazione di ricarica", "clear_cache_title": "<PERSON><PERSON><PERSON><PERSON>", "clear_cache_confirm": "Vuoi veramente pulire la cache della stazione di ricarica '{{chargeBoxID}}'?", "clear_cache_success": "La cache della stazione di ricarica '{{chargeBoxID}}' è stata pulita correttamente", "clear_cache_error": "Si è verificato un errore durante la pulizia della cache della stazione di ricarica", "force_available_status_title": "Forza Stato su Disponibile", "force_available_status_confirm": "Vuoi veramente modificare lo stato di tutte le prese della stazione di ricarica '{{chargeBoxID}}' su Disponibile?", "force_available_status_success": "Lo stato delle prese della stazione di ricarica '{{chargeBoxID}}' è stato modificato con successo", "force_available_status_error": "Si è verificato un errore durante la modifica dello stato delle prese della stazione di ricarica", "force_unavailable_status_title": "Forza Stato su Non Disponibile", "force_unavailable_status_confirm": "Vuoi veramente modificare lo stato di tutte le prese della stazione di ricarica '{{chargeBoxID}}' su Non Disponibile?", "force_unavailable_status_success": "Lo stato delle prese della stazione di ricarica '{{chargeBoxID}}' è stato modificato con successo", "force_unavailable_status_error": "Si è verificato un errore durante la modifica dello stato delle prese della stazione di ricarica", "retrieve_config_success": "La configurazione della stazione di ricarica '{{chargeBoxID}}' è stata recuperata correttamente", "change_config_success": "La stazione di ricarica '{{chargeBoxID}}' è stata salvata correttamente", "change_config_error": "Impossibile modificare la configurazione della stazione di ricarica, controllare i log", "change_config_phase_error": "Impossibile assegnare una stazione di ricarica trifase ad un'area del sito monofase", "charge_point_connectors_error": "Si é verificato un errore durante il salvataggio dei dati della stazione di ricarica. Le proprietà dei punti di ricarica non corrispondono alle prese.", "change_params_success": "Il parametro '{{paramKey}}' è stato aggiornato correttamente nella stazione di ricarica '{{chargeBoxID}}'", "change_params_error": "Si è verificato un errore durante il tentativo di aggiornamento del parametro della stazione di ricarica", "delete_title": "Can<PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare la stazione di ricarica '{{chargeBoxID}}'?", "delete_success": "La stazione di ricarica '{{chargeBoxID}}' è stata cancellata con successo", "delete_error": "Si è verificato un errore durante la cancellazione della stazione di ricarica", "properties_title": "Proprietà", "ocpp_parameters_title": "Parametri OCPP", "firmware_update_title": "Aggiornamento Firmware", "update_firmware_title": "Aggiorna Firmware", "update_firmware_confirm": "Vuoi veramente aggiornare il firmware della stazione di ricarica '{{chargeBoxID}}'?", "button_update_firmware": "Aggiorna", "update_firmware_error": "Si è verificato un errore durante il tentativo di invio del comando di aggiornamento firmware alla stazione di ricarica", "update_firmware_success": "La richiesta di aggiornamento del firmware della stazione di ricarica '{{chargeBoxID}}' è stata inviata correttamente", "save_charger": "<PERSON><PERSON>", "stop_transaction_title": "Interrompi", "stop_transaction_confirm": "Vuoi veramente interrompere la sessione della stazione di ricarica '{{chargeBoxID}}'?", "stop_transaction_success": "La sessione della stazione di ricarica '{{chargeBoxID}}' è stata interrotta con successo", "stop_transaction_error": "Si è verificato un errore durante l'interruzione della sessione", "stop_transaction_user_not_allowed": "Non sei autorizzato ad interromperer questa sessione", "stop_transaction_missing_active_tag": "Non si può fermare la sessione sulla stazione di ricarica '{{chargeBoxID}}', bisogna utilizzare un badge RFID compatibile.", "start_transaction_title": "Avvia", "start_transaction_button": "Avvia", "start_transaction_confirm": "Vuoi veramete avviare una nuova sessione per l'utente '{{userName}}' sulla stazione di ricarica '{{chargeBoxID}}'?", "start_transaction_success": "La sessione sulla stazione di ricarica '{{chargeBoxID}}' è stata avviata correttamente", "start_transaction_error": "Si è verificato un errore durante il tentativo di avvio di una nuova sessione", "start_transaction_missing_active_tag": "Impossibile avviare la transazione per l'utente '{{userName}}' sulla stazione di ricarica '{{chargeBoxID}}', l'utente deve avere almeno un badge attivo", "start_transaction_user_not_allowed": "Non sei autorizzato ad avviare una sessione su questa stazione di ricarica", "unlock_connector_title": "S<PERSON><PERSON>ca", "unlock_connector_button": "S<PERSON><PERSON>ca", "unlock_connector_confirm": "Vuoi veramente sbloccare la presa '{{connectorId}}' della stazione di ricarica '{{chargeBoxID}}'?", "unlock_connector_success": "La presa '{{connectorId}}' della stazione di ricarica '{{chargeBoxID}}' è stata sbloccata correttamente", "unlock_connector_error": "La stazione di ricarica non è stata in grado di sbloccare la presa", "unlock_connector_not_supported_error": "Questa Stazione di Ricarica non supporta lo sblocco della presa", "connector_type_unknown": "Non impostato", "connector_type_all": "All connectors", "connector_type_type2": "Tipo 2", "connector_type_type1": "Tipo 1", "connector_type_type1ccs": "Tipo 1 - Combo CCS", "connector_type_type3c": "Tipo 3C", "connector_type_domestic": "Domestica", "connector_type_combo": "Tipo 2 - Combo CCS", "connector_type_chademo": "CHAdeMO", "connector_type_gta": "GB T AC", "connector_type_gtd": "GB T DC", "connector_type_tesla": "Tesla", "direct_current": "<PERSON><PERSON><PERSON>", "alternating_current": "<PERSON><PERSON>nte <PERSON>", "direct_and_alternating_current": "Corrente Continua e Alternata", "single_phase": "Monofase", "tri_phases": "Trifase", "site_area": "Area Sito", "assign_site_area": "Assegna Area Sito", "change_site_area": "Modifica", "site": "<PERSON><PERSON>", "invalid_url": "URL non valido", "invalid_power": "Il valore di potenza deve essere un numero positivo", "invalid_voltage": "Il valore della tensione deve essere un numero positivo", "invalid_amperage": "Il valore dell'amperaggio deve essere un numero positivo", "invalid_amperage_phases": "L'amperaggio deve essere divisibile per il numero di fasi", "unsaved_title": "Modifiche non salvate", "unsaved_confirmed": "Vuoi veramente salvare le modifiche?", "changes_cancelled": "Le modifiche sono state cancellate", "unsaved_changes": "Hai delle modifiche non salvate. Salvale o cancellale.", "button_retrieve_configuration": "OCPP Retrieve Configuration", "button_force_ocpp_params_update_from_template": "Aggiornare la configurazione OCPP da template", "created_on": "Data creazione", "start_transaction_details_title": "Avvia la sessione sulla stazione di ricarica '{{chargeBoxID}}'", "start_transaction": "Avvia Sessione", "power_limit_unit": "Presa Unità Alimentazione", "watt": "<PERSON>", "amper": "Ampere", "voltage": "Fase/Tensione Neutra (V)", "amperage": "Amperaggio Totale (A)", "amperagePerPhase": "Amperaggio per fase (A)", "session_details": "Sessione", "qr_code_generation_error": "Si è verificato un errore durante la generazione del codice QR", "tabs": {"list": "Stazioni di Ricarica", "charging_plans": "Piani di Ricarica", "in_error": "In Errore", "connection": "Registrare una nuova stazione di ricarica", "evse": "Reservation", "evsee": "Reservation"}, "errors": {"missing_settings": {"title": "Impostazioni Mancanti", "description": "Mancano le impostazioni richieste", "action": "Mantenere le impostazioni richieste mancanti nella configurazione della stazione di ricarica"}, "connection_broken": {"title": "Connessione Persa", "description": "Il server non può raggiungere la stazione di ricarica", "action": "È richiesto un intervento manuale. Controlla la rete e/o la configurazione della stazione di ricarica"}, "missing_site_area": {"title": "Non Assegnata ad un'Area del Sito", "description": "La stazione di ricarica deve essere assegnata ad un'area del sito", "action": "Mantenere l'area del sito nella configurazione della stazione di ricarica"}, "connector_error": {"title": "Errore Presa", "description": "La stazione di ricarica ha inviato un codice di errore per una delle sue prese", "action": "Controlla i dettagli del codice di errore ed eventualmente riavviare la stazione di ricarica"}}, "connections": {"registration_tokens_title": "Token Registrazione Stazione di Ricarica", "registration_token_site_area_name": "Token per l'Area del Sito '{{siteAreaName}}'", "registration_token_creation_title": "Crea <PERSON>ken Registrazione", "registration_token_creation_confirm": "Vuoi veramente creare un token di registrazione per le stazioni di ricarica di quest'area del sito?", "registration_token_creation_success": "Il token di registrazione è stato creato correttamente", "registration_token_creation_error": "Si è verificato un errore durante la creazione di un token di registrazione", "registration_token_site_admin_creation_error": "Bisogna associare un link al token di registrazione", "registration_token_revoke_title": "Revoca Token Registrazione", "registration_token_revoke_confirm": "Vuoi veramente revocare il token di registrazione?", "registration_token_revoke_success": "Il token di registrazione è stato revocato correttamente", "registration_token_revoke_error": "Si è verificato un errore durante la revoca di un token di registrazione", "registration_token_update_title": "Aggiornare Token Registrazione", "registration_token_update_confirm": "Si desidera aggiornare il token di registrazione?", "registration_token_update_success": "Token Registrazione é stato aggiornato con successo", "registration_token_update_error": "Si é verificato un errore durante l'aggiornamento del token di registrazione, si prega di controllare i logs.", "registration_token_not_found": "Il Token Registrazione non é stato trovato", "registration_token_delete_title": "Cancella Token Registrazione", "registration_token_delete_confirm": "Vuoi veramente cancellare il token di registrazione?", "registration_token_delete_success": "Il token di registrazione è stato cancellato correttamente", "registration_token_delete_error": "Si è verificato un errore durante la cancellazione di un token di registrazione", "registration_token_url": "URL di Registrazione", "registration_token_valid": "Valido", "registration_token_expired": "Scaduto", "registration_token_revoked": "Revocato", "registration_token_error": "Si é verificato un errore durante il recupero del Token di Registrazione, si prega di controllare i logs.", "token_restrict_site_area": "Limita l'accesso ad un'Area del Sito", "url": "URL OCPP", "copy_url_tooltip": "Copia l'URL OCPP", "url_copied": "L'URL OCPP è stato copiato negli appunti", "generate_connection_url": "Connetti una Stazione di Ricarica", "ocpp_15_soap": "OCPP 1.5 SOAP", "ocpp_16_json": "OCPP 1.6 JSON", "ocpp_16_soap": "OCPP 1.6 SOAP", "ocpp_15_soap_secure": "Secure OCPP 1.5 SOAP", "ocpp_16_json_secure": "Secure OCPP 1.6 JSON", "ocpp_16_soap_secure": "Secure OCPP 1.6 SOAP", "charging_station_shedules_delete_title": "Delete Scheduling", "charging_station_shedules_delete_confirm": "Do you really want to delete the Scheduling?", "charging_station_shedules_delete_success": "Scheduling has been deleted successfully", "charging_station_shedules_delete_error": "Error occurred while deleting a Scheduling, check the logs", "charging_station_shedules_creation_success": "Station schedule has been created successfully", "charging_station_shedules_creation_error": "Error occurred while creating a Station schedule, check the logs", "get_Stations_error": "Error at Stations fetching time", "charging_station_shedules_update_success": "Station schedule has been updated successfully", "charging_station_shedules_update_error": "Error occurred while updating a Station schedule, check the logs", "charging_station_shedules_not_found": "Station schedule has not been found"}}, "logs": {"logging_number": "Log", "debug": "Debug", "info": "Info", "warning": "Avvertimento", "error": "Errore", "status": "Stato", "level": "<PERSON><PERSON>", "levels": "<PERSON><PERSON>", "date": "Data", "source": "<PERSON><PERSON><PERSON>", "host": "Host", "process": "Processo", "user": "Utente", "users": "<PERSON><PERSON><PERSON>", "on_user": "Azione su Utente", "action": "Azione", "actions": "Azioni", "type": "Tipo", "type_security": "<PERSON><PERSON><PERSON>", "type_regular": "Regolare", "message": "Messaggio", "module": "<PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON>", "search_one_minute": "<PERSON><PERSON><PERSON>", "search_10_minutes": "Ultimi 10 Minuti", "search_30_minutes": "Ultimi 30 Minuti", "search_one_hour": "Ultima Ora", "search_24_hours": "Last 24 hours", "search_today": "<PERSON><PERSON><PERSON>", "search_yesterday": "<PERSON><PERSON>", "search_this_week": "Quest<PERSON> Settimana", "search_last_week": "Settimana Scorsa", "search_this_month": "<PERSON><PERSON>", "search_last_month": "<PERSON><PERSON>", "select_actions": "Azioni Selezionate", "redirect": "Controlla Log", "no_logs": "Nessun log corrispondente ai filtri", "dialog": {"export": {"title": "Esporta Log", "confirm": "Vuoi veramente esportare tutti i log corrispondenti ai filtri?", "error": "Si è verificato un errore durante il tentativo di esportare i log"}}}, "users": {"id": "Id", "tabs": {"list": "<PERSON><PERSON><PERSON>", "in_error": "In Errore", "tags": "Badges", "billing": "Fatturazione"}, "security": "<PERSON><PERSON><PERSON>", "import_users": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>", "import_users_message": "L'importazione eliminerà tutti gli utenti precedentemente importati che non sono ancora presenti nella base dati, desideri continuare?", "import_users_message_auto_activate": "L'importazione eliminerà tutti gli utenti precedentemente importati che non sono ancora presenti nella base dati e gli utenti saranno automaticamente attivati, desideri continuare?", "import_users_auto_activation": "Attivare automaticamente i nuovi utenti", "import_users_success": "{{inSuccess}} utenti sono stati caricati con successo e saranno processati in maniera asincrona", "import_users_partial": "{{inSuccess}} utenti sono stati caricati con successo, saranno processati in maniera asincrona e {{inError}} utenti sono in errore, si prega di controllare i logs", "import_users_error": "{{inError}} utenti in errore durante il caricamento", "import_no_users": "Nessun utente é stato caricato", "access_mode": "Modalità di accesso", "allow_free_access": "Accesso libero a tutte le stazioni di ricarica", "user_without_freeAccess": "Accesso a pagamento", "user_with_freeAccess": "Accesso libero", "number_of_sites": "<PERSON><PERSON><PERSON>i", "number_of_transactions": "Num. <PERSON>i", "eula_accepted_on": "EULA Accettato Il", "edit_profile_short": "MP", "wallet": "Wallet", "razorpay_wallet": "Razorpay Wallet", "virtual_wallet": "Virtual Wallet", "profile": "<PERSON>ilo", "edit_profile": "Modifica Profilo", "user_number": "Utente(i)", "technical_user": "API User", "non_technical_user": "Utente normale", "role": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>", "role_admin": "Amministratore", "role_super_admin": "Super Amministratore", "role_basic": "Base", "role_demo": "Demo", "role_comp_admin": "Amministratore Aziendale", "role_emp": "Employee", "company": "Company", "role_company_admin": "Company Admin", "role_transport_admin": "Transport Admin", "role_transport_manager": "Transport Manager", "role_invalid": "Ruolo non valido", "role_mult_all": "<PERSON><PERSON> i Ruoli", "role_mult_admin_demo": "Amministratore o Demo", "user_not_found": "L'utente non è stato trovato", "user_id_not_found": "L'utente '{{userId}}' non è stato trovato", "invalid_phone_number": "Numero di telefono non valido", "missing_tag": "Questa lista deve contenere almeno un badge", "invalid_tag_id": "Sono permessi solo numeri e lettere", "invalid_plate_id": "Sono permesse solo ma<PERSON><PERSON>le, numeri e '-'", "locale": "<PERSON><PERSON>", "locale_invalid": "Lingua non valida", "locale_desc_en_US": "<PERSON><PERSON><PERSON>", "locale_desc_fr_FR": "<PERSON><PERSON>", "locale_desc_es_ES": "<PERSON><PERSON><PERSON>", "locale_desc_de_DE": "Tedesco", "locale_desc_pt_PT": "<PERSON><PERSON><PERSON>", "locale_desc_it_IT": "Italiano", "locale_desc_cs_CZ": "Ceco", "locale_desc_en_AU": "<PERSON><PERSON>", "role_unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locale_unknown": "Sconosciuta", "status": "Stato", "picture": "<PERSON><PERSON><PERSON><PERSON>", "show_image": "<PERSON><PERSON>", "sites": "<PERSON><PERSON>", "assigned_sites_to_user": "Siti assegnati a {{userName}}", "status_invalid": "Stato non valido", "status_active": "Attivo", "status_blocked": "Sosp<PERSON>o", "status_inactive": "Inattivo", "status_locked": "Bloccato", "status_pending": "In attesa", "status_unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "Email", "name": "Nome", "first_name": "Nome", "plate_id": "Targa", "technical_title": "API Utente", "technical": "API utente (usata per comunicazione backend-to-backend)", "tags": "Badge", "tag_ids_help": "Immettere una lista di ID Badge separati da una virgola", "inumber": "Numero Aziendale", "cost_center": "Centro di Costo", "phone": "Telefono", "mobile": "Cellulare", "notifications_active": "Abilita le Notifiche (Email, Cellulare)", "created_on": "Creato Il", "created_by": "<PERSON><PERSON><PERSON>", "changed_on": "Modificato Il", "changed_by": "Modificato Da", "save_profile": "<PERSON><PERSON>", "save_address": "<PERSON><PERSON>", "save_password": "Salva Password", "save_miscs": "<PERSON> Varie", "miscs": "<PERSON><PERSON><PERSON>", "email_already_used": "Email già usata da un altro utente", "edit": "Modifica", "delete": "Can<PERSON><PERSON>", "delete_title": "Cancella U<PERSON>", "delete_button": "Can<PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare l'utente '{{userFullName}}'?", "delete_success": "L'utente '{{userFullName}}' è stato cancellato correttamente", "delete_billing_error": "Si è verificato un errore durante la cancellazione dell'utente dovuto al sistema di fatturazione, controlla i log", "delete_error": "Si è verificato un errore durante la cancellazione dell'utente", "remove_sites_title": "<PERSON><PERSON><PERSON><PERSON>(i)", "remove_sites_confirm": "Sei sicuro di voler rimuovere il(i) sito(i) selezionati da questo utente?", "remove_sites_success": "Il sito(i) è(sono) stato(i) rimosso(i) correttamente", "remove_sites_error": "Si è verificato un errore durante la rimozione del(i) sito(i) dall'utente", "export_users_title": "Esporta Utenti", "export_users_confirm": "Vuoi davvero esportare l'elenco utenti in un formato csv?", "export_users_error": "Si è verificato un errore durante il tentativo di esportare gli utenti", "update_success": "L'utente '{{userFullName}}' è stato aggiornato correttamente", "update_sites_success": "I siti sono stati aggiunti correttamente", "changed_by_other_user": "L'utente '{{userFullName}}' è stato modificato da un altro utente e sarà ricaricato", "update_error": "Si è verificato un errore durante l'aggiornamento dell'utente", "user_do_not_exist": "L'utente non esiste", "title": "Utente", "picture_size_error": "Il limite di dimensione dell'immagine è stato superato, il limite è inferiore a {{maxPictureKb}} Kb", "create_success": "L'utente {{userFullName}} è stato creato correttamente", "create_error": "Si è verificato un errore durante la creazione dell'utente", "no_users": "<PERSON><PERSON>un utente trovato", "select_users": "Seleziona Utente", "update_set_site_admin_success": "L'utente è ora amministratore del sito '{{siteName}}'", "update_remove_site_admin_success": "L'utente non è più amministratore del sito '{{siteName}}'", "update_site_admin_role_error": "Si è verificato un errore durante l'aggiornamento dei diritti utente del sito '{{siteName}}'", "update_set_site_owner_success": "L'utente è ora proprietario del sito '{{siteName}}", "update_remove_site_owner_success": "L'utente non è più proprietario del sito '{{siteName}}'", "update_site_owner_role_error": "Si è verificato un errore durante il tentativo di aggiornamento dei diritti utente del sito '{{siteName}}'", "redirect": "Navigate to User", "display_sites": "Display sites", "connectors": {"connect": "<PERSON><PERSON><PERSON>", "revoke": "Revoca", "created_on": "Creata Il", "expired_on": "Scaduta Il", "not_connected": "Non connessa"}, "invoicing": {"errors": {"no_invoice_found": "Nessuna fattura disponibile per il tuo account", "unable_to_get_invoice": "Si è verificato un errore durante il recupero della tua fattura"}}, "errors": {"inactive_user": {"title": "Utenti Inattivi", "description": "Questo utente non è attivo.", "action": "Controlla lo stato di questo utente."}, "unassigned_user": {"title": "Non Assegnato ad un Sito", "description": "Questo utente non è assegnato ad un sito.", "action": "Assegna questo utente ad almeno un sito poichè il componente organizzazione è attivo."}, "inactive_user_account": {"title": "Account Utente Inattivo", "description": "Questo utente non si ha effettuato l'accesso da almeno 6 mesi.", "action": "Cancella o disabilita questo account utente."}, "failed_billing_synchro": {"title": "Sincronizzazione fattura fallita", "description": "La sincronizzazione di questo utente con il sistema di fatturazione non è riuscita.", "action": "Risincronizza manualmente."}}, "export_tags_title": "Esportare i Tags", "export_tags_confirm": "Desideri esportare la lista dei tags in formato CSV?", "export_tags_error": "Si é verificato un errore durante l'esportazione dei tags, si prega di verificare i logs."}, "invoices": {"tabs": {"list": "Fatture"}, "tooltips": {"download": "Scarica fattura", "pay": "Paga fattura"}, "status": {"paid": "Pagata", "unpaid": "Non pagata", "draft": "<PERSON><PERSON>", "deleted": "Cancellata", "uncollectible": "Non recuperabile", "void": "<PERSON><PERSON><PERSON>"}, "number": "Numero Fattura", "createdOn": "Data Fattura", "price": "Prezzo Fattura", "amount": "Importo", "tax": "Tax", "taxedAmount": "Taxed Amount", "cannot_retrieve_invoices": "Impossibile recuperare l'elenco delle fatture", "cannot_download_invoice": "Impossibile scaricare la fattura", "number_of_items": "Sessioni", "pay": "Paga", "user": "Utente", "failed_download": "Download della fattura non riuscito"}, "transfers": {"tabs": {"list": "Transfers"}, "tooltips": {"download": "Download fattura per commissione", "finalize": "Finalizzare il trasferimento", "send": "Inviare il trasferimento"}, "status": {"draft": "<PERSON><PERSON>", "pending": "In attesa", "finalized": "Finalizzato", "transferred": "Transferito"}, "id": "ID", "accountExternalID": "Identificativo account connesso", "collected_funds": "Fondi r<PERSON>i", "document_number": "Fattura", "platform_fee_amount": "Ammontare raccolto", "platform_fee_tax_inclusive": "<PERSON>uo<PERSON> (incl. tasse)", "transferAmount": "Trasferimento", "number_of_items": "Sessioni", "transferExternalID": "ID esterno", "accountOwner": "Proprietario", "cannot_finalize_transfer": "Operazione fallita - il trasferimento non può essere finalizzato", "cannot_send_transfer": "Operazione fallita - il trasferimento non é stato inviato al sistema di fatturazione", "cannot_download_commission_incoice": "Impossibile scaricare la fattura per commissione"}, "tags": {"id": "Tag ID", "not_active": "Questo badge non é attivo", "issuer": "Proprietario Organizzazione", "visual_id": "Tag ID visualizzato", "visual_id_instruction": "Si prega di riempire l'ID visualizzato", "status": "Stato", "activated": "Attivo", "deactivated": "Inattivo", "sessions": "Sessioni", "activate_title": "Attivazione badge", "activate_confirm": "Vuoi veramente attivare il badge '{{tagID}}'? Una volta attivato, questo badge sarà utilizzabile per avviare sessioni.", "deactivate_title": "Disattivazione badge", "deactivate_confirm": "Vuoi veramente disattivare il badge '{{tagID}}'? Una volta disattivato, questo badge non sarà più utilizzabile per avviare sessioni.", "deactivate_success": "Il badge '{{tagID}}' è stato disattivato con successo", "deactivate_error": "Si è verificato un errore durante la disattivazione del badge", "activate_success": "Il badge '{{tagID}}' è stato attivato con successo", "activate_error": "Si è verificato un errore durante l'attivazione del badge", "delete_confirm": "Vuoi davvero eliminare il badge '{{id}}'?", "delete_title": "Elimina badge", "delete_success": "Il badge '{{id}}' è stato eliminato con successo", "delete_error": "Si è verificato un errore durante l'eliminazione del badge", "create_success": "Il badge '{{tagID}}' è stato creato con successo", "create_error": "Si è verificato un errore durante la creazione del badge", "register_success": "The badge '{{visualID}}' has been registered successfully", "register_error": "Si è verificato un errore durante la creazione del badge", "update_success": "Il badge '{{tagID}}' è stato aggiornato con successo", "update_by_visual_id_success": "Il badge '{{visualID}}' è stato aggiornato con successo", "update_error": "Si è verificato un errore durante l'aggiornamento del badge", "tag_error": "Si è verificato un errore durante il recupero del badge", "tag_not_found": "Il badge non è stato trovato", "select_tags": "Seleziona Tag", "tag_id_already_used": "Il badge '{{tagID}}' esiste già", "tag_visual_id_already_used": "Il visualID '{{visualID}}' esiste già", "tag_visual_id_does_not_match_tag": "Il visualID '{{visualID}}' non corrisponde a nessun badge.", "tag_inactive": "Il badge '{{visualID}}' é inattivo e non può essere assegnato", "redirect": "Vai ai Tag", "title": "Badge", "default_tag": "Badge di Default", "delete_tags_title": "Elimina Badge", "delete_tags_confirm": "Vuoi davvero eliminare '{{quantity}}' badge?", "delete_tags_success": "{{inSuccess}} badge sono stati eliminati correttamente", "delete_tags_partial": "{{inSuccess}} badge sono stati eliminati correttamente e {{inError}} badge hanno riscontrato un errore, controlla i log", "delete_tags_error": "{{inError}} badges non sono stati eliminati correttamente, si prega di controllare i logs", "delete_tags_unexpected_error": "Si é verificato un errore nella cancellazione dei badges, si prega di controllare i logs", "delete_no_tag": "Nessun badge é stato cancellato", "import_tags": "Importare i Badges", "import_tags_message": "L'importazione eliminerà tutti i badges caricati precedentemente e non ancora importati nella base dati, si desidera continuare?", "import_tags_message_auto_activate": "L'importazione eliminerà tutti i badges caricati precedentemente e non ancora importati nella base dati, si desidera continuare?", "import_tags_success": "{{inSuccess}} badges sono stati caricati correttamente e saranno processati in modalità asincrona", "import_tags_partial": "{{inSuccess}} badges sono stati caricati correttamente e saranno processati in modalità asincrona e {{inError}} badges sono in errore, si prega di controllare i logs", "import_tags_error": "{{inError}} badges non sono stati caricati correttamente", "import_no_tags": "Nessun badge é stati caricato", "import_tags_auto_activation": "Attivare automaticamente nuovi tags"}, "tenants": {"title": "Organizzazione", "name": "Nome", "logo": "Logo", "tenant_management": "Gestione Organizzazione", "tenant_not_found": "Questa organizzazione non è stata trovata", "email": "Email", "subdomain": "Sottodominio", "create_success": "L'organizzazione '{{name}}' è stata creata con successo", "create_error": "Si è verificato un errore durante la creazione dell'organizzazione", "subdomain_already_used": "Il sottodominio '{{subdomain}}' è già in uso", "update_error": "Si è verificato un errore durante l'aggiornamento dell'organizzazione", "error_subdomain": "Sono consentiti solo lettere minuscole e numeri", "update_success": "L'organizzazione '{{name}}' è stata aggiornata correttamente", "delete_confirm": "Vuoi veramente cancellare l'organizzazione '{{name}}'?", "delete_title": "Cancella Organizzazione", "redirectWebsiteUrl": "Redirect Website URL", "delete_success": "L'organizzazione '{{name}}' è stata cancellata correttamente", "delete_error": "Si è verificato un errore durante la cancellazione dell'organizzazione", "save_error_refund": "Il prezzo deve essere attivo per utilizzare il rimborso", "save_error_roaming": "Non possono essere attivati contemporaneamente i componenti di roaming OCPI e OICP", "save_error_billing": "Il prezzo deve essere attivo per usare la fatturazione", "save_error_billing_platform": "La fatturazione deve essere attivate per usare la Piattaforma di Fatturazione", "save_error_smart_charging": "L'organizzazione deve essere attiva per utilizzare Carica Smart", "save_error_asset": "L'Organizzazione deve essere attiva per usare Risorsa", "save_error_car_connector": "Gestione Auto deve essere attivato per utilizzare Prese Auto", "smart_charging_still_active_for_site_area": ["Carica Smart è ancora attiva per alcune aree del sito.", "Si prega di disattivare la Carica Smart per tutte le aree del sito nell'organizzazione."], "logo_size_error": "Il limite di dimensione del logo è stato superato, il limite è inferiore a {{maxPictureKb}} Kb"}, "accounts": {"title": "Account <PERSON><PERSON><PERSON>", "list": {"select_account": "Account con<PERSON>o se<PERSON>o", "add_account": "Aggiungere Account <PERSON><PERSON>", "business_owner": "Proprietario", "company_name": "Nome della compagnia", "account_status": "Stato", "account_id": "Account <PERSON><PERSON>o ID", "billing_account": "Account <PERSON><PERSON><PERSON>"}, "message": {"create_success": "Account aggiunto con successo", "create_error": "Si é verificato un errore durante la creazione dell'account, si prega di controllare i logs", "onboard_success": "La registrazione é stata avviata. Un'email é stata mandata al proprietario dell'attività", "onboard_error": "Si é verificato un errore alla registrazione dell'account, si prega di controllare i logs"}, "status": {"account_idle": "Inattivo", "account_pending": "In sospeso", "account_active": "Attivo"}, "onboarding": {"onboard_action": "Inviare l'email di registrazione al proprietario dell'attività", "onboarding_title": "Open e-Mobility - Registrazione Account", "onboarding_message": "Sei stato invitato a registrare la tua compagnia come account connesso alla piattaforma Open e-Mobility.", "onboarding_message_to_proceed": "Cliccare sur link sottostante per completare la registrazione.", "onboarding_congratulations": "Congratulazioni", "onboarding_process_completed": "La registrazione del tuo account é stata completata.", "onboarding_button_proceed": "Continua", "onboarding_navigate_to_dashboard": "Vai alla dashboard", "onboarding_process_failed": "La registrazione del tuo account é fallita - si prega di contattare il supporto!"}, "platform_fee": {"platform_fee_group_title": "Piattaforma tariffa", "platform_flat_fee": "Tariffa fissa per sessione", "platform_flat_fee_invalid": "La Tariffa fissa deve essere un numero positivo", "platform_fee_percentage": "Tariffa percentuale per sessione", "platform_fee_percentage_invalid": "La Tariffa percentuale deve essere tra 0% e 100%"}}, "settings": {"settings_not_saved_title": "Impostazioni non Salvate", "settings_not_saved": "Devi prima salvare le tue impostazioni", "tabs": {"ocpi": "Roaming", "oicp": "Roaming Hubject", "refund": "<PERSON><PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON>", "billing": "Fatturazione", "analytics": "Analytics", "sms": "SMS", "smart_charging": "Carica Smart", "asset": "<PERSON><PERSON><PERSON><PERSON>", "car_connector": "Car Connector", "taxes": "Taxes", "smtp": "SMTP"}, "ocpi": {"gireve": {"title": "<PERSON><PERSON><PERSON>"}, "setting_do_not_exist": "Impostazioni OCPI non trovate", "setting_not_found": "Le impostazioni OCPI non sono state trovate", "create_success": "Le impostazioni OCPI sono state create correttamente", "create_error": "Si è verificato un errore durante la creazione delle impostazioni OCPI", "update_success": "Le impostazioni OCPI sono state aggiornate correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni OCPI", "description": ["E-Mobility fornisce un servizio certificato per la connessione ad una Piattaforma E-Roaming esterna.", "Questa funzionalità espone in tempo reale l'infrastruttura e la disponibilità della stazione di ricarica tramite il protocollo standard OCPI (Open Charge Point Interface).", "E-Mobility sta attualmente utilizzando la Gireve Inter-Operability Platform (IOP)."]}, "oicp": {"hubject": {"title": "Hubject"}, "setting_do_not_exist": "Impostazioni OCPI non trovate", "setting_not_found": "Le impostazioni OCPI non sono state trovate", "create_success": "Le impostazioni OCPI sono state create correttamente", "create_error": "Si é verificato un errore durante la creazione delle impostazioni OICP, si prega di controllare i logs", "update_success": "Le impostazioni OCPI sono state aggiornate correttamente", "update_error": "Si é verificato un errore durante l'aggiornamento delle impostazioni OICP, si prega di controllare i logs", "description": ["E-Mobility fornisce un servizio certificato per la connessione ad una Piattaforma E-Roaming esterna.", "Questa funzionalità espone in tempo reale l'infrastruttura e la disponibilità della stazione di ricarica tramite il protocollo standard OICP  (Open InterCharge Protocol).", "E-Mobility sta attualmente utilizzando l'Hubject B2B Service Platform (HBS)."]}, "refund": {"description": ["E-Mobility fornisce un servizio di rimborso per un sistema di gestione spese esterno.", "Questa funzionalità consente all'utente di inoltrare le sue sessioni ad un sistema di gestione spese esterno ed ottenere il rimborso per l'elettricità consumata.", "E-Mobility sta usando attualmente SAP Concur. Ulteriori informazioni tecniche disponibili all'indirizzo"], "not_found": "Impostazioni di rimborso non trovate", "create_success": "Le impostazioni di rimborso sono state create con successo", "create_error": "Si è verificato un errore durante la creazione delle impostazioni di Rimborso", "update_success": "Le impostazioni di rimborso sono state aggiornate correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni di Rimborso", "synchronize_dialog_refund_title": "Sincronizza Transazioni Rimborsate", "synchronize_dialog_refund_confirm": "Vuoi veramente sincronizzare le transazioni rimborsate?", "synchronize_started": "La sincronizzazione delle transazioni rimborsate è stata avviata", "synchronize_success": "Le transazioni rimborsate sono state sincronizzate correttamente", "synchronize_error": "Si è verificato un errore durante la sincronizzazione delle transazioni rimborsate", "connection_error": "Errore durante la connessione al sistema di rimborso, controlla la tua connessione nel tuo profilo", "concur": {"title": "SAP Concur", "authentication_url": "URL di autenticazione", "app_url": "URL Applicazione", "api_url": "URL API", "client_id": "ID Client", "client_secret": "Segreto Client", "payment_type_id": "ID Tipo Pagamento", "expense_type_code": "Codice Tipo Spesa", "policy_id": "Policy ID", "report_name": "Nome Report", "link_success": "Il tuo account è stato collegato con successo a Concur", "link_error": "Si è verificato un errore durante il collegamento del tuo account a Concur", "revoke_success": "La tua connessione con Concur è stata revocata con successo", "revoke_error": "Si è verificato un errore durante la revoca della tua connessione con Concur"}}, "pricing": {"title": "Pricing", "description": "L'interfaccia di tariffazione consente funzionalità di determinazione del prezzo per i fornitori di stazioni di ricarica.", "not_found": "Impostazioni di tariffazione non trovate", "create_success": "Le impostazioni di tariffazione sono state create con successo", "create_error": "Si è verificato un errore durante la creazione delle impostazioni di tariffazione", "update_success": "Le impostazioni di tariffazione sono state aggiornate correttamente", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni di tariffazione", "simple_pricing_title": "Modulo integrato tariffazione", "full_description": "Open E-Mobility fornisce un modulo per la tariffazione che permette una definizione flessibile della strategia di tariffazione per la tua compagnia", "pricing_currency_changed_title": "Modifica della valuta", "pricing_currency_changed_confirm": ["La valuta sta per essere modificata. Dopo il salvataggio, é richiesta una riconnessione.", "<strong> ATTENZIONE ! </strong>Una volta salvato, la valuta non potrà più essere modificata!", "Confermi che la valuta selezionata é corretta??"], "settings": {"title": "Impostazioni Pricing", "price": "Prezzo", "currency": "Valuta"}, "end_date_error": "La data deve essere superiore alla data valido-da", "valid_from": "<PERSON><PERSON>", "valid_to": "Valido a", "flat_fee": "<PERSON><PERSON><PERSON>", "energy": "Energia", "charging_time": "Tempo di ricarica", "parking_time": "Tempo di Parcheggio", "create_title": "Creazione definizione tariffazione", "pricing_definition_description": "Descrizione", "pricing_definition_name": "Nome Definizione tariffazione", "pricing_definition_creation_confirm": "Si desidera creare una definizione tariffazione?", "pricing_definition_creation_success": "La Definizione tariffazione é stata creata con successo", "pricing_definition_creation_error": "Si é verificato un errore creando una Definizione tariffazione, si prega di controllare i logs", "pricing_definition_site_admin_creation_error": "Si prega di fornire un sito legato alla definizione tariffazione", "pricing_definition_update_title": "Aggiornare Definizione tariffazione", "pricing_definition_update_confirm": "Si desidera aggiornare la definizione tariffazione?", "pricing_definition_update_success": "La Definizione tariffazione é stata aggiornata con successo", "pricing_definition_update_error": "Si é verificato un errore nell'aggiornamento della definizione tariffazione, si prega di controllare i logs", "pricing_definition_not_found": "La Definizione tariffazione non é stata trovata", "pricing_definition_delete_title": "Cancellare la Definizione tariffazione", "pricing_definition_delete_confirm": "Si desidera cancellare una definizione di tariffazione?", "pricing_definition_delete_success": "Definizione tariffazione é stata cancellata con successo", "pricing_definition_delete_error": "Si é verificato un errore durante la suppressione di una definizio di tariffazione, si prega di controllare i logs", "pricing_definition_error": "Si é verificato un errore nel recupero della definizione tariffazione, si prega di controllare i logs", "pricing_definition_time_range_error": "Tempo di inizio e fine devo essere diversi", "pricing_max_energy_error": "L'energia massima deve essere maggiore dell'energia minima", "pricing_max_duration_error": "La durata massima deve essere piu grande della durata minima", "connector_type": "Tipo di presa", "connector_power": "Alimentazione della presa", "value": "Valore", "unit": "Unità", "charging_station": "Stazione di Ricarica", "pricing_dimensions_title": "Dimensioni", "pricing_definition_title": "Definizione tariffazione", "step_size": "Taglia fase", "connector_power_unit": "kW", "flat_fee_unit": "{{currency}}/sessione", "energy_unit": "{{currency}}/kWh", "charging_time_unit": "{{currency}}/ora", "parking_time_unit": "{{currency}}/ora", "flat_fee_formatted_price": "{{price}}/sessione", "energy_formatted_price": "{{price}}/kWh", "charging_time_formatted_price": "{{price}}/ora", "parking_time_formatted_price": "{{price}}/ora", "energy_step_unit": "Wh", "time_step_unit": "Minuti", "restrictions_title": "Restrizioni", "restriction_max_energy": "Max energia", "restriction_max_energy_unit": "kWh", "restriction_min_energy": "Min energia", "restriction_min_energy_unit": "kWh", "restriction_max_duration": "<PERSON> durata", "restriction_max_duration_unit": "Minuti", "restriction_min_duration": "<PERSON> durata", "restriction_min_duration_unit": "Minuti", "restriction_time_range": "Intervallo di tempo", "restriction_start_time": "Data di inizio", "restriction_end_time": "Data di fine", "restriction_days_of_week": "<PERSON><PERSON><PERSON> settimana"}, "billing": {"description": "L'interfaccia di fatturazione consente funzionalità di fatturazione per i fornitori di stazioni di ricarica.", "deactivated_setting_message": "La fatturazione della session di carica é attiva, le impostazione di connessione non possono piu essere cambiate", "not_found": "Impostazioni di fatturazione non trovate", "not_properly_set": "Impostazioni di fatturazione non corrette", "create_success": "Le impostazioni di fatturazione sono state create con successo", "create_error": "Si è verificato un errore durante la creazione delle impostazioni di fatturazione", "update_success": "Le impostazioni di fatturazione sono state aggiornate con successo", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni di fatturazione", "check_connection": "<PERSON>lo <PERSON>", "connection_success": "La connessione al sistema di fatturazione è stata testata con successo", "connection_error": "Impossibile connettersi al sistema di fatturazione. Controlla la chiave segreta.", "force_synchronize": "Forza Sincronizzazione Fatturazione", "payment_methods_create_title": "C<PERSON>re un metodo di pagamento", "payment_methods_create_success": "Il metodo di pagamento che termina con '{{last4}}' é stato creato con successo", "payment_methods_create_error": "Si é verificato un errore creando il metodo di pagamento, si prega di controllare i logs", "payment_methods_create_error_card_declined": "La tua carta é stata rifiutata, si prega di fornire un metodo di pagamento valido", "payment_methods_card_declined": "<PERSON>ta rifiutata", "payment_methods_delete_title": "Eliminare metodo di pagamento", "payment_methods_delete_confirm": "Si desidera eliminare metodo di pagamento che finise con '{{last4}}'?", "payment_methods_delete_success": "Il metodo di pagamento che termina con '{{last4}}' é stato eliminato con successo", "payment_methods_delete_error": "Si é verificato un errore durante l'eliminazione del metodo di pagamento, si prega di controllare i logs", "payment_methods_expired": "Scaduto", "payment_methods_valid": "Valido", "payment_methods_expire_soon": "Scade a breve", "payment_methods_expiring_on": "Scade il", "payment_methods_ending_with": "Termina con", "payment_methods_status": "Stato", "payment_methods_type": "Tipo", "payment_methods_brand": "Brand", "payment_methods_card": "Carta", "payment_methods_verification_code": "Codice di verifica", "payment_methods_conditions": "Per ragioni di sicurezza, non memorizziamo, ne processiamo direttamente i dettagli della tua carta di credito o della tua banca. Le informazioni sono inviate via un canale di comunicazone sicure al nostro partner STRIPE che fornisce le infrastrutture di fatturazine e pagamento.", "payment_methods_check": "Ho letto i termini di licenza per l'utilizzatore e accetto che dettagli della mia banca siano usati per i pagamenti futuri delle sessioni di ricarica.", "payment_methods_card_number_error": "Numero di carta non valido", "payment_methods_expiration_date_error": "Data di scadenza non valida", "payment_methods_cvc_error": "Codice di verifica non valido", "transaction_billing_activation": "Attivazione Fatturazione", "transaction_billing_activation_title": "Attivazione Fatturazione", "transaction_billing_activation_confirm": ["Si desidera attivare la fatturazione delle sessioni di ricarica? ", "<strong> ATTENZIONE ! </strong> Una volta attivata non sarà più possibile disattivarla !"], "transaction_billing_activation_success": "La fatturazione delle sessioni di ricarica é stata attivata con successo", "transaction_billing_activation_error": "La fatturazione delle sessioni di ricarica non può essere attivata", "billing_clear_test_data": "Cancellare dati di test", "billing_clear_test_data_title": "Cancellazione dati di test", "billing_clear_test_data_confirm": "Si desidera cancellare tutti i dati di test per la fatturazione?", "billing_clear_test_data_success": "I dati di test sono stati cancellati", "billing_clear_test_data_error": "Operazione fallita. La cancellazione dei dati di test é stata annullata", "payment_methods": "Metodi di pagamento", "user": {"force_synchronize_user_dialog_title": "Sincronizza Utente Fatturazione", "force_synchronize_user_dialog_confirm": "Vuoi veramente sincronizzare  l'utente '{{userFullName}}' nel sistema di fatturazione?", "force_synchronize_user_success": "L'utente '{{userFullName}}' è stato sincronizzato con successo", "force_synchronize_user_failure": "Sincronizzazione utente fallita, controlla i log"}, "stripe": {"description": "E-Mobility consente l'invio di fatture agli utenti delle stazioni di ricarica con Stripe...", "title": "Stripe", "url": "URL della Dashboard Stripe", "public_key": "Chiave API Pubblicabile", "invalid_public_key": "La chiave API Pubblicabile non è plausibile", "secret_key": "Chiave API Segreta", "invalid_secret_key": "La Chiave API Segreta non è plausible", "billing_method": "<PERSON><PERSON><PERSON>", "immediate_billing": "Fatturazione Immediata", "periodic_billing": "Fatturazione Periodica", "tax": "Tassa", "platform_fee_tax": "Piattaforma tassa", "no_tax": "<PERSON><PERSON><PERSON> tassa"}, "tap": {"title": "tap", "url": "URL de su cueanta en Tap", "public_key_web": "Publishable API Key Web", "invalid_public_key_web": "Publishable API key Web is not plausible", "public_key": "<PERSON><PERSON>e publicable", "invalid_public_key": "La clave publicable no es plausible", "secret_key": "Clave secreta", "invalid_secret_key": "La clave secreta no es plausible", "billing_method": "Método de facturación", "immediate_billing": "Facturación inmediata", "periodic_billing": "Facturación periódica", "tax": "Impuesto", "platform_fee_tax": "Platform Fee Tax", "no_tax": "Sin impuestos"}}, "analytics": {"sac": {"title": "SAP Analytics", "settings": "Impostazioni"}, "description": ["E-Mobility espone i suoi dati ad uno strumento di analisi dati esterno.", "Questa funzione espone in tempo reale tutti i dati della sessione attraverso un connettore a strumenti esterni di analisi dei dati.", "E-Mobility sta attualmente usando SAP Analytics Cloud per mezzo di un connettore OData.", "SAP Analytics Cloud supporta l'uso intelligente dei dati di ricarica per semplificare il processo decisionale operativo.", "Ulteriori informazioni tecniche disponibili all'indirizzo"], "setting_do_not_exist": "La configurazione non è stata trovata", "setting_not_found": "Le impostazioni di SAP Analytics non sono state trovate", "create_success": "Le impostazioni di SAP Analytics Cloud sono state create con successo", "create_error": "Si è verificato un errore durante la creazione delle impostazioni di SAP Analytics Cloud", "update_success": "Le impostazioni di SAP Analytics Cloud sono state aggiornate con successo", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni di SAP Analytics Cloud"}, "smart_charging": {"sap_smart_charging": {"title": "Ricarica Smart", "settings": "Impostazioni", "user": "Utente ", "password": "Password", "additional_settings": "Altre impostazioni:", "sticky_limitation": "Regolare il limite in base al consumo di corrente", "limit_buffer_dc": "Buffer per limite DC (%)", "limit_buffer_ac": "Buffer per limite AC (%)"}, "description": "E-Mobility può utilizzare l'algoritmo di ricarica intelligente per ottimizzare la distribuzione dell'energia e i programmi di ricarica sulla base di vincoli complessi", "setting_do_not_exist": "La configurazione non è stata trovata", "setting_not_found": "Le impostazioni della ricarica smart SAP non sono state trovate", "create_success": "Le impostazioni di Ricarica Smart SAP sono state create con successo", "create_error": "Si è verificato un errore durante la creazione delle impostazioni di Ricarica Smart SAP", "update_success": "Le impostazioni di Ricarica Smart SAP sono state aggiornate con successo", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni della Ricarica Smart SAP", "connection_success": "La connessione al servizion di ricarica smart è stata testata con successo", "connection_error": "Impossible connettersi al servizio di ricarica smart. Controlla la configurazione.", "check_connection": "<PERSON>lo <PERSON>"}, "asset": {"connection": {"title": "Connessione risorsa", "name": "Nome", "description": "Descrizione", "type": "Tipo", "refreshIntervalMins": "Intervallo di aggiornamento (mins)", "base_url": "URL", "user": "Utente", "password": "Password", "client_id": "Client ID", "client_secret": "Client Secret", "authentication_url": "URL d'autenticazione", "delete_title": "Cancella connessione Risorsa", "delete_confirm": "Vuoi veramente cancellare la connessione '{{assetConnectionName}}'?"}, "types": {"schneider": "Sistema Gestione Edifici Schneider", "greencom": "Sistema Gestion GreenCom Asset", "iothink": "Sistema Gestion ioThink Asset", "wit": "Sistema Gestion WIT Asset", "lacroix": "Sistema Gestion LACROIX Asset"}, "connection_success": "La connessione al sistema è stata testata con successo", "unknown_connection_error": "Errore di connessione sconosciuto", "invalid_grant": "Utente o Password errati", "connection_failed": "Connessione fallita", "description": "E-Mobility può gestire il consumo di risorse e utilizzare queste informazioni con l'algoritmo di ricarica intelligente per bilanciare i consumi della stazione di ricarica per una determinata area del sito.", "setting_do_not_exist": "La configurazione non è stata trovata", "setting_not_found": "Le impostazioni della risorsa non sono state trovate", "create_success": "Le impostazioni della risorsa sono state create con successo", "create_error": "Si è verificato un errore durante la creazione delle impostazioni della risorsa", "update_success": "Le impostazioni della risorsa sono state aggiornate con successo", "update_error": "Si è verificato un errore durante l'aggiornamento delle impostazioni della Risorsa"}, "car_connector": {"connection": {"title": "Prese auto", "name": "Nome", "description": "Descrizione", "type": "Tipo", "authentication_url": "URL d'autenticazione", "api_url": "URL API", "client_id": "Client ID", "client_secret": "Client Secret", "delete_title": "Cancella presa", "delete_confirm": "Si desidera cancellare la presa '{{carConnectorConnectionName}}'?"}, "types": {"mercedes": "Presa Mercedes", "tronity": "Presa Tronity", "targa_telematics": "Targa Telematics Connector"}, "mercedes": {"link_success": "Il tuo account è stato collegato con successo a Mercedes", "link_error": "Si é verificato un errore durante il collegamento del tuo account con Mercedes, si prega di controllare i logs", "revoke_success": "La tua connessione con Mercedes è stata revocata con successo", "revoke_error": "Si é verificato un errore durante la revoca della tua connessione con Mercedes, si prega di controllare i logs"}, "description": "E-Mobility può utilizzare i dati real time del tuo veicolo come, ad esempio, lo stato della ricarica e utilizzare le informazioni con l'algoritmo di ricarica smart per ottimizzare le sessioni di ricarica", "setting_do_not_exist": "La configurazione non é stata trovata", "setting_not_found": "Le impostazioni della presa del veicolo non sono state trovate", "create_success": "Le impostazioni della presa del veicolo sono state create con successo", "create_error": "Si é verificato un errore durante la creazione della impostazioni della presa del veicolo, si prega di controllare i logs", "update_success": "Le impostazioni della presa del veicolo sono state aggiornate con successo", "update_error": "Si é verificato un errore durante l'aggiornalento della impostazioni della presa del veicolo, si prega di controllare i logs"}, "taxes": {"title": "Taxes", "name": "Name", "rate": "Rate", "status": "Status", "api_url": "API Url", "taxes_not_found": "Taxes not found", "create_success": "The Tax '{{name}}' has been created successfully", "create_error": "Error occurred while creating the Tax, check the logs", "update_success": "The Tax '{{name}}' has been updated successfully", "update_error": "Error occurred while updating the Tax, check the logs", "delete_title": "Delete Tax", "delete_confirm": "Do you really want to delete the Tax '{{name}}'?", "delete_success": "The Tax '{{name}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Tax, check the logs"}, "information": "Informazioni", "activation_contact_msg": "Per abilitare questa funzione, si prega di contattare il tuo rappresentante E-Mobility.", "car": {"synchronize_car_catalogs": "Sincronizza Vetture", "synchronize_car_catalogs_dialog_title": "Sincronizza Vetture", "synchronize_car_catalogs_dialog_confirm": "Vuoi veramente avviare la sincronizzazione di tutti i veicoli elettrici?", "assign_user_to_car_dialog_title": "Assegna utente a vettura", "assign_user_to_car_dialog_confirm": "Questo veicolo esiste già, vuoi veramente riutiliz<PERSON>lo?"}}, "technical_settings": {"tabs": {"crypto": "Crittografia", "users": "<PERSON><PERSON><PERSON>", "organization": "Organizzazione"}, "crypto": {"crypto_key": {"title": "Impostazion di Crittografia", "subtitle": "Chiave di crittografia", "key": "Chiave", "block_cypher": "Crittografia a blocchi", "block_size": "Dimensione blocco", "operation_mode": "Modalità di funzionamento"}, "setting_do_not_exist": "Impostazioni tecniche di crittografia  not found", "update_success": "Impostazioni tecniche di crittografia aggiornate con successo", "update_error": "Si é verificato un errore nell'aggiornamento delle impostazioni tecniche di crittografia, si prega di controllare i logs", "crypto_check_error": "La verifica di crittografia é fallita, controlla le tue impostazioni", "crypto_key_length_error": "La lunghezza della chiave di crittografia non é valida, controlla le tue impostazioni", "crypto_algorithm_error": "L'algoritmo di crittografia non é supportato, controlla le tue impostazioni", "crypto_migration_in_progress_error": "Le impostazioni di crittografia non possono essere modificate quando la migrazione é in corso"}, "user": {"title": "Impostazioni Account", "auto_activation": "Attivare automaticamente un nuovo utente durante la registrazione", "update_success": "Le impostazioni utente sono state aggiornate con successo", "update_error": "Si é verificato un errore durante l'aggiornamento delle impostazioni utente, si prega di controllare i logs", "setting_do_not_exist": "Impostazioni utente non trovate"}}, "payment_cards": {"payment_cards_delete_title": "Delete Payment Cards", "payment_cards_delete_confirm": "Do you really want to delete the Payment Cards?", "payment_cards_delete_success": "Payment Cards has been deleted successfully", "payment_cards_delete_error": "Error occurred while deleting a Payment Cards, check the logs"}, "ocpiendpoints": {"platform_fee": "Platform Fee", "name": "Nome", "base_url": "URL", "role": "<PERSON><PERSON><PERSON>", "country_code": "<PERSON><PERSON>", "party_id": "Party ID", "status": "Stato", "version": "Versione", "local_token": "Token Locale", "token": "<PERSON><PERSON>", "new": "Nuovo", "registered": "Registrato", "unregistered": "Non Registrato", "last_patch_job_on": "Data Ultimo Aggiornamento", "patch_job_last_status": "Stato Ultimo <PERSON>", "patch_job_status": "Stato Job", "patch_job_result": "Successo / Totale", "patch_job_checkbox": "Attiva Job Aggiornamento Stato EVSE", "status_active": "Attivo", "status_inactive": "Inattivo", "generate_new_token": "Genera un nuovo token", "test_connection": "Test Connessione", "create_success": "L'IOP '{{name}}' è stato creato con successo", "create_error": "Si è verificato un errore durante la creazione dell'IOP", "update_success": "L'IOP '{{name}}' è stato aggiornato con successo", "update_error": "Si è verificato un errore durante l'aggiornamento dell'IOP", "delete_confirm": "Vuoi veramente cancellare l'IOP '{{name}}'?", "delete_title": "Cancella IOP", "delete_success": "L'IOP '{{name}}' è stato cancellato con successo", "delete_error": "Si è verificato un errore durante la cancellazione dell'IOP", "register_confirm": "Vuoi veramente avviare il processo di registrazione con l'IOP '{{name}}'?", "unregister_confirm": "Vuoi veramente avviare il processo di annullamento della registrazione dell'IOP '{{name}}'?", "register_title": "Registra IOP", "unregister_title": "Annulla Registrazione IOP", "register_success": "L'IOP '{{name}}' è stato registrato con successo", "unregister_success": "La registrazione dell'IOP '{{name}}' è stata annullata con successo", "register_error": "Si è verificato un errore durante la registrazione dell'IOP", "unregister_error": "Si è verificato un errore durante l'annullamento della registrazione dell'IOP", "update_credentials_title": "Aggiornare credenziali IOP", "update_credentials_confirm": "Vuoi veramente aggiornare le credenziali IOP '{{name}}'?", "update_credentials_success": "Le credenziali IOP '{{name}}' sono state aggiornate con successo", "update_credentials_error": "Si é verificato un errore durante l'aggiornamento delle credenziali IOP, si prega di controllare i logs", "push_evse_statuses_title": "Invia Stati EVSE", "push_tokens_title": "Invia Token Utente", "push_evse_statuses_confirm": "Vuoi veramente inviare tutti gli stati EVSE all'IOP '{{name}}'?", "push_tokens_confirm": "Vuoi veramente inviare tutti i token utente all'IOP '{{name}}'?", "trigger_jobs_title": "<PERSON><PERSON><PERSON><PERSON>", "trigger_jobs_confirm": "Vuoi veramente avviare tutti i job dell'IOP '{{name}}'?", "trigger_ocpi_action": "L'azione è stata inviata correttamente e l'elaborazione potrebbe richiedere del tempo", "ocpi_action_in_progress": "Non si può avviare più volte la stessa azione, una é già in esecuzione in background", "pull_locations_title": "Recupera Postazioni", "pull_locations_confirm": "Vuoi veramente recuperare tutte le postazioni dall'IOP '{{name}}'?", "pull_locations_success": "La modifica é stata accettata e le destinazioni saranno recuperate in maniera asincrona", "pull_locations_error": "Si è verificato un errore durante l'aggiornamento delle postazioni dell'IOP - controlla i log per maggiori dettagli", "get_sessions_title": "Recupera Sessioni", "get_sessions_confirm": "Vuoi veramente recuperare tutte le sessioni dall'IOP '{{name}}'?", "get_sessions_success": "La modifica é stata accettata e le sessioni saranno recuperate in maniera asincrona", "get_sessions_error": "Si è verificato un errore durante l'aggiornamento delle sessioni IOP - controlla i log per maggiori dettagli", "pull_tokens_title": "Recupera <PERSON>", "pull_tokens_confirm": "Vuoi veramente recuperare tutti i token dall'IOP '{{name}}'?", "pull_tokens_success": "La modifica é stata accettata e i token saranno recuperati in maniera asincrona", "pull_tokens_error": "Si è verificato un errore durante l'aggiornamento dei token IOP - controlla i log per maggiori dettagli", "pull_cdrs_title": "Recupera Rendiconti Sessioni Ricarica", "pull_cdrs_confirm": "Vuoi veramente recuperare i rendiconti di tutte le sessioni di ricarica dall'IOP '{{name}}'?", "pull_cdrs_success": "La modifica é stata accettata e i dettagli di ricarica saranno recuperati in maniera asincrona", "pull_cdrs_error": "Si è verificato un errore durante l'aggiornamento dei rendiconti di sessioni di ricarica dell'IOP - controlla i log per maggiori dettagli", "pull_tariffs_title": "Pull Tariffs Detail Records", "pull_tariffs_confirm": "Do you want to retrieve all Tariffs detail records from IOP '{{name}}'?", "pull_tariffs_success": "The command has been accepted and the Tariffs detail record(s) will be pulled asynchronously", "pull_tariffs_error": "Error occurred while updating the IOP Tariffs detail records, check the logs", "check_cdrs_title": "Controlla Rendiconti Sessioni Ricarica", "check_cdrs_confirm": "Vuoi veramente controllare tutti i rendiconti di sessioni di ricarica dell'IOP '{{name}}'?", "check_cdrs_success": "La modifica é stata accettata e i dettagli di ricarica saranno verificati in maniera asincrona", "check_cdrs_error": "Si è verificato un errore durante il controllo dei rendiconti di sessioni di ricarica dell'IOP - controlla i log per maggiori dettagli", "check_sessions_title": "Verifica Sessioni", "check_sessions_confirm": "Vuoi veramente verificare tutte le sessioni con IOP '{{name}}'?", "check_sessions_success": "La modifica é stata accettata e le sessioni saranno verificate in maniera asincrona", "check_sessions_error": "Si è verificato un errore durante il controllo delle sessioni IOP - controlla i log per maggiori dettagli", "check_locations_title": "Verifica Postazioni", "check_locations_confirm": "Vuoi veramente controllare tutte le postazioni con IOP '{{name}}'?", "check_locations_success": "La modifica é stata accettata e le destinazioni saranno verificate in maniera asincrona", "check_locations_error": "Si è verificato un errore durante il controllo delle postazioni IOP - controlla i log per maggiori dettagli", "trigger_jobs_error": "Si è verificato un errore durante l'esecuzione dei job - controlla i log per maggiori dettagli", "success_ping": "Ping eseguito con successo", "error_ping_401": "Ping fallito: Non Autorizzato", "error_ping_404": "Ping fallito: <PERSON> Trovato", "error_ping_412": "Ping fallito: Risposta Non Valida", "error_ping": "Errore Ping", "error_generate_local_token": "Si è verificato un errore durante la generazione di un nuovo token locale", "push_evse_statuses_success": "La modifica é stata accettata e gli stati EVSE saranno trasmessi in maniera asincrona", "push_tokens_success": "La modifica é stata accettata e i token saranno trasmessi in maniera asincrona", "push_evse_statuses_error": "Si è verificato un errore durante l'aggiornamento degli stati EVSE - controlla i log per maggiori dettagli", "push_tokens_error": "Si è verificato un errore durante l'aggiornamento dei token utente - controlla i log per maggiori dettagli", "start_stop_job": "Abilita/Disabilita job in background", "background_job_activated": "Job di background attivati con successo", "background_job_deactivated": "Job di background disattivati con successo", "background_job_no_run": "Non eseguito", "start_background_job_title": "<PERSON><PERSON><PERSON>", "start_background_job_confirm": "Vuoi veramente avviare il job di background per l'IOP '{{name}}'?", "stop_background_job_title": "Interrompi Job di Background", "stop_background_job_confirm": "Vuoi veramente interrompere il job di background per l'IOP '{{name}}'?", "total_charge_points": "Stazione di Ricaricas", "total_tokens": "Carte RFID", "total_locations": "<PERSON><PERSON>", "total": "Totale", "succeeded": "Successo", "failed": "Fallito"}, "ocpi": {"title": "Roaming OCPI", "type_placeholder": "Tipo", "roaming_platforms": "Piattaforma Interoperabilità", "cpo": "Operatore dei punti di ricarica", "enable_cpo": "Abilita operatore dei punti di ricarica", "emsp": "Fornitore del servizio E-Mobility", "enable_emsp": "Abilita fornitore del servizio E-Mobility", "tariff_id": "ID tariffa", "connector_id": "Connector ID", "remarks": "Remarks", "owner_name": "Nome del proprietario", "description": "Interfaccia OCP(open charge point)", "details": "Identificazione", "country_code": "<PERSON>dice paese", "party_id": "Party ID", "business_details": "Dettagli Business", "logo": "<PERSON><PERSON><PERSON><PERSON>", "sync_all": "Sincroniz<PERSON>", "push_evse_statuses": "Invio stati", "push_tokens": "Invio <PERSON>", "pull_cdrs": "Recupero Rendiconti Sessioni Ricarica (CDR)", "pull_tariffs": "Pull Tariffs", "pull_locations": "Recupero Postazioni", "pull_sessions": "Recupero Sessioni", "pull_tokens": "Recupero <PERSON>", "check_locations": "<PERSON>lo <PERSON>", "check_sessions": "Controllo Sessioni", "check_cdrs": "Controllo Rendiconti Sessioni Ricarica (CDR)", "update_credentials": "Update Credentials", "businessdetails": {"name": "Nome dell'operatore", "website": "Link al sito dell'operatore", "logo": {"url": "URL dell'immagine in scala reale", "thumbnail": "URL dell'immagine di anteprima", "category": "Categoria dell'immagine", "type": "Tipo immagine (gif, jpeg, png, svg,...)", "width": "Larghezza dell'immagine a scala intera", "height": "Altezza dell'immagine a scala intera"}}}, "oicpendpoints": {"name": "Name", "base_url": "URL", "role": "<PERSON><PERSON><PERSON>", "country_code": "<PERSON><PERSON>", "party_id": "Party ID", "status": "Stato", "version": "Versione", "new": "Nuovo", "registered": "Registrato", "unregistered": "Non Registrato", "last_patch_job_on": "Data ultimo aggiornamento", "patch_job_last_status": "Ultimo invio stati", "patch_job_status": "Stato Job", "patch_job_result": "Successo / Totale", "patch_job_checkbox": "Attiva Job Aggiornamento Stato EVSE", "status_active": "Attivo", "status_inactive": "Inattivo", "test_connection": "Test Connessione", "create_success": "'{{name}}' é stato creato con successo", "create_error": "Si é verificato un errore durante la creazione dell'IOP, si prega di controllare i logs", "update_success": "'{{name}}' é stato aggiornato con successo", "update_error": "Si é verificato un errore durante l'aggiornamento dell'IOP, si prega di controllare i logs", "delete_confirm": "Si desidera cancellare l'IOP '{{name}}'?", "delete_title": "Can<PERSON><PERSON>", "delete_success": "'{{name}}' é stato cancellato con successo", "delete_error": "Si é verificato un errore nella cancellazione, si prega di controllare i logs", "register_confirm": "Si desidera avviare una procedura di registrazione con l'IOP '{{name}}'?", "unregister_confirm": "Si desidera annullare la registrazione con l'IOP '{{name}}'?", "register_title": "Registra IOP", "unregister_title": "Annulla Registrazione IOP", "register_success": "'{{name}}' é stato registrato con successo", "unregister_success": "'{{name}}' é stato deregistrato con successo", "register_error": "Si é verificato un errore durante la registrazione, si prega di controllare i logs", "unregister_error": "Si é verificato un errore durante la deregistrazione, si prega di controllare i logs", "push_evse_statuses_title": "Invio stati EVSE", "push_evses_title": "Invio EVSEs", "push_evse_statuses_confirm": "Si desidera inviare tutti gli stati EVSE all'Hubject '{{name}}'?", "push_evses_confirm": "Si desidera inviare tutti gli EVSEs all'Hubject '{{name}}'?", "trigger_jobs_title": "<PERSON><PERSON><PERSON><PERSON>", "trigger_jobs_confirm": "Si desidera eseguire tutti i processi dell'IOP '{{name}}'?", "trigger_jobs_error": "Si é verificato un errore durante l'esecuzione dei jobs, si prega di controllare i logs", "success_ping": "Ping eseguito con successo", "error_ping_401": "Ping fallito: Non Autorizzato", "error_ping_404": "Ping fallito: <PERSON> Trovato", "error_ping_412": "Ping fallito: Risposta Non Valida", "error_ping": "<PERSON> fallito", "push_evse_statuses_success": "Lo stato di {{success}} EVSE é stato aggiornato con successo", "push_evse_statuses_partial": "Lo stato {{success}} EVSE(s) é stato aggiornato - {{error}} EVSE(s) non aggiornato, si prega di controllare i logs", "push_evses_success": "{{success}} EVSEs aggiornati con successo", "push_evses_partial": "{{success}} EVSE(s) aggiornati - {{error}} EVSE(s) non aggiornati, si prega di controllare i logs", "push_evse_statuses_error": "Si é verificato un errore durante l'aggiornamento degli stati EVSE, si prega di controllare i logs", "push_evses_error": "Si é verificato un errore durante l'aggiornamento degli EVSEs, si prega di controllare i logs", "start_stop_job": "Abilita/Disabilita job in background", "background_job_activated": "Job di background attivati con successo", "background_job_deactivated": "Job di background disattivati con successo", "background_job_no_run": "Non eseguito", "start_background_job_title": "<PERSON><PERSON><PERSON>", "start_background_job_confirm": "Si desidera avvia il job '{{name}}' in background?", "stop_background_job_title": "Interrompi Job di Background", "stop_background_job_confirm": "Si desidera interrompero il job '{{name}}' in background?", "total_charge_points": "Totale stazioni di ricarica", "total": "Totale", "succeeded": "Successo", "failed": "Fallito"}, "oicp": {"title": "Roaming Hubject (OICP)", "type_placeholder": "Tipo", "roaming_platforms": "Piattaforma Interoperabilità", "cpo": "Identificazione Operatore dei punti di ricarica", "emsp": "Identificazione Fornitore del serviwio E-Mobility", "description": "Protocollo Open InterCharge", "details": "Identificazione", "country_code": "<PERSON><PERSON>", "party_id": "Party ID", "business_details": "Dettagli Business", "logo": "<PERSON><PERSON><PERSON><PERSON>", "key": "Chiave", "cert": "Certificato", "sync_all": "Sincroniz<PERSON>", "push_evses": "Trasmetti dati EVSE", "push_evses_statuses": "Trasmetti stati EVSE", "businessdetails": {"name": "Nome dell'operatore", "website": "Link al sito dell'operatore", "logo": {"url": "URL dell'immagine in scala reale", "thumbnail": "URL dell'immagine di anteprima", "category": "Categoria dell'immagine", "type": "Tipo immagine (gif, jpeg, png, svg,...)", "width": "Larghezza dell'immagine a scala intera", "height": "Altezza dell'immagine a scala intera"}}}, "organization": {"title": "Organizzazione", "description": "Gestione Organizzazione: Aziende, Siti e Aree Sito", "tabs": {"companies": "Aziende", "sites": "<PERSON><PERSON>", "siteareas": "<PERSON><PERSON>"}, "graph": {"power": "Potenza Istantanea (kW)", "no_consumption": "<PERSON><PERSON><PERSON> dato di consumo disponibile", "asset_consumption_watts": "Consumo asset (kW)", "asset_production_watts": "Produzione asset (kW)", "charging_station_consumption_watts": "Consumo Stazione di Ricarica (kW)", "net_consumption_watts": "<PERSON><PERSON><PERSON> netto (kW)", "limit_watts": "Limite (kW)", "asset_consumption_amps": "Consumo asset (A)", "asset_production_amps": "Produzione assetn (A)", "charging_station_consumption_amps": "Consumo Stazione di Ricarica(A)", "net_consumption_amps": "<PERSON><PERSON><PERSON> netto (A)", "limit_amps": "Limite (A)"}}, "asset": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Gestione Asset: <PERSON><PERSON><PERSON>, Siti e Aree Sito", "tabs": {"assets": "Risorse", "in_error": "In Errore"}, "graph": {"power": "Potenza Istantanea (kW)", "limit_watts": "Limite Rete (kW)", "no_consumption": "<PERSON><PERSON><PERSON> dato di consumo disponibile"}}, "cars": {"vin": "VIN", "license_plate": "Targa", "user": "Utente", "assign_user": "Assegna utente", "vehicle_model": "<PERSON><PERSON>", "vehicle_make": "Costruttore", "vehicle_model_version": "Versione", "battery_capacity_full": "Capacità Batteria", "fast_charge_speed": "Velocità di Carica", "performance_top_speed": "Massima Velocità", "range_real": "Autonomia Reale", "range_wltp": "Autonomia WLTP", "efficiency_real": "Efficienza", "performance_acceleration": "0-100 km/h", "image": "<PERSON><PERSON><PERSON><PERSON>", "drivetrain_propulsion": "Trazione", "drivetrain_torque": "Coppia Totale", "battery_capacity_useable": "Capacità Utilizzabile", "charge_plug": "Spina Corrente Alternata", "drivetrain_power_hp": "Potenza", "fast_charge_plug": "Spina Corrente Continua", "charge_plug_location": "Posizione Spina Ricarica", "charge_standard_power": "Potenza Corrente Alternata", "charge_alternative_power": "Potenza Corrente Alternata alternativa", "charge_option_power": "Opzione Potenza Corrente Alternata", "charge_standard_charge_speed": "Velocità Corrente Alternata", "charge_standard_charge_time": "Tempo Corrente Alternata", "fast_charge_power_max": "Potenza Corrente Continua", "misc_seats": "Posti a sedere", "misc_body": "Carrozzeria", "misc_isofix": "Isofix", "misc_turning_circle": "<PERSON><PERSON> Sterzo", "misc_segment": "Segmento", "misc_isofix_seats": "Posti a sedere Isofix", "miscellaneous": "Miscellanea", "battery": "Batteria", "debug_car": "<PERSON><PERSON><PERSON>", "charge_standard_tables": "Convertitori Corrente Alternata", "evse_phase_volt": "Voltaggio", "evse_phase_amp": "Amperaggio", "evse_phase": "Fase(i)", "evse_phase_ac_standard": "Fase(i) Corrente Alternata", "evse_phase_ac_alternative": "Fase(i) Corrente Alternata alternative", "evse_phase_ac_option": "Opzione Fase(i) Corrente Alternata", "charge_phase_volt": "Fase Ricarica Volt", "charge_phase_amp": "Fase Ricarica Ampere", "charge_phase": "Fase Ricarica", "charge_power": "Potenza Ricarica", "charge_time": "Tempo Ricarica", "charge_speed": "Velocità Ricarica", "synchronize_car_catalogs_success": "La richiesta é stata accetta e la sincronizzazione dei veicoli sera effettuata in maniera asincrona", "synchronize_car_catalogs_error": "Si é verificato un errore durante la sincronizzazione dei veicoli, si prega di verificare i logs", "synchronize_car_catalogs_ongoing": "La sincronizzazione dei veicoli é già in corso", "synchronize_car_catalogs_up_to_date": "Veicoli già aggiornati", "select_car_maker": "Seleziona Costruttore", "car_makers": "Co<PERSON><PERSON><PERSON><PERSON>", "create_success": "Il veicolo '{{carName}}' è stato creato con successo", "update_success": "Il veicolo '{{carName}}' è stato aggiornato con successo", "delete_success": "Il veicolo '{{carName}}' è stato cancellato con successo", "delete_error": "Si è verificato un errore durante la cancellazione del veicolo", "assign_car_catalog": "Assegna un modello del veicolo", "create_error": "Si è verificato un errore durante la creazione del veicolo", "update_error": "Si è verificato un errore durante l'aggiornamento del veicolo", "car_exist": "Veicolo già esistente", "user_not_owner": "Solo il proprietario può aggiornarlo (l'impostazione predefinita può essere modificata)", "car_exist_different_car_catalog": "Il veicolo esiste già con un modello differente", "user_already_assigned": "Hai già assegnato questo veicolo ad un utente", "company_car": "Auto Aziendale", "invalid_vin": "VIN Non Valido", "assign_users": "<PERSON><PERSON><PERSON>", "car_owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Tipo", "default_car": "<PERSON>ei<PERSON>lo <PERSON>", "private_car": "Veicolo Privato", "pool_car": "Auto di Gruppo", "remove_users_title": "Rimuovi Utente(i)", "remove_users_confirm": "Sei sicuro di voler rimuovere gli(lo) utenti(e) selezionati(o) da questo veicolo?", "assign_users_car_partial": "{{assigned}} utenti sono stati assegnati con successo e {{inError}} utenti hanno riscontrato un errore", "assign_users_car_success": "{{assigned}} utenti sono stati assegnati con successo", "assign_users_car_error": "Si è verificato un errore durante l'assegnazione degli utenti", "update_users_car_partial": "{{assigned}} utenti sono stati aggiornati con successo e {{inError}} hanno riscontrato un errore", "update_users_car_success": "{{assigned}} utenti sono stati aggiornati con successo", "update_users_car_error": "Si è verificato un errore durante l'aggiornamento degli utenti", "remove_users_car_partial": "{{assigned}} utenti sono stati rimossi con successo e {{inError}} hanno riscontrato un errore", "remove_users_car_success": "{{assigned}} utenti sono stati rimossi con successo", "remove_users_car_error": "Si è verificato un errore durante la rimozione degli utenti", "car_not_found": "Veicolo non trovato", "car_error": "Si è verificato un errore durante il recupero del veicolo", "car_user_error": "Si è verificato un errore durante il recupero degli utenti del veicolo", "assign_converter": "Assegna Converter", "converter": "Converter", "delete_title": "<PERSON><PERSON><PERSON>", "delete_confirm": "Vuoi veramente cancellare il veicolo '{{carName}}'?", "users": "<PERSON><PERSON><PERSON>", "select_car": "Seleziona Auto", "car_connector_name": "Connection Name", "car_connector_meter_id": "Connection Meter ID", "unit": {"meters": "m", "seconde": "sec", "secondes": "secs", "minutes": "mins", "newton_metre": "Nm", "horse_power": "hp", "kilowatt_heure": "kWh", "kilometer_per_hour": "km/h", "kilometer": "km", "kilowatt": "kW", "drivetrain_power_hp_unit": "hp"}}, "car": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Gestisci veicolo dell'utente", "tabs": {"car_catalogs": "Catalogo Veicoli", "cars": "<PERSON><PERSON><PERSON><PERSON>"}}, "car_connector": {"title": "Presa veicolo", "description": "Utilizzazione dei dati del veicolo in tempo reale"}, "analytics": {"title": "Analytics", "type_placeholder": "Tipo", "description": "Interfaccia Extended Analytics", "mainurl": "Home Page SAP Analytics Cloud", "timezone": "<PERSON>so orario usato in SAP Analytics Cloud", "links": "Link SAP Analytics Cloud", "set_link": "Imposta Link", "delete_title": "Cancella link", "delete_confirm": "Vuoi veramente cancellare il link '{{linkName}}'?", "link": {"name": "Nome", "description": "Descrizione", "role": "<PERSON><PERSON><PERSON> U<PERSON>", "url": "URL"}}, "smart_charging": {"title": "Ricarica Smart", "type_placeholder": "Tipo", "description": "Ricarica smart veicoli elettrici", "optimizerUrl": "URL dell'ottimizzatore"}, "sms": {"title": "SMS", "type_placeholder": "Type", "description": "SMS of electric vehicles", "username": "Api Id", "password": "Api Password", "Sender_password": "Sender Id", "smsType1": "Text Local", "smsType2": "sms Ala", "smsType3": "SmsGatewayHub", "setting_do_not_exist": "Configuration has not been found", "create_error": "Error occurred while creating the SMS settings, check the logs", "update_success": "SMS settings have been updated successfully", "update_error": "Error occurred while updating the SMS settings, check the logs", "create_success": "SMS settings have been created successfully", "description_blank": "CERO SMART MOBILITY can need of your sms like the current state of charge and see this information with the sms to your enhancement fuctionality."}, "smtp": {"title": "SMTP", "type_placeholder": "Type", "description": "SMTP for mail server", "from": "From", "host": "Host", "port": "Port", "secure": "Secure", "requireTLS": "Require TLS", "user": "User", "password": "Password", "smtpType1": "SMTP", "setting_do_not_exist": "Configuration has not been found", "create_error": "Error occurred while creating the SMTP settings, check the logs", "update_success": "SMTP settings have been updated successfully", "update_error": "Error occurred while updating the SMTP settings, check the logs", "create_success": "SMTP settings have been created successfully", "description_blank": "CERO SMART MOBILITY can need of your SMTP like the current state of charge and see this information with the SMTP to your enhancement fuctionality."}, "refund": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type_placeholder": "Tipo", "description": "Interfaccia Rimborso"}, "pricing": {"title": "Pricing", "type_placeholder": "Tipo", "description": "Interfaccia Pricing", "price_kw_h_label": "Prezzo al kW.h", "price_unit_label": "Valuta", "update_success": "Il prezzo è stato aggiornato con successo", "update_error": "Si è verificato un errore durante l'aggiornamento del prezzo"}, "billing": {"title": "Fatturazione", "type_placeholder": "Tipo", "description": "Interfaccia Fatturazione", "id": "ID Fatturazione", "updated_on": "Sincronizzato il", "tabs": {"invoices": "Fatture", "in_error": "In Errore"}}, "billingPlatform": {"title": "Piattaforma di fatturazione", "description": "Gestisci molteplici account di fatturazione"}, "transactions": {"id": "Id", "date_from": "Data", "duration": "<PERSON><PERSON>", "inactivity": "Inattività", "user": "Utente", "badge_id": "ID Badge", "connector": "Presa", "total_consumption": "Consu<PERSON>", "current_consumption": "Potenza", "state": "Stato", "price": "Prezzo", "taxedPrice": "Taxed Price", "state_of_charge": "Batteria", "date": "Data", "charging_station": "Stazione di Ricarica", "started_at": "Iniziato a", "end_date": "Data di Fine", "stop_reason": "Reason", "consumption": "Consu<PERSON>", "transactions_button": "Sessioni", "transaction_today": "<PERSON><PERSON><PERSON>", "transaction_one_week_ago": "Una settimana fa", "transaction_one_month_ago": "Un mese fa", "transaction_three_months_ago": "Tre mesi fa", "transaction_six_months_ago": "Sei mesi fa", "transaction_one_year_ago": "Un anno fa", "tab_current": "Attuale", "tab_history": "Storico", "stopped_by": "Interrotto Da", "soft_stop": "Interrompi", "soft_stop_transaction_title": "Interrompi Sessione (Modo Soft)", "soft_stop_transaction_confirm": "Vuoi veramente interrompere (in modalità soft) la sessione appartenente a '{{userFullName}}' su '{{chargeBoxID}}'?", "soft_stop_transaction_success": "La sessione è stata interrotta con successo. Puoi sbloccare il connettore", "soft_stop_transaction_error": "Si è verificato un errore durante l'interruzione della sessione", "rebuild_transaction_consumptions_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rebuild_transaction_consumptions_confirm": "Vuoi veramente ricostruire i tutti i consumi della sessione?", "rebuild_transaction_consumptions_success": "I consumi della sessione sono stati ricostruiti con successo", "rebuild_transaction_consumptions_error": "Si è verificato un errore durante la ricostruzione dei consumi della sessione", "delete": "Can<PERSON><PERSON>", "error_code": "Errore", "delete_transaction_title": "Cancella Sessione", "delete_transaction_confirm": "Vuoi veramente cancellare la sessione appartenente a '{{userFullName}}' su '{{chargeBoxID}}'?", "delete_transaction_success": "La sessione è stata cancellata con successo", "delete_transaction_error": "Si è verificato un errore durante la cancellazione della sessione", "delete_transactions_title": "Cancella Sessione(i)", "delete_transactions_confirm": "Vuoi veramente cancellare {{quantity}} sessione(i)?", "delete_transactions_success": "{{inSuccess}} sessioni sono state cancellate con successo", "delete_transactions_partial": "{{inSuccess}} sessioni sono state cancellate con successo e {{inError}} sessioni hanno riscontrato un errore", "delete_transactions_error": "{{inError}} sessioni non sono state cancellate, si prega di controllare i logs", "delete_transactions_unexpected_error": "Si é verificato un errore inatteso durante la cancellazione delle sessioni, si prega di controllare i logs", "delete_no_transaction": "Nessuna sessione é stata cancellata", "export_ocpi_cdr_button_title": "Esporta Cdr", "export_ocpi_cdr_title": "Esporta Cdr della transazione", "export_ocpi_cdr_confirm": "Vuoi davvero esportare il Cdr della Sessione '{{transactionID}}'", "export_ocpi_cdr_error": "Si è verificato un errore durante l'esportazione del Cdr della Sessione '{{transactionID}}'", "inactivity_info": "Tutte le Inattività", "inactivity_warning": "Inattività Medie", "inactivity_error": "Inattività Elevate", "refund": "<PERSON><PERSON><PERSON><PERSON>", "refund_transaction_title": "<PERSON><PERSON>", "refund_transaction_confirm": "Vuoi veramente rimborsare la sessione di '{{userFullName}}' su '{{chargeBoxID}}'?", "refund_transaction_success": "La sessione è stata rimborsata con successo", "refund_transaction_error": "Si è verificato un errore durante il rimborso della sessione", "refund_undefined": "Non Inviato", "refund_approved": "A<PERSON>rovato", "refund_cancelled": "Cancellato", "refund_submitted": "Inviato", "transaction_number": "Sessione(i)", "total_consumption_kw": "Consumo Totale", "transaction_not_found": "La sessione non è stata trovata", "transaction_id_not_found": "L'ID di sessione '{{sessionID}}' non è stato trovato", "load_transactions_error": "Si è verificato un errore durante il recupero delle sessioni", "load_transaction_error": "Si è verificato un errore durante il recupero della sessione", "total_duration": "<PERSON><PERSON>", "refund_transactions": "Rimborsato ", "pending_transactions": "In Sospeso ", "count_refunded_reports": "Report", "hours": "ora(e)", "mins": "minuto(i)", "hour_short": "h", "no_active_transaction": "Nessuna sessione attiva", "no_history": "<PERSON><PERSON><PERSON> dato storico durante questo periodo", "refundDate": "Trasferito Il", "reportId": "ID Report", "select_report": "Seleziona Report", "create_invoice_success": "La fattura è stata creata correttamente per questa sessione", "create_invoice_error": "Si è verificato un errore durante la creazione di una fattura per questa sessione ", "redirect": "<PERSON>ai alle Sessioni", "error_start_no_payment_method": "<PERSON><PERSON><PERSON> in<PERSON>po<PERSON>, si prega di aggiungere al profilo un metodo di pagamento.", "error_start_general": "Ser<PERSON>zio temporaneamente indisponibile, si prega di contattare il supporto.", "filter": {"type": {"name": "Tipo", "refunded": "Rimborsato", "not_refunded": "Non Rimborsato"}}, "tabs": {"history": "Storico", "in_progress": "In Corso", "in_error": "In Errore", "refund": "<PERSON><PERSON><PERSON><PERSON>", "invoices": "Fatture"}, "graph": {"battery": "Batteria (%)", "power": "Potenza Rete Elettrica (kW)", "power_l1": "Potenza Rete Elettrica L1 (kW)", "power_l2": "Potenza Rete Elettrica L2 (kW)", "power_l3": "PotenzaRete Elettrica L3 (kW)", "amps": "Ampere Rete Elettrica (A)", "amps_l1": "Ampere Rete Elettrica L1 (A)", "amps_l2": "Ampere Rete Elettrica L2 (A)", "amps_l3": "Ampere Rete Elettrica L3 (A)", "energy": "Energia Erogata (kW.h)", "energy_amps": "Energia Erogata (A.h)", "reset_zoom": "Reset", "no_consumption": "<PERSON><PERSON><PERSON> dato di consumo disponibile", "cumulated_amount": "Ammontare", "amperage": "Amperaggio (A)", "amperage_dc": "Amperaggio DC (A)", "amperage_l1": "Amperaggio L1 (A)", "amperage_l2": "Amperaggio L2 (A)", "amperage_l3": "Amperaggio L3 (A)", "voltage": "Voltaggio (V)", "voltage_dc": "Voltaggio DC (V)", "voltage_l1": "Voltaggio L1 (V)", "voltage_l2": "Voltaggio L2 (V)", "voltage_l3": "Voltaggio L3 (V)", "plan_watts": "Connector Limit (kW)", "plan_amps": "Connector Limit (A)", "limit_plan_watts": "Limite Piano (kW)", "limit_plan_amps": "Limite Piano (A)", "limit": "Limite", "limit_watts": "Limite Rete Elettrica (kW)", "limit_amps": "Limite Rete Elettrica (A)", "load_all_consumptions": "Mostra risoluzione massima del grafico", "unit_kilowatts": "kW", "unit_amperage": "Amp"}, "dialog": {"delete": {"title": "Cancella Sessione", "confirm": "Vuoi veramente cancellare la sessione di {{user}} ?", "rejected_refunded_msg": "Non puoi cancellare una sessione che è già stata rimborsata!", "rejected_billed_msg": "Non puoi cancellare una sessione che è già stata fatturata!"}, "export": {"title": "Esporta Sessioni", "confirm": "Vuoi veramente esportare tutte le sessioni corrispondenti ai filtri?", "error": "Si è verificato un errore durante l'esportazione delle sessioni"}, "soft_stop": {"title": "Interrompi Sessione", "confirm": "Vuoi veramente interrompere la sessione di {{user}}?"}, "refund": {"title": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Vuoi veramente trasferire {{quantity}} sessioni(e) al tuo sistema di rimborso?"}, "roaming": {"title": "Roaming", "confirm": "Vuoi davvero inviare il CDR dell'ID di sessione '{{sessionID}}' alla piattaforma di roaming?"}, "create_invoice": {"title": "<PERSON>rea <PERSON>", "confirm": "Vuoi veramente creare una fattura per questa sessione?"}, "session": {"tooltips": {"amount": "Ammontare", "start_date": "Data Inizio", "total_consumption": "Consumo Totale", "current_consumption": "<PERSON><PERSON><PERSON>", "state_of_charge": "Livello Batteria", "total_duration": "<PERSON><PERSON>", "inactivity": "Durata inattività"}, "pricing_detail_description": "Descrizione", "pricing_detail_entity_type": "Entità", "pricing_detail_entity_id": "ID entità", "pricing_detail_entity_name": "Nome entità", "pricing_detail_unit_price": "Unita prezzo", "pricing_detail_consumption": "Consu<PERSON>", "pricing_detail_duration": "<PERSON><PERSON>", "pricing_detail_amount": "Ammontare", "pricing_detail_view_all": "<PERSON><PERSON>za tutto"}}, "notification": {"delete": {"success": "La sessione dell'utente {{user}} è stata cancellata con successo", "error": "Si è verificato un errore durante la cancellazione della sessione"}, "roaming": {"success": "Il CDR dell'ID di sessione '{{sessionID}}' è stato inviato correttamente alla piattaforma di roaming", "error": "Si è verificato un errore durante il l'invio del CDR, controllare i log", "error_not_from_tenant": "Questa sessione non appartiene a questo tenant", "error_no_ocpi": "Questa sessione non contiene dati <PERSON>", "error_cdr_already_pushed": "Il CDR di questa sessione è già stato inviato"}, "soft_stop": {"success": "La sessione dell'utente {{user}} è stata interrotta con successo.", "error": "Si è verificato un errore durante l'interruzione della sessione."}, "refund": {"success": "{{inSuccess}} sessioni sono state trasferite con successo", "partial": "{{inSuccess}} sessioni sono state trasferite con successo e {{inError}} sessioni hanno riscontrato un errore", "forbidden_refund_another_user": "Non puoi trasferire la sessione di un altro utente", "not_authorized": "Non sei autorizzato ad eseguire questa richiesta", "error": "L'applicazione E-Mobility non è riuscita a connettersi al sistema di rimborso. Contatta un Amministratore.", "concur_connection_invalid": "La connessione al tuo sistema di rimborso non è configurata correttamente. Contatta un Amministratore.", "tenant_concur_connection_invalid": "L'organizzazione nella configurazione del tuo sistema di rimborso non è completa. Contatta un Amministratore"}}, "errors": {"no_consumption": {"title": "<PERSON><PERSON><PERSON>", "description": "La sessione non contiene valori del contatore.", "action": "Controllare le impostazioni della stazione di ricarica. Assicurarsi che il valori del contatore siano inviati dalla stazione di ricarica."}, "low_consumption": {"title": "Basso consumo", "description": "La sessione ha un consumo inferiore a 1 kWh.", "action": "Si prega di controllare i dati della stazione di ricarica. Assicurarsi che i dati di ricarica siano inviati correttamente dalla stazione."}, "average_consumption_greater_than_connector_capacity": {"title": "Consumo Troppo Alto", "description": "La potenza di carica media registrata è maggiore della capacità della presa.", "action": "Controllare le impostazioni della stazione di ricarica. Assicurarsi che la potenza massima della presa sia impostata e valida."}, "negative_inactivity": {"title": "Inattività Negativa", "description": "L'inattività della sessione è negativa.", "action": "Controllare le impostazioni della stazione di ricarica. Assicurarsi che i valori del contatore siano inviati nell'ordine corretto."}, "long_inactivity": {"title": "High Inactivity", "description": "L'inattività della sessione è maggiore di 24 ore.", "action": "Controllare la sessione"}, "negative_duration": {"title": "<PERSON><PERSON>", "description": "La durata della sessione è negativa.", "action": "Controllare le impostazioni della stazione di ricarica. Assicurarsi che i valori del contatore siano inviati nell'ordine corretto."}, "low_duration": {"title": "<PERSON><PERSON> bassa", "description": "La sessione é durata meno di 1 minuto.", "action": "Si prega di controllare i dati della stazione di ricarica. Assicurarsi che i dati di ricarica siano inviati correttamente."}, "missing_price": {"title": "Prezzo mancante", "description": "Non è stato assegnato un prezzo alla transazione.", "action": "<PERSON><PERSON>ne il pricing sia attivato nell'organizzazione, nessun prezzo è stato assegnato alla transazione. Contattare l'amministratore del sito."}, "incorrect_starting_date": {"title": "Data Inizio Non Corretta", "description": "La data di inizio non è corretta o impossibile.", "action": "Controllare le impostazioni della stazione di ricarica. Assicurarsi che la data inviata all'inizio della transazione sia corretta."}, "missing_user": {"title": "Sessione senza Utente", "description": "L'Utente è sconosciuto in questa sessione.", "action": "Cancellare la Sessione o disabilitare il controllo di accesso nella corrispondente Area del Sito."}, "no_billing_data": {"title": "Sessione senza dati di Fatturazione", "description": "Dati di fatturazione inesistenti per questa Sessione.", "action": "Cancella la sessione o crea una fattura da essa."}}}, "templates": {"title": "Stazione di Ricarica Template", "tabs": {"charging_station_templates": "Stazione di Ricarica Templates"}, "invalid_json": "Formato JSON non valido", "template_not_found": "Il template non é stato trovato", "create_success": "Il template '{{template}}' é stato creato con successo", "create_error": "Si é verificato un errore durante la creazione del template, si prega di controllare i logs", "update_success": "Il template '{{template}}' é stato aggiornato con successo", "update_error": "Si é verificato un errore durante l'aggiornamento del template, si prega di controllare i logs", "delete_confirm": "Si desidera cancellare il template '{{template}}'?", "delete_success": "Il template '{{template}}' é stato cancellato con successo", "delete_error": "Si é verificato un errore durante la cancellazione del template, si prega di controllare i logs", "delete_title": "Cancella template", "dialog": {"export": {"title": "Esporta templates della stazione di ricarica", "confirm": "Si desidera esportare tutti i template della stazione di ricarica che corrispondono ai fitri selezionati?", "error": "Si é verificato un errore durante il tentativo di esportazione si prega di controllare i logs"}}, "chargePointVendor": "Fornitore", "chargePointModel": "Filtro sur modello", "chargeBoxSerialNumber": "Filtro sur numero di serie"}, "geomap": {"dialog_geolocation_title": "Imposta Geolocalizzazione di {{componentName}} '{{itemComponentName}}'", "select_geolocation": "Seleziona Geo Localizzazione", "search": "Ricerca Luogo", "max_zoom": "Zoom <PERSON>", "min_zoom": "Zoom Minimo"}, "errors": {"title": "Errore", "details": "<PERSON><PERSON><PERSON>", "description": "Descrizione", "action": "Azione"}, "notifications": {"tabs": {"notifications": "Notifications"}, "form": {"title": "Notification Title", "body": "Notification Body"}, "table": {"title": "Title", "body": "Body", "user": "User", "userID": "ID", "timestamp": "Sent Date", "channel": "Channel", "sourceId": "Source", "sourceDescr": "Source Description", "chargeBoxID": "charge Box ID"}, "sent_success": "Notification has been sent successfully", "error_in_sent": "Error in notification send", "admin_only": "Solo Amministratori", "session_started": "Avvisami quando la mia sessione di ricarica è iniziata", "optimal_charge_reached": "Avvisami quando è stato raggiunto il livello ottimale della batteria (85%)", "end_of_charge": "Avvisami quando il mio veicolo elettrico non è più in carica (il veicolo elettrico è ancora collegato)", "end_of_session": "Avvisami quando la mia sessione di ricarica è finita (il veicolo elettrico non è più collegato)", "user_account_status_changed": "Avvisami quando lo stato del mio utente è stato modificato da un Amministratore (Attivo, Bloccato,etc)", "new_registered_user": "Avvisami quando un nuovo utente si è appena registrato", "unknown_user_badged": "Avvisami quando un utente sconosciuto ha inserito un badge in una stazione di ricarica", "charging_station_status_error": "Avvisami quando si verifica un errore su una presa di una stazione di ricarica", "charging_station_registered": "Avvisami quando una stazione di ricarica è stat registrata al backend o è stata riavviata", "charging_stations_offline": "Avvisami quando una stazione di ricarica è offline", "preparing_session_not_started": "Avvisami quando una sessione non è stata avviata (solo proprietario del sito)", "ocpi_patch_status_error": "Avvisami se il trasferimento degli stati di una stazione di ricarica alla piattaforma di roaming (OCPI) fallisce", "oicp_patch_status_error": "Avvisami se il trasferimento degli stati di una stazione di ricarica verso la piattaforma di roaming (OICP) é fallito", "billing_synchronization_failed": "Avvisami quando una sincronizzazione con un sistema di fatturazione non è riuscita", "billing_periodic_operation_failed": "Avvisami un'operazione di fatturazione periodica é fallita", "session_not_started": "Avvisami se nessuna sessione è stata avviata dopo l'inserimento di un badge", "user_account_inactivity": "Avvisami quando non accedo all'applicazione da più di 6 mesi", "car_catalog_synchronization_failed": "Avvisami se si verifica un errore durante la sincronizzazione delle vetture", "compute_and_apply_charging_profiles_failed": "Avvisami quando l'applicazione dei profili di ricarica smart fallise per una certa zona", "end_user_error_notification": "Avvisami quando un utente riporta un errore", "billing_new_invoice": "Avvisami quando è disponibile una nuova fattura", "admin_account_verification": "Avvisami quando un conto deve essere verificato durante la registrazione."}, "payment_settlement": {"table": {"transactionNo": "Transaction No", "amount": "Amount", "invoiceId": "Invoice ID", "corporateName": "Corporate", "paymentMode": "Payment Mode", "remark": "Remark", "settlementDate": "Settlement Date", "transactionDate": "Transaction Date", "id": "ID"}, "form": {"corporateID": "Corporate", "transactionNo": "Transaction No", "amount": "Amount", "invoiceId": "Invoice ID", "paymentMode": "Payment Mode", "remark": "Remark", "settlementDate": "Settlement Date", "transactionDate": "Transaction Date", "id": "ID"}, "payment_methods": {"online": "Online", "cash": "Cash", "credit_card": "Credit Card", "debit_card": "Debit Card"}, "tabs": {"payment": "Payment Settlement"}, "export": {"title": "Export Settled Payments", "confirm": "Do you really want to export the list to a csv format?", "error": "Error occurred while trying to export"}, "record_not_found": "Payment recored not found", "create_success": "The Payment '{{id}}' has been created successfully", "create_error": "Error occurred while creating the Payment, check the logs", "update_success": "The Payment '{{id}}' has been updated successfully", "update_error": "Error occurred while updating the Payment, check the logs", "delete_title": "Delete Payment", "delete_confirm": "Do you really want to delete the Payment '{{id}}'?", "delete_success": "The Payment '{{id}}' has been deleted successfully", "delete_error": "Error occurred while deleting the Payment, check the logs", "get_corporate_error": "Error at corporate fetching time"}, "evse": {"header_title": "Reservation"}, "wallet_report": {"search": {"accountName": "Account Name"}, "searchDilog": {"title": "Select Corporate Admin", "CorporateAdmin": "Corporate Admin"}, "table": {"Date": "Date", "SessionId": "SessionId", "TransactionId": "TransactionId", "currency": "currency", "Amount": "Amount", "paymentType": "Debit/Debit", "ClosingBalance": "ClosingBalance"}}, "virtual_wallet": {"title": "Virtual Wallet", "add_balance": "Add Money", "create_success": "Amount added successfully", "create_error": "Error in amount add", "transactions_not_found": "Transactions not found", "form_field": {"amount": "Amount", "select_user": "Select User", "transactionRef": "Transaction Ref ID", "transactionDate": "Transaction Date", "remarks": "Remarks"}, "errors": {}, "table": {"id": "Transaction ID", "user": "User", "addedBy": "Added By", "amount": "Amount", "select_user": "Select User", "paymentId": "Transaction Ref ID", "transactionDate": "Transaction Date", "remarks": "Remarks"}}}