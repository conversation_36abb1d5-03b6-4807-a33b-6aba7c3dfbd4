<div class="main-content flex-grow-1">
  <mat-tab-group
    (selectedIndexChange)="updateRoute($event)"
    [selectedIndex]="activeTabIndex"
    animationDuration="0ms"
    disableRipple="true"
    class="h-100 mat-tab-info"
  >
    <mat-tab *ngIf="canListCorporates">
      <ng-template mat-tab-label>
        <mat-icon>work</mat-icon>
        <span>{{ "users.tabs.corps" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-corporate-list></app-corporate-list>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="canListPaymentSettlements">
      <ng-template mat-tab-label>
        <mat-icon>attach_money</mat-icon>
        <span>{{ "payment_settlement.tabs.payment" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-payment-settlement-list></app-payment-settlement-list>
      </ng-template>
    </mat-tab>

  </mat-tab-group>
</div>
