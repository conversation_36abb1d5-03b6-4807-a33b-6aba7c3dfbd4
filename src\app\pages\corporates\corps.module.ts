import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PaymentSettlementListComponent } from '../payment-settlement/payment-settlement-list/payment-settlement-list.component';
import { PaymentSettlementAddComponent } from '../payment-settlement/payment-settlement-create/payment-settlement-add.component';
import { PaymentSettlementAddDialogComponent } from '../payment-settlement/payment-settlement-create/payment-settlement-add.dialog.component';

import { MaterialModule } from '../../app.module';
import { AddressModule } from '../../shared/address/address.module';
import { DialogsModule } from '../../shared/dialogs/dialogs.module';
import { CommonDirectivesModule } from '../../shared/directives/directives.module';
import { FormattersModule } from '../../shared/formatters/formatters.module';
import { TableModule } from '../../shared/table/table.module';
import { CorpDialogComponent } from './corp/corp-dialog.component';
import { CorpComponent } from './corp/corp.component';
import { CorpMainComponent } from './corp/main/corp-main.component';
import { CoporateListComponent } from './corporates/corporate-list.component';
import { CorpsComponent } from './corps.component';
import { CorpRoutes } from './corps.routing';
import { AppUserRolePipe } from './formatters/user-role.pipe';
import { AppFormatUserStatusPipe, UserStatusFormatterComponent } from './formatters/user-status-formatter.component';
import { AppUserStatusPipe } from './formatters/user-status.pipe';

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(CorpRoutes),
    ReactiveFormsModule,
    TranslateModule,
    MaterialModule,
    AddressModule,
    MatTabsModule,
    TableModule,
    DialogsModule,
    CommonDirectivesModule,
    FormattersModule,
  ],
  declarations: [
    UserStatusFormatterComponent,
    AppFormatUserStatusPipe,
    CorpsComponent,
    CoporateListComponent,
    CorpComponent,
    CorpMainComponent,
    CorpDialogComponent,
    AppUserRolePipe,
    AppUserStatusPipe,
    PaymentSettlementListComponent,
    PaymentSettlementAddComponent,
    PaymentSettlementAddDialogComponent,
  ],
  exports: [
    AppUserRolePipe,
    AppUserStatusPipe,
    CorpDialogComponent
  ],
  providers: [
    AppUserRolePipe,
    AppUserStatusPipe,
  ]
})

export class CorpsModule {
}
