<div class="main-content flex-grow-1">
  <mat-tab-group
    (selectedIndexChange)="updateRoute($event)"
    [selectedIndex]="activeTabIndex"
    animationDuration="0ms"
    disableRipple="true"
    class="h-100 mat-tab-info"
  >
    <mat-tab *ngIf="canListChargingStations">
      <ng-template mat-tab-label>
        <mat-icon>ev_station</mat-icon><span>{{ "chargers.tabs.list" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-charging-stations-list></app-charging-stations-list>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="canListChargingProfiles">
      <ng-template mat-tab-label>
        <mat-icon>show_chart</mat-icon><span>{{ "chargers.tabs.charging_plans" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-charging-plans-list></app-charging-plans-list>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="canListChargingStationsInError">
      <ng-template mat-tab-label>
        <mat-icon>warning</mat-icon><span>{{ "chargers.tabs.in_error" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-charging-stations-in-error></app-charging-stations-in-error>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="canListTokens">
      <ng-template mat-tab-label>
        <mat-icon>link</mat-icon><span>{{ "chargers.tabs.connection" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-charging-stations-registration-tokens></app-charging-stations-registration-tokens>
      </ng-template>
    </mat-tab>
    <mat-tab *ngIf="canListTokens">
      <ng-template mat-tab-label>
        <mat-icon>settings_input_component</mat-icon
        ><span>{{ "chargers.tabs.evse" | translate }}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-evse-management></app-evse-management>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</div>
