import { Component, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { StatusCodes } from 'http-status-codes';
import * as moment from 'moment';
import { AuthorizationDefinitionFieldMetadata, DialogMode } from 'types/Authorization';
import { CentralServerService } from '../../../../services/central-server.service';
import { ComponentService } from '../../../../services/component.service';
import { DialogService } from '../../../../services/dialog.service';
import { MessageService } from '../../../../services/message.service';
import { SpinnerService } from '../../../../services/spinner.service';
import { SiteAreasDialogComponent } from '../../../../shared/dialogs/site-areas/site-areas-dialog.component';
import { KeyValue, RestResponse } from '../../../../types/GlobalType';
import { HTTPError } from '../../../../types/HTTPError';
import { SiteArea } from '../../../../types/SiteArea';
import { TenantComponents } from '../../../../types/Tenant';
import { Utils } from '../../../../utils/Utils';
import { ChargingStationsChargingStationSchedulesDialogComponent } from './charging-stations-schedules.dialog.component';
import { ChargingStationSchedules } from 'types/ChargingStationSchedules';
import { UsersDialogComponent } from 'shared/dialogs/users/users-dialog.component';
import { User } from 'types/User';
import { TagsDialogComponent } from 'shared/dialogs/tags/tags-dialog.component';
import { Tag } from 'types/Tag';

@Component({
  selector: 'app-charging-stations-schedules',
  templateUrl: 'charging-stations-schedules.component.html',
  styleUrls: ['charging-stations-schedules.component.scss'],
})
export class ChargingStationsChargingStationSchedulesComponent implements OnInit {
  @Input() public currentData!: ChargingStationSchedules;
  @Input() public dialogMode!: DialogMode;
  @Input() public inDialog!: boolean;
  @Input() public readOnly: boolean;
  @Input() public dialogRef!: MatDialogRef<ChargingStationsChargingStationSchedulesDialogComponent>;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;

  public stations: KeyValue[] = [];

  public readonly isOrganizationComponentActive: boolean;
  public formGroup!: UntypedFormGroup;
  public id!: AbstractControl;
  public expireDate!: AbstractControl;
  // public startTime!: AbstractControl;
  // public closingTime!: AbstractControl;
  public chargingStationID!: AbstractControl;
  public connectorId!: AbstractControl;
  public user!: AbstractControl;
  public userID!: AbstractControl;
  public currentDate = moment().format('HH:mm');
  public selectedTag!: Tag;
  public tag!: AbstractControl;
  public visualTagID!: AbstractControl;

  public constructor(
    private centralServerService: CentralServerService,
    private messageService: MessageService,
    private spinnerService: SpinnerService,
    private dialogService: DialogService,
    private componentService: ComponentService,
    private translateService: TranslateService,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.isOrganizationComponentActive = this.componentService.isActive(
      TenantComponents.ORGANIZATION
    );
  }

  public ngOnInit(): void {
    this.formGroup = new UntypedFormGroup({
      expireDate: new UntypedFormControl('', Validators.compose([Validators.required])),
      user: new UntypedFormControl('', Validators.compose([])),
      userID: new UntypedFormControl('', Validators.compose([Validators.required])),
      // startTime: new UntypedFormControl('', Validators.compose([Validators.required])),
      // closingTime: new UntypedFormControl(
      //   this.currentDate,
      //   Validators.compose([Validators.required])
      // ),
      chargingStationID: new UntypedFormControl('', Validators.compose([Validators.required])),
      connectorId: new UntypedFormControl(1, Validators.compose([Validators.required])),
      tag: new UntypedFormControl(
        '',
        Validators.compose([Validators.required, this.tagActiveValidator.bind(this)])
      ),
      visualTagID: new UntypedFormControl('', Validators.compose([Validators.required])),
    });
    this.id = this.formGroup.controls['id'];
    this.user = this.formGroup.controls['user'];
    this.userID = this.formGroup.controls['userID'];
    this.expireDate = this.formGroup.controls['expireDate'];
    // this.startTime = this.formGroup.controls['startTime'];
    // this.closingTime = this.formGroup.controls['closingTime'];
    this.chargingStationID = this.formGroup.controls['chargingStationID'];
    this.connectorId = this.formGroup.controls['connectorId'];
    this.tag = this.formGroup.controls['tag'];
    this.visualTagID = this.formGroup.controls['visualTagID'];

    // Dialog mode
    Utils.handleDialogMode(this.dialogMode, this.formGroup);

    // get Stations listing
    this.getStationList();

    this.loadOldData();
  }

  private getStationList() {
    this.spinnerService.show();
    this.centralServerService.getChargingStationsListsForEVSE().subscribe({
      next: (response: any) => {
        this.spinnerService.hide();
        console.log('response:::', response);
        if (response?.result && response?.result?.length > 0) {
          this.stations = response.result;
          console.log('this.stations', this.stations);
        } else {
          Utils.handleError(
            JSON.stringify(response),
            this.messageService,
            this.translateService.instant('chargers.connections.get_Stations_error')
          );
        }
      },
      error: (error) => {
        console.log('error::::', error);
        this.spinnerService.hide();
        Utils.handleHttpError(
          error,
          this.router,
          this.messageService,
          this.centralServerService,
          this.translateService.instant('chargers.connections.get_Stations_error')
        );
      },
    });
  }

  public loadOldData(): void {
    if (this.currentData) {
      // if (this.currentData.startTime) {
      //   this.startTime.setValue(`${moment().format('YYYY-MM-DD')} ${this.currentData.startTime}`);
      // }
      // if (this.currentData.closingTime) {
      //   this.closingTime.setValue(
      //     `${moment().format('YYYY-MM-DD')} ${this.currentData.closingTime}`
      //   );
      // }
      if (this.currentData.chargingStationID) {
        this.chargingStationID.setValue(this.currentData.chargingStationID);
      }
      if (this.currentData.expireDate) {
        this.expireDate.setValue(this.currentData.expireDate);
      }
      if (this.currentData.expireDate) {
        this.expireDate.setValue(this.currentData.expireDate);
      }
      if (this.currentData.user) {
        this.user.setValue(this.currentData.user);
      }
      if (this.currentData.userID) {
        this.userID.setValue(this.currentData.userID);
      }
      if (this.currentData.connectorId) {
        this.connectorId.setValue(this.currentData.connectorId);
      }
    }
  }

  public closeDialog(saved: boolean = false) {
    if (this.inDialog) {
      this.dialogRef.close(saved);
    }
  }

  public close() {
    Utils.checkAndSaveAndCloseDialog(
      this.formGroup,
      this.dialogService,
      this.translateService,
      this.save.bind(this),
      this.closeDialog.bind(this)
    );
  }

  public resetSiteArea() {
    // this.siteAreaID.reset();
    // this.siteArea.reset();
    this.formGroup.markAsDirty();
  }

  public save(formData: ChargingStationSchedules) {
    // console.log('formData:::::::::::', formData);

    let params = {
      args: {
        connectorId: formData.connectorId,
        // expiryDate: `${moment(formData.expireDate).format('YYYY-MM-DDT23:59:59')}.999Z`,
        expiryDate: formData.expireDate,
        idTag: this.visualTagID.value,
      },
      userID: formData.userID,
    };

    this.createOne(params, formData.chargingStationID);
  }

  private createOne(formData: any, chargingStationID: string) {
    console.log('formData:::::::::::', formData);

    this.spinnerService.show();
    this.centralServerService
      .createChargingStationSchedules(formData, chargingStationID)
      .subscribe({
        next: (response) => {
          this.spinnerService.hide();
          if (response.status === RestResponse.ACCEPTED) {
            this.messageService.showSuccessMessage(
              'chargers.connections.charging_station_shedules_creation_success',
              {
                id: response?.id,
              }
            );
            this.spinnerService.hide();
            this.dialogRef.close(response.status);
          } else {
            console.log('response:::::', response);

            if (
              response.status === RestResponse.OCCUPIED ||
              response.status === RestResponse.UNAVAILABLE
            ) {
              Utils.handleError(JSON.stringify(response), this.messageService, response?.status);
            } else {
              Utils.handleError(
                JSON.stringify(response),
                this.messageService,
                this.translateService.instant(
                  'chargers.connections.charging_station_shedules_creation_error'
                )
              );
            }
          }
        },
        error: (error) => {
          console.log('error:::::', error);
          this.spinnerService.hide();
          Utils.handleHttpError(error, this.router, this.messageService, this.centralServerService);
        },
      });
  }

  public assignSiteArea() {
    // Create the dialog
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'transparent-dialog-container';
    dialogConfig.data = {
      title: 'chargers.assign_site_area',
      sitesAdminOnly: true,
      rowMultipleSelection: false,
      staticFilter: {
        Issuer: true,
      },
    };
    // Open
    this.dialog
      .open(SiteAreasDialogComponent, dialogConfig)
      .afterClosed()
      .subscribe((result) => {
        if (!Utils.isEmptyArray(result) && result[0].objectRef) {
          const siteArea = result[0].objectRef as SiteArea;
          // this.siteArea.setValue(siteArea.name);
          // this.siteAreaID.setValue(siteArea.id);
          this.formGroup.markAsDirty();
        }
      });
  }

  public assignUser() {
    // Create dialog
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'transparent-dialog-container';
    dialogConfig.data = {
      title: 'virtual_wallet.table.select_user',
      sitesAdminOnly: true,
      rowMultipleSelection: false,
      staticFilter: {
        Issuer: true,
        Role: 'B|E',
      },
    };
    this.dialog
      .open(UsersDialogComponent, dialogConfig)
      .afterClosed()
      .subscribe((result) => {
        if (!Utils.isEmptyArray(result) && result[0].objectRef) {
          const user = result[0].objectRef as User;
          this.user.setValue(Utils.buildUserFullName(user));
          this.userID.setValue(user.id);
          this.formGroup.markAsDirty();
        }
      });
  }

  public assignTag() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'transparent-dialog-container';
    // Set data
    dialogConfig.data = {
      rowMultipleSelection: false,
      staticFilter: {
        UserID: this.userID.value,
        Issuer: true,
      },
    };
    // Show
    const dialogRef = this.dialog.open(TagsDialogComponent, dialogConfig);
    // Register to the answer
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.selectedTag = result[0].objectRef;
        console.log('this.selectedTag::', this.selectedTag);
        this.tag.setValue(Utils.buildRFIDTagName(result[0].objectRef));
        this.visualTagID.setValue(result[0].objectRef.id);
      }
    });
  }

  private tagActiveValidator(tagControl: AbstractControl): ValidationErrors | null {
    // Check the object
    if (!this.selectedTag || this.selectedTag.active) {
      return null;
    }
    return { inactive: true };
  }
}
