import { AfterViewInit, Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  AuthorizationDefinitionFieldMetadata,
  DialogMode,
  DataResultAuthorizations,
  DialogParamsWithAuth,
} from 'types/Authorization';
import { Utils } from '../../../../utils/Utils';
import { ConfigureWalletComponent } from './configure-wallet.component';

@Component({
  template:
    '<app-configure-wallet #appRef [metadata]="metadata" [inDialog]="true"></app-configure-wallet>',
})
export class ConfigureWalletDialogComponent implements AfterViewInit {
  @ViewChild('appRef') public appRef!: ConfigureWalletComponent;
  public tokenID!: string;
  public dialogData!: any;
  public metadata?: Record<number, AuthorizationDefinitionFieldMetadata>;
  public dialogMode: DialogMode;
  public constructor(
    public dialogRef: MatDialogRef<ConfigureWalletDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    dialogParams: DialogParamsWithAuth<any, DataResultAuthorizations>
  ) {
    this.dialogData = dialogParams.dialogData;
    this.metadata = dialogParams.authorizations?.metadata;
    console.log('dialogData->>>>>>>>>', this.dialogData);
    console.log('dialogParams->>>>>>>>>', dialogParams);
  }

  public ngAfterViewInit() {
    Utils.registerSaveCloseKeyEvents(
      this.dialogRef,
      this.appRef.formGroup,
      this.appRef.save.bind(this.appRef),
      this.appRef.close.bind(this.appRef)
    );
  }
}
