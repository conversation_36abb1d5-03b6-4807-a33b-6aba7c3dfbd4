<form class="form" [formGroup]="formGroup">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <input
            appAutofocus
            [formControl]="amount"
            matInput
            placeholder="Add amount"
            required
            type="number"
            min="1"
            max="100000"
          />
          <mat-error *ngIf="amount.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
          <mat-error *ngIf="amount.errors?.min">
            <div [translate]="'general.error_min_value'" [translateParams]="{ value: 1 }"></div>
          </mat-error>
          <mat-error *ngIf="amount.errors?.max">
            <div
              [translate]="'general.error_max_value'"
              [translateParams]="{ value: 100000 }"
            ></div>
          </mat-error>
          <mat-error *ngIf="amount.errors?.pattern">
            <div [translate]="'general.error_not_a_number'"></div>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-3" style="margin-top: 50px">
      <button mat-raised-button color="primary" (click)="addBalance()">Submit</button>
    </div>
  </div>
</form>

<!-- <div id="paymentIframeDiv" style="min-height: 100px"></div> -->

<!-- <iframe *ngIf="paymentLink" #iframe [src]="paymentLink">
  <p>Your browser does not support iframes.</p>
</iframe> -->
