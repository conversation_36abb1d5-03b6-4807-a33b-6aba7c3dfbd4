<form class="form" [formGroup]="formGroup">
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <input
            appAutofocus
            matInput
            placeholder="Account Name"
            type="text"
            formControlName="CorporateName"
          />
          <mat-error *ngIf="CorporateName.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <!-- <input
            matInput
            placeholder="Authorised Person"
            type="text"
            formControlName="AuthorisedPerson"
          /> -->

          <!-- <mat-select placeholder="Authorised Person" formControlName="AuthorisedPerson">
            <mat-option *ngFor="let corpPerson of corpPersonsList" [value]="corpPerson.id">
              {{ corpPerson.firstName }}
            </mat-option>
          </mat-select> -->

          <input
            matInput
            type="text"
            readonly="true"
            placeholder="Authorised Person"
            class="form-field-popup"
            (click)="assignUser()"
            [formControl]="AuthorisedPersonName"
          />
          <button
            *ngIf="AuthorisedPerson.enabled && AuthorisedPerson.value"
            mat-icon-button
            matSuffix
            (click)="resetUser()"
            aria-label="Clear"
          >
            <mat-icon>clear</mat-icon>
          </button>

          <mat-error *ngIf="AuthorisedPerson.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <div class="form-group">
        <mat-form-field>
          <input matInput placeholder="PAN/TAN" type="text" formControlName="PAN_TAN" />
          <mat-error *ngIf="PAN_TAN.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <input matInput placeholder="GST Number" type="text" formControlName="GSTNo" />
          <mat-error *ngIf="GSTNo.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <mat-select placeholder="Pricing" formControlName="Pricing">
            <mat-option *ngFor="let catalog of priceCatalog" [value]="catalog.id">
              {{ catalog.name }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="Pricing.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>
  <!-- Closing </div> tag for the "row" div -->
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <mat-form-field>
          <mat-select placeholder="Billing" formControlName="Billing">
            <mat-option value="Immediate">Immediate Billing</mat-option>
            <mat-option value="Periodic">Periodic Billing</mat-option>
          </mat-select>
          <mat-error *ngIf="Billing.errors?.required">
            {{ "general.mandatory_field" | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </div>
</form>
