import { Routes } from '@angular/router';

import { RouteGuardService } from '../../guard/route-guard';
import { Action, Entity } from '../../types/Authorization';
import { DashboardComponent } from './dashboard.component';

export const DashboardRoutes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    canActivate: [RouteGuardService],
    data: {
      auth: {
        entity: Entity.CHARGING_STATION,
        action: Action.LIST,
      },
    },
  },
];
