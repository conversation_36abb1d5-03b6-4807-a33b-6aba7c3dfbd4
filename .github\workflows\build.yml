name: Build CI
on:
  push:
    branches:
      - master
      - master-qa
  pull_request:
    branches:
      - master-qa
jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v2
      with:
        node-version: ${{ matrix.node-version }}
    - name: npm install
      run: npm ci --force
    - name: npm run build:prod
      run: npm run build:prod

  docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Docker Buildx
      id: buildx
      uses: docker/setup-buildx-action@v1
    - name: Build docker image
      run: |
        cd docker
        make SUBMODULES_INIT=false
