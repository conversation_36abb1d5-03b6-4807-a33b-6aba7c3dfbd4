<div class="main-content">
  <mat-tab-group (selectedIndexChange)="updateRoute($event)" [selectedIndex]="activeTabIndex" animationDuration="0ms" disableRipple="true" class="h-100 mat-tab-info">
    <mat-tab *ngIf="isBasic || isAdmin || isTransportAdmin || isTransportManager">
      <ng-template mat-tab-label>
        <mat-icon>people</mat-icon><span>{{'car.tabs.cars' | translate}}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-cars-list></app-cars-list>
      </ng-template>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>
        <mat-icon>people</mat-icon><span>{{'car.tabs.car_catalogs' | translate}}</span>
      </ng-template>
      <ng-template matTabContent>
        <app-car-catalogs-list></app-car-catalogs-list>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</div>
