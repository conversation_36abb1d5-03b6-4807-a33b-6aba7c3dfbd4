<div class="wrapper wrapper-full-page">
    <div class="page-header login-page header-filter" filter-color="black">
      <div class="container">
        <div class="col-md-6 ms-auto me-auto">
          <form class="form" [formGroup]="formGroup" (ngSubmit)="OTPVerify(formGroup.value)">
            <div class="card card-login card-hidden">
              <div class="card-header card-header-primary text-center h-25">
                <div class="social-line">
                  <img class="big-card-logo mb-2" [src]="tenantLogo" />
                </div>
                <h4 class="card-title text-center">Verify OTP</h4>
              </div>
              <div class="card-body">
                <span class="bmd-form-group">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                        <mat-icon>pin</mat-icon>
                        <mat-form-field class="ms-2">
                          <input appAutofocus matInput type="text" placeholder="OTP"
                            [formControl]="otp" required autocomplete="" [readonly]="false">
                          <mat-error *ngIf="otp.errors?.otp">{{"users.invalid_phone_number" | translate}}</mat-error>
                          <mat-error *ngIf="otp.errors?.required">{{"general.mandatory_field" | translate}}</mat-error>
                        </mat-form-field>
                      </span>
                    </div>
                  </div>
                </span>
              </div>
              <div class="card-footer justify-content-center mb-4" [hidden]="verifyEmailAction">
                <button mat-button type="submit"
                  [disabled]="!formGroup.valid">{{"authentication.verify_email_resend_button" | translate}}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  