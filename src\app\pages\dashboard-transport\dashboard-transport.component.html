<div class="main-content flex-grow-1">
  <div class="dashboard-header">
    <div class="dashboard-box">
      <i class="material-icons">dashboard</i> {{ "dashboard.tabs.dashboard" | translate }}
    </div>
  </div>

  <div class="dashboard-content">
    <div class="dropdown-container">
      <mat-form-field appearance="outline" class="dropdown-mat-box">
        <mat-select [(value)]="selectedDropdownOption" (selectionChange)="applyFilter($event)">
          <mat-option value="">{{ "dashboard.flters.dropdown.all" | translate }}</mat-option>
          <mat-option value="true">{{
            "dashboard.flters.dropdown.current" | translate
          }}</mat-option>
          <mat-option value="false">{{
            "dashboard.flters.dropdown.external" | translate
          }}</mat-option>
        </mat-select>
        <mat-label> Select </mat-label>
      </mat-form-field>

      <!-- Site Dropdown -->
      <mat-form-field appearance="outline" class="dropdown-mat-box">
        <mat-select
          placeholder="Select Site"
          [(ngModel)]="selectedSites"
          (selectionChange)="applySiteFilter($event)"
          multiple
        >
          <mat-option *ngFor="let site of availableSites" [value]="site">{{
            site.name
          }}</mat-option>
        </mat-select>
        <mat-label>Select Site</mat-label>
      </mat-form-field>

      <!-- Site Area Dropdown -->
      <mat-form-field appearance="outline" class="dropdown-mat-box">
        <mat-select
          placeholder="Select Site Area"
          [(ngModel)]="selectedSiteAreas"
          (selectionChange)="applySiteAreaFilter($event)"
          multiple
        >
          <mat-option *ngFor="let siteArea of availableSiteAreas" [value]="siteArea">{{
            siteArea.name
          }}</mat-option>
        </mat-select>
        <mat-label>Select Site Area</mat-label>
      </mat-form-field>
    </div>

    <div class="info-cards">
      <div class="card chargin-stations clickable mt-0" (click)="goto('charging-stations')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.charging_stations" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.chargingStationsCountMDB }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/chargin-station.jpg" />
          </div>
        </div>
      </div>

      <div class="card error clickable mt-0" (click)="goto('charging-stations', 'inerror')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.connection_loss" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value blink-red">{{ dashboardData.connectionLost }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/charging-error.jpg" />
          </div>
        </div>
      </div>

      <div class="card success clickable mt-0" (click)="goto('users')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.active_users" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.usersCountMDB }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/active-user.jpg" />
          </div>
        </div>
      </div>

      <div class="card success clickable mt-0" (click)="goto('transactions')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.active_sessions" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <!-- Apply the blink-green class only if ActiveSessions is greater than 0 -->
              <p class="card-value" [ngClass]="{ 'blink-green': dashboardData.ActiveSessions > 0 }">
                {{ dashboardData.ActiveSessions }}
              </p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Sessions.png" />
          </div>
        </div>
      </div>

      <div class="card blue clickable mt-0" (click)="goto('transactions', 'history')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.charging_sessions_month" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.chargingSessionsMonth }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Sessions-this-month.png" />
          </div>
        </div>
      </div>

      <div class="card blue clickable mt-0" (click)="goto('statistics', 'carbon-credit')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.carbon_credit_today" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.Co2EmissionToday }} Kg</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Carbon-Credit-Today.png" />
          </div>
        </div>
      </div>

      <div class="card money clickable mt-0" (click)="goto('statistics', 'carbon-credit')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.carbon_credit_month" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.Co2EmissionInMonth }} Kg</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Carbon-Credit-Month.png" />
          </div>
        </div>
      </div>

      <div class="card money clickable mt-0" (click)="openChargingNowPopup()">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.vehicle_charging_now" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">
                <span class="view-text">
                  {{ dashboardData.ActiveSessions }}
                </span>
              </p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/active-sessions.jpg" />
          </div>
        </div>
      </div>

      <div class="card money clickable mt-0">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.vehicle_charging_history" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">
                <span class="view-text" (click)="openChargingHistoryPopup()"> View </span>
              </p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Vehicle-Charging.png" />
          </div>
        </div>
      </div>

      <div class="card energy clickable mt-0" (click)="goto('transactions', 'history')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.energy_today" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.energyToday }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/energy-today.jpg" />
          </div>
        </div>
      </div>

      <div class="card energy clickable mt-0" (click)="goto('transactions', 'history')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.energy_month" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.energyCurrentMonth }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Monthly energy consumption.png" />
          </div>
        </div>
      </div>

      <div class="card energy clickable mt-0" (click)="goto('transactions', 'history')">
        <div class="card-body">
          <div class="d-flex flex-row align-items-center">
            <p class="card-title">{{ "dashboard.cards.energy_year" | translate }}</p>
          </div>
        </div>
        <div class="card-footer">
          <div
            class="d-flex flex-row align-items-center justify-content-between"
            style="font-size: 22px; font-weight: bold"
          >
            <div class="d-flex flex-row align-items-center">
              <p class="card-value">{{ dashboardData.energyCurrentYear }}</p>
            </div>
          </div>
          <div class="d-flex flex-row align-items-center justify-content-between">
            <img class="dashboard-icon" src="../../assets/icon/Yearly energy consumption.png" />
          </div>
        </div>
      </div>

      <div style="clear: both"></div>
    </div>

    <div #mapContainer id="map" class="map mb_3"></div>
  </div>
</div>
