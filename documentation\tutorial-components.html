<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="apple-touch-icon" sizes="76x76" href="../src/assets/img/apple-icon.png" />
    <link rel="icon" type="image/png" href="../src/assets/img/favicon.png" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>Material Dashboard PRO Angular by Creative Tim | Premium Bootstrap Admin Template</title>
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0' name='viewport' />
    <meta name="viewport" content="width=device-width" />

	<!-- Bootstrap core CSS     -->
    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <!--  Material Dashboard CSS    -->
    <link href="css/material-dashboard.css" rel="stylesheet" />
    <!--     Fonts and icons     -->
    <link href="http://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons" />
    <link href="css/demo-documentation.css" rel="stylesheet" />
    <script src="https://cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js"></script>
    <style>
        pre.prettyprint {
            background-color: #eee;
            border: 0px;
            margin-bottom: 60px;
            margin-top: 30px;
            padding: 20px;
            text-align: left;
        }

        .atv,
        .str {
            color: #05AE0E;
        }

        .tag,
        .pln,
        .kwd {
            color: #3472F7;
        }

        .atn {
            color: #2C93FF;
        }

        .pln {
            color: #333;
        }

        .com {
            color: #999;
        }

        .space-top {
            margin-top: 50px;
        }

        .area-line {
            border: 1px solid #999;
            border-left: 0;
            border-right: 0;
            color: #666;
            display: block;
            margin-top: 20px;
            padding: 8px 0;
            text-align: center;
        }

        .area-line a {
            color: #666;
        }

        .container-fluid {
            padding-right: 15px;
            padding-left: 15px;
        }
    </style>
    <!--     Fonts and icons     -->
    <link href="http://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons" />
</head>

<body class="components-page">
    <nav class="navbar navbar-transparent navbar-default navbar-fixed-top navbar-color-on-scroll">
        <div class="container">
            <!-- Brand and toggle get grouped for better mobile display -->
            <div class="navbar-header">
                <button id="menu-toggle" type="button" class="navbar-toggle">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar bar1"></span>
                    <span class="icon-bar bar2"></span>
                    <span class="icon-bar bar3"></span>
                </button>
                <a href="https://www.creative-tim.com">
                    <div class="logo-container">
                        <div class="logo">
                            <img src="../src/assets/img/new_logo.png" alt="Creative Tim Logo">
                        </div>
                        <div class="brand">
                            Creative Tim
                        </div>
                    </div>
                </a>
            </div>
            <!-- Collect the nav links, forms, and other content for toggling -->
            <div class="collapse navbar-collapse text-center">
        		<ul class="nav navbar-nav navbar-center">
        			<li class="visible-md visible-lg">
        				<div class="navbar-title hidden text-center">
        					<h4>
        						<div class="image-container">
        							<img src="../src/assets/img/angular2-logo.png" alt="Angular2 Logo">
        						</div>
        						Material Dashboard PRO Angular
        					</h4>
        				</div>
        			</li>
        		</ul>
        		<ul  class="nav navbar-nav navbar-right">
        			<li>
                        <a href="https://github.com/creativetimofficial/ct-material-dashboard-pro-angular/issues" target="_blank">Have an issue?</a>
                    </li>
            	</ul>
            </div>
            <!-- /.navbar-collapse -->
        </div>
        <!-- /.container-fluid -->
    </nav>
    <div class="page-header header-filter">
        <div class="container">
            <div class="content-center">
                <h1 class="title text-center">Components Documentation</h1>
                <h3 class="category">v2.1.0</h3>
                <h4 class="description text-center">We are constantly doing updates for you. Please check the online documentation.</h4>
                <a href="https://demos.creative-tim.com/material-dashboard-pro-angular2/documentation/tutorial" class="btn btn-primary btn-lg btn-round">View Documentation</a>
            </div>
        </div>
    </div>

    <!-- end section -->
    </div>
    <footer class="footer footer-transparent">
        <div class="container">
            <nav class="pull-left">
                <ul>
                    <li>
                        <a href="https://www.creative-tim.com">
                            Creative Tim
                        </a>
                    </li>
                    <li>
                        <a href="https://www.creative-tim.com/about-us">
                            About Us
                        </a>
                    </li>
                    <li>
                        <a href="http://blog.creative-tim.com">
                            Blog
                        </a>
                    </li>
                    <li>
                        <a href="https://www.creative-tim.com/license">
                            Licenses
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="social-area pull-right">
                <a class="btn btn-just-icon btn-twitter btn-simple" href="https://twitter.com/CreativeTim">
                    <i class="fa fa-twitter"></i>
                </a>
                <a class="btn btn-just-icon btn-facebook btn-simple" href="https://www.facebook.com/CreativeTim">
                    <i class="fa fa-facebook-square"></i>
                </a>
                <a class="btn btn-just-icon btn-google btn-simple" href="https://plus.google.com/+CreativetimPage">
                    <i class="fa fa-google-plus"></i>
                </a>
                <a class="btn btn-just-icon btn-instagram btn-simple" href="https://www.instagram.com/creativetimofficial">
                    <i class="fa fa-instagram"></i>
                </a>
            </div>
            <div class="copyright">
                &copy;
                <script>
                    document.write(new Date().getFullYear())
                </script> Creative Tim, made with love
            </div>
        </div>
    </footer>
</body>
</html>
