import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CentralServerService } from '../../services/central-server.service';
import { SpinnerService } from '../../services/spinner.service';
import { MessageService } from '../../services/message.service';
import { TranslateService } from '@ngx-translate/core';

interface UserState {
  mfaAddedByUser: boolean;
  email: string;
  password: string;
  acceptEula: boolean;
  tenant: string;
}

@Component({
  selector: 'app-two-factor-auth',
  templateUrl: './two-factor-auth.component.html',
})
export class TwoFactorAuthComponent implements OnInit {
  public formGroup: UntypedFormGroup;
  public otp: UntypedFormControl;
  public email: string;
  public password: string;
  public tenant: string;
  public acceptEula: boolean;
  public qrcode: string;
  public messages!: Record<string, string>;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private centralServerService: CentralServerService,
    private spinnerService: SpinnerService,
    private messageService: MessageService,
    private translateService: TranslateService
  ) {
    this.otp = new UntypedFormControl('', [
      Validators.required,
      Validators.minLength(6),
      Validators.maxLength(6),
      Validators.pattern(/^\d{6}$/),
    ]);
    this.formGroup = new UntypedFormGroup({
      otp: this.otp,
    });
    this.translateService.get('authentication', {}).subscribe((messages) => {
      this.messages = messages;
    });
  }

  ngOnInit(): void {
    this.qrcode = sessionStorage.getItem('qrcode') || '';
    const user = sessionStorage.getItem('userState');
    if (!this.qrcode && !user) {
      void this.router.navigateByUrl('/');
      return;
    }
    if (user) {
      const userState: UserState = JSON.parse(user);
      this.email = userState.email || '';
      this.password = userState.password || '';
      this.acceptEula = userState.acceptEula;
      this.tenant = userState.tenant || '';
    }
  }

  public verifyOTP(): void {
    this.spinnerService.show();
    const verificationPayload = {
      email: this.email,
      password: this.password,
      acceptEula: this.acceptEula,
      tenant: this.tenant,
      mfacode: this.formGroup.get('otp')?.value,
    };

    this.centralServerService.login(verificationPayload).subscribe({
      next: (result) => {
        this.spinnerService.hide();
        if (result.authenticatorUserExpired === true) {
          this.messageService.showErrorMessage(this.messages['mfa_qrcode_expired']);
          sessionStorage.removeItem('qrcode');
          sessionStorage.setItem('qrcode', result.qrcode);
          if (result.qrcode) {
            this.qrcode = result.qrcode;
          }
        } else if (result.token) {
          this.centralServerService.loginSucceeded(result.token);
          sessionStorage.removeItem('userState');
          sessionStorage.removeItem('qrcode');
          void this.router.navigateByUrl('/');
        } else {
          this.messageService.showErrorMessage(this.messages['mfa_totp_invalid']);
        }
      },
      error: (error) => {
        this.spinnerService.hide();
        this.messageService.showErrorMessage(this.messages['general.unexpected_error_backend']);
      },
    });
  }
}
