import { Address } from './Address';
import { TableData } from './Table';

export interface Tenant extends TableData {
  id: string;
  name: string;
  shortname: string;
  email: string;
  address: Address;
  subdomain: string;
  components?: TenantComponent;
  logo: string;
  iosPackage: string;
  androidPackage: string;
  taxLevel: string;
  taxLevelValue: string;
  redirectWebsiteUrl: string;
}

export interface TenantComponent {
  ocpi?: TenantComponentContent;
  oicp?: TenantComponentContent;
  organization?: TenantComponentContent;
  pricing?: TenantComponentContent;
  billing?: TenantComponentContent;
  billingPlatform?: TenantComponentContent;
  refund?: TenantComponentContent;
  statistics?: TenantComponentContent;
  mfa?:TenantComponentContent;
  analytics?: TenantComponentContent;
  smartCharging?: TenantComponentContent;
  asset?: TenantComponentContent;
  car?: TenantComponentContent;
  carConnector?: TenantComponentContent;
  iosPackage?: TenantComponentContent;
  androidPackage?: TenantComponentContent;
  redirectWebsiteUrl?: TenantComponentContent;
}

export interface TenantComponentContent {
  active: boolean;
  type: string;
}

export enum TenantButtonAction {
  EDIT_TENANT = 'edit_tenant',
  CREATE_TENANT = 'create_tenant',
  DELETE_TENANT = 'delete_tenant',
}

export enum TenantComponents {
  OCPI = 'ocpi',
  OICP = 'oicp',
  REFUND = 'refund',
  PRICING = 'pricing',
  CARBON_CREDIT = 'carbonCredit',
  ORGANIZATION = 'organization',
  STATISTICS = 'statistics',
  MFA_AUTH = 'mfa',
  ANALYTICS = 'analytics',
  BILLING = 'billing',
  BILLING_PLATFORM = 'billingPlatform',
  ASSET = 'asset',
  SMART_CHARGING = 'smartCharging',
  SMS = 'smsVendor',
  SMTP = 'smtp',
  CAR = 'car',
  // PAYMENT_SETTLEMENT = 'car',
  PAYMENT_SETTLEMENT = 'invoicePayment',
  CAR_CONNECTOR = 'carConnector',
  CHARGING_STATION_TEMPLATE = 'chargingStationTemplate',
  WALLET_REPORTS = 'WALLET_REPORT',
  ACCOUNT_NAME = 'accounts',
}
