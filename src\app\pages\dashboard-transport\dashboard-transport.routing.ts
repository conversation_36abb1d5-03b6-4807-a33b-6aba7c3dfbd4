import { Routes } from '@angular/router';

import { RouteGuardService } from '../../guard/route-guard';
import { Action, Entity } from '../../types/Authorization';
import { DashboardTransportComponent } from './dashboard-transport.component';

export const DashboardTransportRoutes: Routes = [
  {
    path: '',
    component: DashboardTransportComponent,
    canActivate: [RouteGuardService],
    data: {
      auth: {
        entity: Entity.CHARGING_STATION,
        action: Action.LIST,
      },
    },
  },
];
