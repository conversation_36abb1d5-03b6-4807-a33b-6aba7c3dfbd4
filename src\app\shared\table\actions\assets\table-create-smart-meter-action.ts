
import { ComponentType } from '@angular/cdk/portal';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { AssetsAuthorizations, DialogParamsWithAuth } from 'types/Authorization';

import { Asset, AssetButtonAction } from '../../../../types/Asset';
import { TableActionDef } from '../../../../types/Table';
import { TableCreateAction } from '../table-create-action';

export interface TableCreateSmartMeterActionDef extends TableActionDef {
  action: (smartMeterDialogComponent: ComponentType<unknown>, dialog: MatDialog,
    dialogParams: DialogParamsWithAuth<Asset, AssetsAuthorizations>, refresh?: () => Observable<void>) => void;
}

export class TableCreateSmartMeterAction extends TableCreateAction {
  public getActionDef(): TableCreateSmartMeterActionDef {
    return {
      ...super.getActionDef(),
      id: AssetButtonAction.CREATE_SMART_METER,
      action: this.createSmartMeter,
    };
  }

  private createSmartMeter(smartMeterDialogComponent: ComponentType<unknown>, dialog: MatDialog,
    dialogParams: DialogParamsWithAuth<Asset, AssetsAuthorizations>, refresh?: () => Observable<void>) {
    super.create(smartMeterDialogComponent, dialog, dialogParams, refresh);
  }
}

