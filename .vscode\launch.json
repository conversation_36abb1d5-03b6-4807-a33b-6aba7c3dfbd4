{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // SourceMapping: Update your /etc/hosts file with specific entries for tenants describes in configurations bellow:
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Tenant 'slf'",
      "request": "launch",
      "type": "pwa-chrome",
      "url": "http://slf.localhost:45000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Debug Tenant 'utbilling'",
      "type": "pwa-chrome",
      "request": "launch",
      "url": "http://utbilling.localhost:45000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Debug Tenant 'utbillingplatform'",
      "type": "pwa-chrome",
      "request": "launch",
      "url": "http://utbillingplatform.localhost:45000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Debug Tenant 'salvatore'",
      "type": "pwa-chrome",
      "request": "launch",
      "url": "http://salvatore.localhost:45000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "Run all Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": [
        "--i",
        "--runInBand",
        "--config='./jest.config.ts'",
        "--reporters='default'",
        "--reporters='./test/JestEvseReporter'",
        "--watchAll=false",
      ],
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "disableOptimisticBPs": false,
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest"
      }
    },
  ]
}
