import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { WindowService } from 'services/window.service';
import { ActivatedRoute } from '@angular/router';
import { CentralServerService } from '../../services/central-server.service';
import { SpinnerService } from '../../services/spinner.service';

@Component({
  templateUrl: 'authentication-eula.component.html',
})
export class AuthenticationEulaComponent implements OnInit {
  public eulaText!: string;
  public constructor(
    private translateService: TranslateService,
    private spinnerService: SpinnerService,
    private windowService: WindowService,
    private route: ActivatedRoute,
    private centralServerService: CentralServerService
  ) {
    this.spinnerService.hide();
  }
  public ngOnInit() {
    this.route.queryParams.subscribe(params => {
      const language = this.windowService.getUrlParameterValue('Language');
      const slug = params['slug'] || 'terms-and-conditions';
      this.centralServerService.getEndUserLicenseAgreement(slug).subscribe((eula) => {
        this.eulaText = eula.description;
      });
    });
  }
}
