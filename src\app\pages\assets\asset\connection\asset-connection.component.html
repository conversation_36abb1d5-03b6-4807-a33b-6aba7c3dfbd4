<form class="form" [formGroup]="formGroup">
  <!-- Smart Meter specific fields -->
  <div *ngIf="isSmartMeter" class="smart-meter-connection">
    <!-- 1. URL -->
    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          <mat-form-field>
            <input matInput placeholder="URL" type="url" [formControl]="url" required (input)="markFormAsDirty()">
            <mat-error *ngIf="url?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
            <mat-error *ngIf="url?.errors?.pattern">
              Please enter a valid URL (http:// or https://)
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
    <!-- 2. Meter ID -->
    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          <mat-form-field>
            <input matInput placeholder="Meter ID" type="text" [formControl]="meterID" required (input)="markFormAsDirty()">
            <mat-error *ngIf="meterID?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
            <mat-error *ngIf="meterID?.errors?.maxlength">
              <div [translateParams]="{length: meterID?.errors?.maxlength?.requiredLength}" [translate]="'general.error_max_length'"></div>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
    <!-- 3. Meter Name -->
    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          <mat-form-field>
            <input matInput placeholder="Meter Name" type="text" [formControl]="meterName" required (input)="markFormAsDirty()">
            <mat-error *ngIf="meterName?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
            <mat-error *ngIf="meterName?.errors?.maxlength">
              <div [translateParams]="{length: meterName?.errors?.maxlength?.requiredLength}" [translate]="'general.error_max_length'"></div>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
    <!-- 4. Public Key -->
    <div class="row">
      <div class="col-md-12">
        <div class="form-group compact-textarea">
          <mat-form-field>
            <textarea matInput placeholder="Public Key" [formControl]="publicKey" required rows="2" (input)="markFormAsDirty()"></textarea>
            <mat-error *ngIf="publicKey?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
    <!-- 5. Private Key -->
    <div class="row">
      <div class="col-md-12">
        <div class="form-group compact-textarea">
          <mat-form-field>
            <textarea matInput placeholder="Private Key" [formControl]="privateKey" required rows="2" (input)="markFormAsDirty()"></textarea>
            <mat-error *ngIf="privateKey?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  <!-- Regular asset connection fields -->
  <div *ngIf="!isSmartMeter" class="regular-asset-connection">
    <div class="row">
      <div class="col-md-12 col-xl-4">
        <div class="form-group">
          <mat-checkbox [formControl]="dynamicAsset" (change)="disableConnectionDetails()">
            {{'assets.dynamic_asset' | translate}}
          </mat-checkbox>
        </div>
      </div>
      <div class="col-md-12 col-xl-4">
        <div class="form-group">
          <mat-checkbox [formControl]="usesPushAPI" (change)="disableConnectionDetails()">
            {{'assets.uses_push_api' | translate}}
          </mat-checkbox>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 col-xl-5">
        <div class="form-group">
          <mat-form-field>
            <mat-select [formControl]="connectionID" placeholder="{{'chargers.connector' | translate}}" required>
              <mat-option *ngFor="let connection of assetConnections" value="{{connection.key}}">
                {{connection.value}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="connectionID?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 col-xl-5">
        <div class="form-group">
          <mat-form-field>
            <input matInput type="text" placeholder="{{'assets.meter_id' | translate}}" [formControl]="meterID"
              required />
            <mat-error *ngIf="meterID?.errors?.required">
              {{"general.mandatory_field" | translate}}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>
</form>