<div class="row mt-2 ms-2 me-2">
  <div class="col-auto mt-2 me-auto">
    <mat-form-field>
      <mat-datetimepicker #picker type="date" openOnFocus="false" mode="portrait" timeInterval="1">
      </mat-datetimepicker>
      <mat-datetimepicker-toggle matSuffix [for]="picker"></mat-datetimepicker-toggle>
      <input matInput placeholder="{{'assets.consumption_date' | translate}}" required
        (dateChange)="dateFilterChanged($event.value ? $event.value.toDate() : null)" [matDatetimepicker]="picker"
        [formControl]="dateControl" autocomplete="false">
      <mat-error *ngIf="dateControl.errors?.matDatepickerParse">{{"general.invalid_date" | translate}}
      </mat-error>
    </mat-form-field>
  </div>
  <div class="col-auto" [hidden]="asset?.values?.length === 0">
    <app-chart-unit-selector (unitChanged)="unitChanged($event)"></app-chart-unit-selector>
  </div>
</div>
<div *ngIf="asset?.values?.length === 0" class="graph-no-consumption">
  {{'asset.graph.no_consumption' | translate}}
</div>
<div [hidden]="asset?.values?.length === 0">
  <div class="chart-container mt-2">
    <div #primary class='chart-primary'></div>
    <div #danger class='chart-danger'></div>
    <div #success class='chart-success'></div>
    <canvas #chart></canvas>
  </div>
</div>
