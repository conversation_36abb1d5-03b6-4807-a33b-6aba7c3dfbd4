<div class="main-content vw-90 vh-90 tag-dialog-size">
  <div class="card card-profile card-testimonial">
    <mat-tab-group
      animationDuration="0ms"
      disableRipple="true"
      class="mat-tab-info"
      [class]="dialogRef && !readOnly ? 'mat-tabs-with-actions' : 'mat-tabs-with-close-action'"
    >
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>work</mat-icon>
          <span>Account</span>
        </ng-template>
        <div class="card-body mat-tab-dialog-body-content">
          <app-corp-main
            [formGroup]="formGroup"
            [corp]="corp"
            [readOnly]="readOnly"
            [corpAuthorizations]="corpAuthorizations"
            [dialogMode]="dialogMode"
            [priceCatalog]="priceCatalog"
          ></app-corp-main>
        </div>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>location_on</mat-icon>
          <span>{{ "general.address" | translate }}</span>
        </ng-template>
        <div class="card-body">
          <app-address
            [address]="CorporateAddress"
            [formGroup]="formGroup"
            [hideGeolocation]="true"
          >
          </app-address>
        </div>
      </mat-tab>
    </mat-tab-group>
    <div [class]="dialogRef ? 'tabs-actions' : 'tabs-actions-embedded'">
      <button
        mat-icon-button
        *ngIf="!readOnly"
        (click)="saveCorp(formGroup.getRawValue())"
        title="{{ 'general.save' | translate }}"
        [disabled]="!formGroup.valid || !formGroup.dirty"
      >
        <mat-icon>save</mat-icon>
      </button>
      <button
        mat-icon-button
        *ngIf="dialogRef"
        (click)="close()"
        title="{{ 'general.close' | translate }}"
      >
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
