<div class="wrapper wrapper-full-page">
  <div class="page-header login-page header-filter" filter-color="black">
    <div class="container">
      <div class="col-lg-6 ms-auto me-auto">
        <form class="form" [formGroup]="formGroup" (ngSubmit)="resetPassword(formGroup.value)">
          <div class="card card-login card-hidden">
            <div class="card-header card-header-primary text-center h-25">
              <div class="social-line">
                <img class="big-card-logo mb-2" [src]="tenantLogo"/>
              </div>
              <h4 class="card-title text-center">{{"authentication.reset_password_title" | translate}}</h4>
            </div>
            <div class="card-body">
              <span class="bmd-form-group">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <mat-icon>email</mat-icon>
                      <mat-form-field class="ms-2">
                        <input appAutofocus matInput type="text" placeholder="{{'authentication.email' | translate}}"
                          [formControl]="email" required>
                        <mat-error *ngIf="email.errors?.email">{{"authentication.invalid_email" | translate}}</mat-error>
                        <mat-error *ngIf="email.errors?.required">{{"general.mandatory_field" | translate}}</mat-error>
                      </mat-form-field>
                    </span>
                  </div>
                </div>
              </span>
            </div>
            <div class="card-footer justify-content-center mb-4">
              <button mat-button type="submit"
                [disabled]="!formGroup.valid">{{"authentication.reset_password_button" | translate}}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
