import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CentralServerService } from 'services/central-server.service';
import { MessageService } from 'services/message.service';
import { AuthorizationDefinitionFieldMetadata } from 'types/Authorization';

@Component({
  selector: 'app-initiate-kyc',
  templateUrl: 'initiate-kyc.component.html',
})
export class PancardComponent implements OnInit {
  @Input() public user!: any;
  @Input() public metadata!: Record<string, AuthorizationDefinitionFieldMetadata>;
  @Output() public tabChanged = new EventEmitter<any>();
  @Output() public closePopup = new EventEmitter<any>();

  public formGroup!: UntypedFormGroup;
  public panCardNumber!: AbstractControl;

  public constructor(
    private centralServerService: CentralServerService,
    private messageService: MessageService
  ) {}

  public ngOnInit(): void {
    // Initialize form groups
    this.formGroup = new UntypedFormGroup({});

    // Init the form control for PAN card number
    this.formGroup.addControl(
      'panCardNumber',
      new UntypedFormControl(
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern('[A-Z]{5}[0-9]{4}[A-Z]{1}'), // PAN card format validation
        ])
      )
    );

    // Form control
    this.panCardNumber = this.formGroup.controls['panCardNumber'];
  }

  submitPAN() {
    if (this.formGroup.valid) {
      const params = {
        pan: this.panCardNumber.value, // Use the correct key for the API
      };
      this.centralServerService.submitPANCard(params).subscribe({
        next: (response) => {
          if (response.kyc_url) {
            // window.location.href = response.kyc_url;
            this.openWindow(response.kyc_url);
          }
        },
        error: (error) => {
          this.messageService.showErrorMessage(error?.details?.errorMessage);
        },
      });
    } else {
      this.messageService.showErrorMessage('Please enter a valid PAN card number.');
    }
  }

  openWindow(myURL: string, title = 'KYC Wallet', myWidth = 900, myHeight = 700) {
    var left = (screen.width - myWidth) / 2;

    var top = (screen.height - myHeight) / 4;

    var razorpayWindow = window.open(
      myURL,
      title,
      'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=' +
        myWidth +
        ', height=' +
        myHeight +
        ', top=' +
        top +
        ', left=' +
        left
    );

    let razorpayInterval = setInterval(() => {
      if (razorpayWindow.closed) {
        clearInterval(razorpayInterval);
        this.messageService.showSuccessMessage('Wallet KYC completed.');
        this.closePopup.emit();
        // location.reload();
      }
    }, 1000);
  }
}
