import { AfterViewInit, Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  AuthorizationDefinitionFieldMetadata,
  DataResultAuthorizations,
  DialogParamsWithAuth,
} from 'types/Authorization';
import { Utils } from '../../../../utils/Utils';
import { ChargingStationSchedules } from 'types/ChargingStationSchedules';
import { ChargingStationsChargingStationSchedulesComponent } from './charging-stations-schedules.component';

@Component({
  template:
    '<app-charging-stations-schedules #appRef [currentData]="dialogData" [metadata]="metadata" [inDialog]="true" [dialogRef]="dialogRef"></app-charging-stations-schedules>',
})
export class ChargingStationsChargingStationSchedulesDialogComponent implements AfterViewInit {
  @ViewChild('appRef') public appRef!: ChargingStationsChargingStationSchedulesComponent;
  public tokenID!: string;
  public dialogData!: ChargingStationSchedules;
  public metadata?: Record<string, AuthorizationDefinitionFieldMetadata>;

  public constructor(
    public dialogRef: MatDialogRef<ChargingStationsChargingStationSchedulesDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    dialogParams: DialogParamsWithAuth<ChargingStationSchedules, DataResultAuthorizations>
  ) {
    this.dialogData = dialogParams.dialogData;
    this.metadata = dialogParams.authorizations?.metadata;
    console.log('dialogData', this.dialogData);
  }

  public ngAfterViewInit() {
    Utils.registerSaveCloseKeyEvents(
      this.dialogRef,
      this.appRef.formGroup,
      this.appRef.save.bind(this.appRef),
      this.appRef.close.bind(this.appRef)
    );
  }
}
