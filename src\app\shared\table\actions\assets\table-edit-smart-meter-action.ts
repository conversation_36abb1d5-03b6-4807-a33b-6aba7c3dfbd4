
import { ComponentType } from '@angular/cdk/portal';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';

import { Asset, AssetButtonAction } from '../../../../types/Asset';
import { AssetsAuthorizations, DialogParamsWithAuth } from '../../../../types/Authorization';
import { TableActionDef } from '../../../../types/Table';
import { TableEditAction } from '../table-edit-action';

export interface TableEditSmartMeterActionDef extends TableActionDef {
  action: (smartMeterDialogComponent: ComponentType<unknown>, dialog: MatDialog,
    dialogParams: DialogParamsWithAuth<Asset, AssetsAuthorizations>, refresh?: () => Observable<void>) => void;
}

export class TableEditSmartMeterAction extends TableEditAction {
  public getActionDef(): TableEditSmartMeterActionDef {
    return {
      ...super.getActionDef(),
      id: AssetButtonAction.EDIT_SMART_METER,
      action: this.editSmartMeter,
    };
  }

  private editSmartMeter(smartMeterDialogComponent: ComponentType<unknown>, dialog: MatDialog,
    dialogParams: DialogParamsWithAuth<Asset, AssetsAuthorizations>, refresh?: () => Observable<void>) {
    super.edit(smartMeterDialogComponent, dialog, dialogParams, refresh);
  }
}

