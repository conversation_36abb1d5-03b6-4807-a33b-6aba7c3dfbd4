import { Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

import { AssetComponent } from '../asset/asset.component';
import { Asset } from '../../../types/Asset';
import { AssetsAuthorizations, DialogMode, DialogParamsWithAuth } from '../../../types/Authorization';

@Component({
  selector: 'app-smart-meter-dialog',
  template: '<app-asset #appRef [currentAssetID]="assetID" [dialogMode]="dialogMode" [dialogRef]="dialogRef" [assetsAuthorizations]="assetsAuthorizations" [isSmartMeter]="true"></app-asset>',
})
export class SmartMeterDialogComponent {
  @ViewChild('appRef') public appRef!: AssetComponent;
  public assetID: string | undefined;
  public assetsAuthorizations: AssetsAuthorizations;
  public dialogMode: DialogMode;

  public constructor(
    public dialogRef: MatDialogRef<SmartMeterDialogComponent>,
    @Inject(MAT_DIALOG_DATA) dialogParams: DialogParamsWithAuth<Asset, AssetsAuthorizations>) {
    this.assetID = dialogParams?.dialogData?.id as string;
    this.assetsAuthorizations = dialogParams?.authorizations || {} as AssetsAuthorizations;
    this.dialogMode = this.assetID ? DialogMode.EDIT : DialogMode.CREATE;
  }

  public close(saved: boolean) {
    this.dialogRef.close(saved);
  }
}
