import { AfterViewInit, Component, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  AuthorizationDefinitionFieldMetadata,
  DataResultAuthorizations,
  DialogMode,
  DialogParamsWithAuth,
} from 'types/Authorization';

import { Utils } from '../../../../utils/Utils';
import { KYCWalletComponent } from './kyc-wallet.component';

@Component({
  template: `
    <app-kyc-wallet
      #appRef
      [metadata]="metadata"
      [inDialog]="true"
      [dialogMode]="dialogMode"
      [dialogData]="dialogData">
    </app-kyc-wallet>
  `,
})
export class KYCWalletDialogComponent implements AfterViewInit {
  @ViewChild('appRef') public appRef!: KYCWalletComponent;
  public tokenID!: string;
  public dialogData!: any;
  public metadata?: Record<number, AuthorizationDefinitionFieldMetadata>;
  public dialogMode: DialogMode;

  public constructor(
    public dialogRef: MatDialogRef<KYCWalletDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    dialogParams: DialogParamsWithAuth<any, DataResultAuthorizations>
  ) {
    this.dialogData = dialogParams.dialogData;
    this.metadata = dialogParams.authorizations?.metadata;
    this.dialogMode = dialogParams.dialogMode;
  }

  public ngAfterViewInit() {
    Utils.registerSaveCloseKeyEvents(
      this.dialogRef,
      this.appRef.formGroup,
      this.appRef.save.bind(this.appRef),
      this.appRef.close.bind(this.appRef)
    );
  }
}
